<?php
require('yetki.php');
$ekranKod = 60;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8 , width=device-width, initial-scale=1"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>


<style style="text/css">





.tusBosluk2 {
        margin-left :30px ;
        margin-right :30px ;
    }







    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}

    
	/* Define the default color for all the table rows */
	.sortable th{ 
        position:sticky;
        /*position: -webkit-sticky;*/
        top: 0;
        background: green;/*#727272;*/
        color: white; /*#c5c5c5;*/
        z-index: 0;

		/*padding:7px; border: #4e95f4 1px solid;*/
		/*#4e95f4 orjinal hali*/ 
}

	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
        margin-left :10px 
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }




    /** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}


</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">




function geri(gelenSayac){

	var trh2= 'tarih' + gelenSayac;

	var el = document.getElementById(trh2);

	if (el.value=='') return;	

	var tarih = new Date(el.value);

	tarih.setDate(tarih.getDate() -1);

	//console.log(tarih);

	var gun = tarih.getDate();
	//console.log(gun);
	var ay = tarih.getMonth() + 1;
	//console.log(ay);
	var yil = tarih.getFullYear();
	//console.log(yil);

	var yeniTarih = yil + '-'+ sifirEkle(ay) + '-' + sifirEkle(gun); 

	//console.log("Yeni  :" +yeniTarih);

	document.getElementById(trh2).value = yeniTarih; //"2014-02-09"

	//el.setDate(tarih);

	//console.log(yeniTarih);
	//console.log(ay);



}

function sifirEkle(gelen){
	
	var n = gelen.toString().length;	
	//console.log(n);	
	if(n==1) return "0" + gelen; else return gelen;
	
}



function ileri(gelenSayac){


	var trh2= 'tarih' + gelenSayac;

	//console.log(trh2);

	var el = document.getElementById(trh2);

	if (el.value=='') return;	

	var tarih = new Date(el.value);

	tarih.setDate(tarih.getDate() +1);

	//console.log(tarih);

	var gun = tarih.getDate();
	//console.log(gun);
	var ay = tarih.getMonth() + 1;
	//console.log(ay);
	var yil = tarih.getFullYear();
	//console.log(yil);

	var yeniTarih = yil + '-'+ sifirEkle(ay) + '-' + sifirEkle(gun); 

	//console.log("Yeni  :" +yeniTarih);

	document.getElementById(trh2).value = yeniTarih; //"2014-02-09"




}

function sifiraZorla(gelenNumber){
    
    var x = document.getElementById(gelenNumber).value;

    if(x=='') document.getElementById(gelenNumber).value = 0; 

}
















function proceed (gelenSayfa) {
    var form = document.createElement('form');
    form.setAttribute('method', 'post');    
    form.setAttribute('action', gelenSayfa);
    form.style.display = 'hidden';
    document.body.appendChild(form)
    form.submit();
}

function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sayac){
    
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edildi.");
		return false;
    }    

    var kisilerListesi = "";

    var i=1;
        for (i = 1; i <= sayac; i++) {               
                
                if(document.getElementById("kisiler-" + i).checked == true){
                    kisilerListesi+=document.getElementById("kisiler-" + i).value+';';
                }
        } 

    kisilerListesi = kisilerListesi.slice(0, -1);


    //console.log(kisilerListesi);

    if(kisilerListesi==""){
        alert("Görevlendirme için en az bir kişi seçilmelidir!\nİşlem iptal edildi.");
		return false;		
		
	}

    document.getElementById("kisiBilgileri").value = kisilerListesi;
	return true;	


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function searchTableColumns(gelenTablo, gelenAlan,gelenHedefAlan) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      var bulunanKayitAdet = 0; //Kayıt sayısı için
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
            bulunanKayitAdet++;
        } else {
            tr[i].style.display = "none";
        }
      }   

      //Kayıt adedi yazılıyor.

      if(bulunanKayitAdet > 0){
          document.getElementById(gelenHedefAlan).innerHTML = bulunanKayitAdet + " Adet kayıt listelendi"; 
      }else{
          document.getElementById(gelenHedefAlan).innerHTML = ""; 
      }





}




function kolonArama($gelenKolon, $kolonNo) {
  var input, filter, table, tr, td, i, txtValue;
  input = document.getElementById($gelenKolon);//"myInput"
  filter = input.value.toLocaleUpperCase("tr");
  table = document.getElementById("tableMain");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[$kolonNo];
    if (td) {
      txtValue = td.textContent || td.innerText;
      if (txtValue.toLocaleUpperCase("tr").indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	//console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function modalGoster(gelenKod, gelenKriter) {
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
    }
    };
    xhttp.open("POST", "talepHareketVer.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("talepDetaySn="+gelenKod+"&kriter="+gelenKriter);

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
}

function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}





</script>
</head>
<body>

<?php




function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}

function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn='$gorevSn';";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$row[0];	   
   }  

   return $liste;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
}


function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}


function talepHareketVarMi($gelenTalepNo, $gelenTalepDetayNo){

    global $db;

    $sorgu = "SELECT COUNT(*) FROM talep_hareket WHERE talep_sn = $gelenTalepNo AND talep_detay_sn = $gelenTalepDetayNo;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    } 

    $kayitAdedi = 0;

    while($row = pg_fetch_row($ret)){        
        $kayitAdedi = $row[0];	   
    }

    if($kayitAdedi>0) $donenDeger = true; else $donenDeger = false;

    return $donenDeger;
}


function talepHareketteNakitAcikVarMi($gelenTalepNo, $gelenTalepDetayNo){

    global $db;

    $sorgu = "SELECT COALESCE(SUM(nakit_odenen), 0), COALESCE(SUM(acik_odenen), 0) FROM talep_hareket WHERE talep_sn = $gelenTalepNo AND talep_detay_sn = $gelenTalepDetayNo;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    } 

    $kayitAdedi = 0;

    while($row = pg_fetch_row($ret)){  //Nakit veya Açık ödeme var ise?

        if($row[0]>0 || $row[1]>0) $donenDeger = true; else $donenDeger = false;     
           
    }    

    return $donenDeger;
}



function bostaTalepDetayVarMi($gelenTalepSn){

    global $db;    

    $sorgu = "SELECT COUNT(*) FROM talep_detay WHERE iptal=FALSE
                                                AND isleme_alindi = FALSE
                                                AND onay = TRUE
                                                AND tamamlandi = FALSE
                                                AND talep_sn = $gelenTalepSn;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    } 

    $kayitAdedi = 0;

    while($row = pg_fetch_row($ret)){        
        $kayitAdedi = $row[0];	   
    }

    if($kayitAdedi>1) $donenDeger = true; else $donenDeger = false;

    return $donenDeger;
}


function talepTamamlandiMi($gelenTalepSn){

    global $db;    

    $sorgu = "SELECT tamamlandi FROM talep WHERE sn = $gelenTalepSn;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    }     

    while($row = pg_fetch_row($ret)){        
        $donenDeger = $row[0];	   
    }    

    

    return $donenDeger;
}


function gorevYetkililerineEkle($gelenTalepSn){

    global $db; 
    global $kullanici;
    
    $gelenGorevSn = 0;
    $donenDeger = true;
    
    //Önce Görev Numarası bulunuyor.

    $sorguCumle = "SELECT sn FROM gorevlendirme WHERE talep_sn = $gelenTalepSn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        echo "Görev kodu alınamadı!!";
        exit;
    }

    while($row = pg_fetch_row($ret)){        
        $gelenGorevSn = $row[0];	   
    }

    if($gelenGorevSn==0){
        echo "Satınalmaya ait görev numarası bulunamadı!";
        //exit;
    }



    //Talebi üzerine alan kişi de görevi görebilir listesine ekleniyor.

    $sorguCumle="INSERT INTO gorev_alan(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, '$kullanici')";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        echo "Görev Alanlar kaydedilemedi!";
        exit;
    }


    //Talebi oluşturan kişi

    $sorguCumle="INSERT INTO gorev_gorebilen(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, '$kullanici')";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        echo "Görev Alanlar kaydedilemedi!";
        exit;
    }    

    return $donenDeger;
}




function nakitseAkinsoftaYaz($gelenTalepDetaySn){

    global $db; 

    $sorgu = "SELECT talep_hareket.sn, 
                     talep_hareket.nakit_odenen, 
                     nakit_odenen_cari_kod, 
                     talep_hareket.talep_sn, 
                     talep_detay.islem_kullanici, 
                     talep.dogrudan,
                     talep.sube,
                     talep.fatura_durumu /*7*/
                                        FROM talep_hareket 
                                        JOIN talep_detay ON talep_detay.sn = talep_hareket.talep_detay_sn
                                        JOIN talep ON talep.sn = talep_hareket.talep_sn 
                                        WHERE talep_hareket.nakit_odenen>0 
                                        AND talep_detay_sn = $gelenTalepDetaySn;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    }     

    while($row = pg_fetch_row($ret)){  
        talepHarekettenAkinsoftdaOdemeYap($row[5],  /*Doğrudan Mı?*/
                                          $row[4],  /*Talebi üzerine alan kullanıcı*/
                                          $row[3],  /*Tale No*/
                                          $row[2],  /*Ödeme Yapılan Cari Kod*/
                                          $row[1],  /*Nakit Ödenen Tutar*/
                                          $row[0],  /*Talep Hareket No*/
                                          subeKoduVer($row[6]),  /*Talep Şubesi*/
                                          $row[7]  /*Fatura Durumu*/
                                        );
        $donenDeger = $row[0];	   
    }    

}




function talepUzerineAlmaLogYaz($gelenTalepSn, $gelenTalepDetaySn, $gelenKullanici, $gelenEylemTip){
	
	global $db;
	
	$sorgu = "INSERT INTO talep_uzerine_alma_hareket(talep_sn, talep_detay_sn, kullanici, islem_tip)
					VALUES($gelenTalepSn, $gelenTalepDetaySn, '$gelenKullanici', '$gelenEylemTip');";
					
					
	$ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo "Hata 02: " . pg_last_error($db). "\n$sorgu";
       echo "Tabloya ulaşılamadı!";
       exit();
    }
	
	
}



//Doğrudan harcamad da kullanılacak.
//O yüzden include yapmak daha mantıklı.

//require_once("TalepHarekettenAkinsoftdaOdemeYap.php");






//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */





//Bağlantı Yapılıyor... 
//require_once("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");
require("sqlMetinDuzenle.php");
require("dosyalariYukle.php");
require("AkinsoftIslemleri.php");
require("BelgesiVarMi.php");




$gelenTalepDetaySn = $_POST['talepDetaySn'];
$gelenTalepSn = $_POST['talepSn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];

//Harekey kaydının silinmesi için
$gelenTalepHareketSn = $_POST['talepHareketSn'];
$gelenTarih1 = $_POST['tarih1'];
$gelenTarih2 = $_POST['tarih2'];



require("saat1.inc");



//echo $gelenKriter;



if($gelenTarih1=="") $gelenTarih1 = date("Y-")."01-01";


if($gelenTarih2=="") $gelenTarih2 = date("Y-m-d"); //"2021-12-31";






if($gelenKriter=="") $gelenKriter = "8";


if($gelenKriter == "3"){
    echo '<br><h1 style="text-align:center;">Onaylanmış Talepler</h1>';

}elseif($gelenKriter == "1") {
    echo '<br><h1 style="text-align:center;">Tüm Talepler</h1>';

}
elseif($gelenKriter == "2") {
    echo '<br><h1 style="text-align:center;">Onay Bekleyen Talepler</h1>';

}elseif($gelenKriter == "4") {
    echo '<br><h1 style="text-align:center;">İptal Edilmiş Talepler</h1>';

}elseif($gelenKriter == "5") {
    echo '<br><h1 style="text-align:center;">Devam Eden Tüm Satınalmalar</h1>';

}elseif($gelenKriter == "6") {
    echo '<br><h1 style="text-align:center;">Henüz İşleme Alınmamış Satınalmalar</h1>';

}elseif($gelenKriter == "7") {
    echo '<br><h1 style="text-align:center;">Tamamlanmış Satınalmalar</h1>';

}elseif($gelenKriter == "8") {
    echo '<br><h1 style="text-align:center;">Bana Ait Devam Eden Satınalmalar</h1>';

}





if($gelenTalepDetaySn=="")$gelenTalepDetaySn=0;


//Burada dosya yükleme işlemleri yapılıyor.

//Dosya geliyorsa kaydedecek.
//count($_FILES['fileToUpload']['name'])



if(count($_FILES)>0){
	dosyalariYukle("/mnt/talepBelgeler/".$gelenTalepSn."/".$gelenTalepDetaySn."/", "Talep Detay "); 
	
}



//Talep iptalinin geri alınması

if($gelenIslemTip == "İptali Geri Al" && $gelenTalepDetaySn<>"" ){

    //echo "<br><br><br><br><br>xxxxxx";

    $sorguCumle = "UPDATE talep_detay SET iptal = FALSE, 
                                          iptal_kullanici = '',
                                          onay = FALSE, 
                                          onay_kullanici = '',
                                          onay_zaman = NULL 
                                          WHERE sn=$gelenTalepDetaySn;";

    $ret = pg_query($db, $sorguCumle);

    if($ret){
        logYaz($gelenTalepSn . " Numaralı satınalma detayı ". $kullanici . " tarafından iptal işlemi geri alındı.");        
        echo "<script> alert('İptal işlemi geri alındı.'); </script>";
    }else{
        echo pg_last_error($db);
        echo $sorguCumle;
        exit;        
    }  

}







//Toplu onay yapılıyor.

if($gelenIslemTip == "Talep Toplu Onay" && $gelenTalepSn<>"" ){

    $sorguCumle = "SELECT sn FROM talep_detay WHERE iptal=FALSE
                                                AND isleme_alindi = FALSE
                                                AND onay = TRUE
                                                AND tamamlandi = FALSE
                                                AND talep_sn = $gelenTalepSn;";

    $inDegerleri = "";
	$inDegerleriArray = [];

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 


    while($row = pg_fetch_row($ret)){
        $inDegerleri = $inDegerleri . $row[0] . ",";
		
		array_push($inDegerleriArray, $row[0]);
        
    }

    $inDegerleri = substr($inDegerleri, 0, -1);


    $sorguCumle = "UPDATE talep_detay SET isleme_alindi = TRUE, islem_zaman=NOW(), islem_kullanici='$kullanici' 
                           WHERE sn IN($inDegerleri);"; 

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit();
    } else{
		
		foreach($inDegerleriArray as $detayNolar){
			
			talepUzerineAlmaLogYaz($gelenTalepSn, $detayNolar, $kullanici, "Üzerine Alma");		
			
		}	
		
		
        logYaz($inDegerleri . " Numaralı satınalma detayları ". $kullanici . " tarafından topluca işleme alındı.");        
        echo "<script> alert('Toplu onay yapıldı.'); </script>";
    }


}

//****************************




echo '<table border ="0">';

echo "<tr>";
echo "<td>";
echo '<form action="#" method="post">';
echo 'Kriter:';
echo "</td>";
echo "<td colspan='2'>";



echo '<select name="kriter">';


//Burada Tamer'in onaylanmamış siparişleri görmesi engellendi.
// 2023 03 04

//Sonradan kullanıcı kontrolü konarak iptal edildi. 2023 03 10

//if($kullanici!="tsecilgin"){	
	echo '<option value="1" ' .selectedKoy($gelenKriter, "1"). '>Tüm Talepler</option>';
	echo '<option value="2" ' .selectedKoy($gelenKriter, "2"). '>Onay Bekleyen Talepler</option>';
//}

echo '<option value="3" ' .selectedKoy($gelenKriter, "3"). '>Onaylanmış Talepler</option>';
echo '<option value="5" ' .selectedKoy($gelenKriter, "5"). '>Devam Eden Tüm Satınalmalar</option>';
echo '<option value="4" ' .selectedKoy($gelenKriter, "4"). '>İptal Edilmiş Talepler</option>';
echo '<option value="6" ' .selectedKoy($gelenKriter, "6"). '>Henüz İşleme Alınmamış Satınalmalar</option>';
echo '<option value="7" ' .selectedKoy($gelenKriter, "7"). '>Tamamlanmış Satınalmalar</option>';
echo '<option value="8" ' .selectedKoy($gelenKriter, "8"). '>Bana Ait Devam Eden Satınalmalar</option>';

echo "</select>";

echo "</td>";

echo "</tr>";


echo '<tr>';
echo '<td>Talep Tarihi:</td>';
echo '<td>';
echo '<input type="date" id="tarih1" name="tarih1" class= "tusBosluk2" value="'.$gelenTarih1.'" title="Bu tarih ve sonrası!"/>';
echo '</td>';
echo '<td>';
echo '<input type="date" id="tarih2" name="tarih2" class= "tusBosluk2" value="'.$gelenTarih2.'" title="Bu tarihe kadar!"/>';
echo '</td>';


echo "</tr>";

echo '<tr>';
echo '<td>';
echo '</td>';
echo '<td>';
echo '<button type="button" class= "tusBosluk2" onclick="geri(1)">Geri</button>';
echo '<button type="button" class= "tusBosluk2" onclick="ileri(1)">İleri</button>';
echo '</td>';
echo '<td >';
echo '<button type="button" class= "tusBosluk2" onclick="geri(2)">Geri</button>';
echo '<button type="button" class= "tusBosluk2" onclick="ileri(2)">İleri</button>';
echo '</td>';




echo '</tr>';




echo '<tr>';
echo '<td colspan="3">';






echo '<input type="submit" value="Sorgula">';
echo '</form>';
echo '</td>';
echo '</tr>';

echo "</table>";





//****************************



/*

echo '<form action="#" method="post">';
echo '<br>Kriter: ';
echo '<select name="kriter"  onchange="this.form.submit()">';


//Burada Tamer'in onaylanmamış siparişleri görmesi engellendi.
// 2023 03 04

//Sonradan kullanıcı kontrolü konarak iptal edildi. 2023 03 10

//if($kullanici!="tsecilgin"){	
	echo '<option value="1" ' .selectedKoy($gelenKriter, "1"). '>Tüm Talepler</option>';
	echo '<option value="2" ' .selectedKoy($gelenKriter, "2"). '>Onay Bekleyen Talepler</option>';
//}

echo '<option value="3" ' .selectedKoy($gelenKriter, "3"). '>Onaylanmış Talepler</option>';
echo '<option value="5" ' .selectedKoy($gelenKriter, "5"). '>Devam Eden Tüm Satınalmalar</option>';
echo '<option value="4" ' .selectedKoy($gelenKriter, "4"). '>İptal Edilmiş Talepler</option>';
echo '<option value="6" ' .selectedKoy($gelenKriter, "6"). '>Henüz İşleme Alınmamış Satınalmalar</option>';
echo '<option value="7" ' .selectedKoy($gelenKriter, "7"). '>Tamamlanmış Satınalmalar</option>';
echo '<option value="8" ' .selectedKoy($gelenKriter, "8"). '>Bana Ait Devam Eden Satınalmalar</option>';

echo "</select>";
echo "<br><br>";


echo '<input type="submit" value="Sorgula">';
echo '</form>';
*/
echo "<br><br>";





//Burada kullanıcılara bilgilendirme yapılacak.
$gidenKimden="<EMAIL>";
$gidenAdresler = [];
$gidenTelefonlar = [];
$mesaj = $gelenGorevSn. " Numaralı Görev için TAMAMLANDI TALEP veya TAMAMLANDI değişikliği yapıldı.\nLütfen Portaldan kontrol eder misiniz?";
$kisiler =[];



//echo $gelenIslemTip;

//Burada hareket kaydı siliniyor.
if($gelenTalepHareketSn<>"" && $gelenIslemTip== "HareketSil"){

    $sorguCumle = "DELETE FROM talep_hareket WHERE sn= $gelenTalepHareketSn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    logYaz($gelenTalepHareketSn . " Numaralı talep hareket kaydı silindi.");


}


if($gelenTalepDetaySn <> "" && $gelenIslemTip<>""){

    
    switch ($gelenIslemTip) {
        case "islemOnay":
            $sorguCumle = "UPDATE talep_detay SET isleme_alindi = TRUE, islem_zaman=NOW(), islem_kullanici='$kullanici' 
                           WHERE sn=$gelenTalepDetaySn;"; 

            logYaz($gelenTalepDetaySn . " Numaralı satınalma detayı ". $kullanici . " tarafından işleme alındı.");

            if(gorevYetkililerineEkle($gelenTalepSn)==false){
                echo "Görev Yetkililerine Eklenmedi!";
            }
			
			talepUzerineAlmaLogYaz($gelenTalepSn, $gelenTalepDetaySn, $kullanici, "Üzerine Alma");		

            //$gidenBilgi = $kullanici . " Görev kapatma talebi oluşturdu!";

            //Mesaj gidecek kişiler ve görevi oluşturan
            //$kisiler = gorevlendirilenleriArrayVer($gelenGorevSn);
            //Görevi veren de ekleniyor.
            //array_push($kisiler, gorevVereniVer($gelenGorevSn));

            
            break;
        case "tamamlandiOnay":
            $sorguCumle = "UPDATE talep_detay SET tamamlandi = TRUE, tamamlandi_zaman=NOW() 
                            WHERE sn=$gelenTalepDetaySn;"; 

            logYaz($gelenTalepDetaySn . " Numaralı satınalma detay TAMAMLANDI.");


            //Talep detay hareketine bakıp NAKİT olanlar için Akınsoft Ödemesi, yapılacak.

            //nakitseAkinsoftaYaz($gelenTalepDetaySn);


            //$gidenBilgi = $kullanici . " Görev kapatma talebi oluşturdu!";

            //Mesaj gidecek kişiler ve görevi oluşturan
            //$kisiler = gorevlendirilenleriArrayVer($gelenGorevSn);
            //Görevi veren de ekleniyor.
            //array_push($kisiler, gorevVereniVer($gelenGorevSn));            
            break;
        case "islemIptal":
            $sorguCumle = "UPDATE talep_detay SET isleme_alindi = FALSE, islem_zaman=NULL, islem_kullanici='' 
                           WHERE sn=$gelenTalepDetaySn;"; 
						   
						   
		   talepUzerineAlmaLogYaz($gelenTalepSn, $gelenTalepDetaySn, $kullanici, "Üzerine Alma İptali");
             


			 
            logYaz($gelenTalepDetaySn . " Numaralı satınalma İŞLEM İPTALİ uygulandı.");

            

            break; 
        case "tamamlandiIptal":
            $sorguCumle = "UPDATE talep_detay SET tamamlandi = FALSE, tamamlandi_zaman=NULL 
                            WHERE sn=$gelenTalepDetaySn;";
                            
                            
            logYaz($gelenTalepDetaySn . " Numaralı satınalma TAMAMLANDI İPTALİ uygulandı.");


            break;
        case "satirIptal":
            $sorguCumle = "UPDATE talep_detay SET iptal = TRUE 
                            WHERE sn=$gelenTalepDetaySn;"; 
                            
            logYaz($kullanici . " Kullanıcısı, " . $gelenTalepDetaySn . " Numaralı satınalma detayını iptal etti.");

            //Burada mutlaka Edip beye KMS gönderelim.
            //kisaMesajGonder("**** $kullanici  Kullanıcısı,  $gelenTalepDetaySn  Numaralı satınalma detayını İPTAL etti!!!", ['5324155502'], 1);

        break;
        } 


    //Sorgu çalıştır.
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

}

/*    

    foreach ($kisiler as $kisi){
        array_push($gidenAdresler, kullanicidanBilgiVer('eposta', $kisi));

        $mesajX = $mesaj ."\n". paslamaKoduVer($kisi, 'GorevTamamlama.php', '', '');


        //Bu kısım KMS göndermek için.
        $sallaTel = []; 
        array_push($sallaTel, kullanicidanBilgiVer('cep_tel', $kisi) );
        //kisaMesajGonder($mesajX, $sallaTel, 1);
        unset($sallaTel);
        
    }

    

    if(count($gidenAdresler)>0) epostaGonder($gidenKimden, $gidenAdresler, $gidenBilgi, $mesaj);
    //if(count($gidenTelefonlar)>0) kisaMesajGonder($mesaj, $gidenTelefonlar, 1);






                    
}


*/

//  if($gelenGorevSn==""||$gelenIslemTip=="") exit;

//Gelen Değerler alınıyor...
/*
$gelenKullanici = $_POST['kullanici'];

$gelenTip = $_POST['tip'];
$gelenKriterTum = $_POST['kriterTum'];

$takas = explode(" - ", $gelenKullanici);

$gelenKullaniciKod = trim($takas[1]);

*/

//$aktifKullanici = $_SESSION["user"];


/*
CREATE TABLE public.gorevlendirme (
    sn integer NOT NULL,
    gorev_veren text NOT NULL,
    gorev_konu text NOT NULL,
    baslama_zaman timestamp without time zone DEFAULT now() NOT NULL,
    hedef_zaman timestamp without time zone DEFAULT now() NOT NULL,
    tamamlandi boolean DEFAULT false NOT NULL,
    bitis_zaman timestamp without time zone,
    uzatma_zaman timestamp without time zone,
    gorev_detay text DEFAULT ''::text,
    yaratma_zaman timestamp without time zone DEFAULT now() NOT NULL,
    duzeltme_zaman timestamp without time zone DEFAULT now() NOT NULL
);


if($_SESSION["root"] == 1){
    $sorguCumle = "SELECT gorev_veren, gorev_konu, baslama_zaman, uzatma_zaman, bitis_zaman, tamamlandi, gorevlendirme.sn, gizli, gorev_detay, hedef_zaman FROM gorevlendirme                     
                    ORDER BY hedef_zaman;";
}else{
    $sorguCumle = "SELECT gorev_veren, gorev_konu, baslama_zaman, uzatma_zaman, bitis_zaman, tamamlandi, sn, gizli, gorev_detay, hedef_zaman 
                    FROM gorevlendirme                    
                    ORDER BY baslama_zaman DESC;";

}
 */

$alanlar = "SELECT talep_detay.sn, 
                    talep_sn, 
                    talep.konu, 
                    talep.detay, 
                    aciklama, 
                    miktar, 
                    birim, 
                    termin_zaman, 
                    onay, 
                    onay_zaman, 
                    isleme_alindi, 
                    islem_zaman, 
                    talep_detay.tamamlandi, /*12*/
                    tamamlandi_zaman, 
                    talep.tamamlandi,
                    talep_detay.kullanici,
                    talep_detay.iptal, 
                    islem_kullanici,
                    talep_detay.kullanici,
                    talep_detay.onay_kullanici,
                    talep.gorev_sn,
                    talep.sube,
					talep.talep_eden, /*22*/
					talep.kullanici,
					talep_detay.teslim_kabul,
					talep_detay.islem_kullanici "; //25

switch ($gelenKriter) {
    case "1": //Tamamı
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn 
								   WHERE talep_detay.iptal = FALSE
								   AND DATE(talep_detay.yaratma_zaman)>='$gelenTarih1'
								   AND DATE(talep_detay.yaratma_zaman)<='$gelenTarih2'								   
								   ORDER BY talep_detay.yaratma_zaman, talep_detay.sn DESC;";
      break;
    case "2": //Onay Bekleyenler

        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                        WHERE talep_detay.onay = FALSE 
                        AND talep_detay.iptal = FALSE
						AND DATE(talep_detay.yaratma_zaman)>='$gelenTarih1'
						AND DATE(talep_detay.yaratma_zaman)<='$gelenTarih2'						
                        ORDER BY talep_detay.yaratma_zaman DESC, talep_detay.sn DESC;";              
      break;
    case "3": //Onaylanmış
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                        WHERE talep_detay.onay = TRUE
                        AND talep_detay.iptal = FALSE
                        AND talep_detay.tamamlandi = FALSE
						AND DATE(talep_detay.yaratma_zaman)>='$gelenTarih1'
						AND DATE(talep_detay.yaratma_zaman)<='$gelenTarih2'							
                        ORDER BY talep_detay.yaratma_zaman DESC, talep_detay.sn DESC;"; 

        $topluOnayaIzinVer = 1;
      break;
    case "4": //İptal edilmiş
    $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                    WHERE talep_detay.iptal = true
					AND DATE(talep_detay.yaratma_zaman)>='$gelenTarih1'
					AND DATE(talep_detay.yaratma_zaman)<='$gelenTarih2'					
                    ORDER BY talep_detay.yaratma_zaman DESC, talep_detay.sn DESC;"; 


        $iptalEdilmisGorunsun = 1;

    break;     
    case "5": //Devam Eden Satınalmalar
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                            WHERE talep_detay.onay = TRUE 
                            AND talep_detay.iptal = FALSE
                            AND talep_detay.isleme_alindi = TRUE 
                            AND talep_detay.tamamlandi=FALSE
							AND DATE(talep_detay.yaratma_zaman)>='$gelenTarih1'
							AND DATE(talep_detay.yaratma_zaman)<='$gelenTarih2'	
                            ORDER BY talep_detay.yaratma_zaman DESC, talep_detay.sn DESC;"; 
    break;     
    case "6": //İşleme alınmamış talepler
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                            WHERE talep_detay.onay = TRUE 
                            AND talep_detay.iptal = FALSE
                            AND talep_detay.isleme_alindi = FALSE 
							AND DATE(talep_detay.yaratma_zaman)>='$gelenTarih1'
							AND DATE(talep_detay.yaratma_zaman)<='$gelenTarih2'	
                            ORDER BY talep_detay.yaratma_zaman DESC, talep_detay.sn DESC;"; 

        $topluOnayaIzinVer = 1;

    break; 
    case "7": //Tamamlanmış satınalmalar
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                            WHERE talep_detay.onay = TRUE 
                            AND talep_detay.iptal = FALSE
                            AND talep_detay.isleme_alindi = TRUE 
                            AND talep_detay.tamamlandi = TRUE
							AND DATE(talep_detay.yaratma_zaman)>='$gelenTarih1'
							AND DATE(talep_detay.yaratma_zaman)<='$gelenTarih2'	
                            ORDER BY talep_detay.yaratma_zaman DESC, talep_detay.sn DESC;"; 
    break; 
    case "8": //Devam Eden Satınalmalar
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                            WHERE talep_detay.onay = TRUE 
                            AND talep_detay.iptal = FALSE
                            AND talep_detay.isleme_alindi = TRUE 
                            AND talep_detay.tamamlandi=FALSE
                            AND islem_kullanici = '$kullanici'
							AND DATE(talep_detay.yaratma_zaman)>='$gelenTarih1'
							AND DATE(talep_detay.yaratma_zaman)<='$gelenTarih2'	
                            ORDER BY talep_detay.yaratma_zaman DESC, talep_detay.sn DESC;"; 
    break;  
  } 
//echo $sorguCumle;

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
} 


echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" oninput="searchTableColumns(\'tableMain\',\'myInputHepsi\',\'bulunanKayitAdet\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">&nbsp;&nbsp;<label id="bulunanKayitAdet"></label>';


echo "<br><br>";  


echo '<table class= "sortable" valign="middle" id="tableMain">';
echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
echo '<th style="text-align:center;cursor:pointer;">Detay S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Satınalma S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Konu</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Açıklama</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep<br>Miktar</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep<br>Birim</th>';
echo '<th style="text-align:center;cursor:pointer;">Termin Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">İlgili<br>Şube</th>';
echo '<th style="text-align:center;cursor:pointer;">Oluşturan</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep<br>Eden</th>';
echo '<th style="text-align:center;cursor:pointer;">Onaylayan</th>';
echo '<th style="text-align:center;cursor:pointer;">Onay<br>Zaman</th>';
//echo '<th style="text-align:center;cursor:pointer;">İşleme Alındı</th>';
echo '<th style="text-align:center;cursor:pointer;">Satınalmacı</th>';
echo '<th style="text-align:center;cursor:pointer;">İşlem Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">Tamamlandı</th>';
echo '<th style="text-align:center;cursor:pointer;">Tamamlanma Zaman</th>';
echo "<th style=\"text-align:center\">İşlem</th>";
echo "</tr>";

//$sayac = 1;

while($row = pg_fetch_row($ret)){
	
	//Sadece talep sahibi ve kaydı açan görebiliyor.
	if( ($gelenKriter=="1" || $gelenKriter == "2") && $_SESSION["root"] == 0 ){
		
		if($row[22]!=$kullanici && $row[23]!=$kullanici ){
			continue;
		}
		
	}

    //Gizli görev ve kullanıcı listede yoksa ve root değilse pas geçiliyor.

    //$goreviGorebilir = goreviGorebilir($row[6], $kullanici, $row[0]);

    //Sadece görevi alanlar
    //$sadeceGorevli = sadeceGorevli($row[6], $kullanici);
    /*
    if($_SESSION["root"] == 0){
        
        if( $row[7]=='t' && $goreviGorebilir == false ) continue;
    }
    */
//$gelenKriter

    echo "<tr class='item' title='". $row[3] ."'>";
    echo "<td onclick='modalGoster(\"$row[1]-$row[0]\",$gelenKriter)' style='text-align:center'>". $row[0] . "</td>"; //S/N       
    echo "<td onclick='modalGoster(\"$row[1]-$row[0]\",$gelenKriter)' style='text-align:center'>". $row[1] . "</td>"; //S/N       
    echo "<td onclick='modalGoster(\"$row[1]-$row[0]\",$gelenKriter)'>". $row[2] . "</td>"; //Konu
    echo "<td onclick='modalGoster(\"$row[1]-$row[0]\",$gelenKriter)'>". $row[4] . "</td>"; //Açıklama
	
	
	// Kişi üzerine almadan adetleri göremeyecek. 2023 09 18
	
	if($row[25]==$kullanici){
		echo "<td style='text-align:center'>". $row[5] . "</td>"; //Miktar		
	}else{
		echo "<td style='text-align:center'><font style='color:red;'>Üzerinize<br>Almadan<br>Göremezsiniz!</font></td>"; //Miktar
		
	}
	
	
    
    echo "<td style='text-align:center'>". $row[6] . "</td>"; //Birim
    echo "<td sorttable_customkey='". tarihFormatla($row[7]) ."' style='text-align:center'>". tarihYaz($row[7]) . "</td>"; //TerminZaman
    echo "<td style='text-align:center'>". $row[21] . "</td>"; //Şube
    echo "<td style='text-align:center'>". $row[18] . "</td>"; //oluşturan
	echo "<td style='text-align:center'>". $row[22] . "</td>"; //Talep Eden

    echo "<td style='text-align:center'>". $row[19] . "</td>"; //Onay
    echo "<td sorttable_customkey='". tarihFormatla($row[9]) ."' style='text-align:center'>". tarihSaatYaz($row[9]) . "</td>"; //Onay Zaman
    //echo "<td style='text-align:center'>". trueFalse($row[10],'İşlemde') . "</td>"; //İşleme Alındı
    echo "<td style='text-align:center'>". $row[17] . "</td>"; //Satınalmacı
	

    echo "<td sorttable_customkey='". tarihFormatla($row[11]) ."' style='text-align:center'>". tarihSaatYaz($row[11]) . "</td>"; //İşlem Zaman

    echo "<td style='text-align:center'>". trueFalse($row[12],'Tamamlandı') . "</td>"; //Tamamlandı
    echo "<td sorttable_customkey='". tarihFormatla($row[13]) ."' style='text-align:center'>". tarihSaatYaz($row[13]) . "</td>"; //Tamamlanma Zaman
       


    //Detay, silme ve Hikaye Tuşları
    echo "<td style='vertical-align: middle; text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";


//    if($kullanici == $row[8]){

    

    if($row[8]=='t' && $duzeltYetki =  't'){ //Satınalma onaylanmış ve kullanıcının da DÜZELTME yekisi verilmiş ise?





        if(($row[10]=='f' || $row[12]=='f') && $row[16]=='f' && $row[10]=='f' ){

            

            //Tamamlanma islem Onayla?
            echo '<form action="#"  method="post"  class="inline">'; 
            echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">';            
            echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';            
                
                    echo '<input type="hidden" name="islemTip" value="islemOnay">';
                    $simgeAdi = "GorevAlmaSimge.png";                

            
            echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';   
            echo '<button type="submit"><img src="'.$simgeAdi.'" height="20" width="20" title="Onayla==>'.$row[0].'"/></button>';    
            echo '</form>';









        }

        //************************************************************************* */



        if(($row[10]=='f' || $row[12]=='f') && $row[16]=='f' &&$row[12]=='f'  && $row[17]==$kullanici && talepHareketVarMi($row[1], $row[0]) == true ){

            //Tamamlanma islem Onayla?
            echo '<form action="#"  method="post"  class="inline">'; 
            echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">';            
                
            echo '<input type="hidden" name="islemTip" value="tamamlandiOnay">';
            $simgeAdi = "confirm.png";               

            
            echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';   

            if(talepHareketteNakitAcikVarMi($row[1], $row[0]) == true){

                echo '<button type="submit"><img src="'.$simgeAdi.'" height="20" width="20" title="Muhasebeye Gönder==>'.$row[0].' Detay Kapatma durumuna geçer!"/></button>';    

            }else {
                echo '<button type="submit" ><img src="'.$simgeAdi.'" height="20" width="20" title="Muhasebeye Gönder==>'.$row[0].'"/></button>';    
                
            }
            
            echo '</form>';
        }



        if( ($row[10]=='t' || $row[12]=='t') && $row[14]=='f' && $row[16]=='f' && $row[17]==$kullanici){ 
            
            
            if(talepHareketVarMi($row[1], $row[0]) == false){

                //Tamamlanma islem Onay İptal?
                echo '<form action="#"  method="post"  class="inline"  onclick="return ConfirmIslem(\'Üzerine alınmış Satınalma Geri bırakılacak ve\nYönetici bilgisine gönderilecektir!\');">'; 
                echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 
				echo '<input type="hidden" name="talepSn" value="'.$row[1].'">'; 
    
                    //if($row[12]=='t') {
                    //    echo '<input type="hidden" name="islemTip" value="tamamlandiIptal">';
                    //    $tooltipMesaj = "Tamamlandı İptal";
                    //}elseif($row[10]=='t'){
                        echo '<input type="hidden" name="islemTip" value="islemIptal">';
                        $tooltipMesaj = "İşlemi üzerinden bırakma!";
                    //}
                
                echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';        
                echo '<button type="submit"><img src="cancel.png" height="20" width="20" title="'.$tooltipMesaj.'==>'.$row[0].'"/></button>';    
                echo '</form>';
            }
            
            





            if( $row[12]=='f') { //Henüz tamamlanmadıysa
				
				
				if($row[24]=="t"){// Teslim kabul yapılmış ise
					
					//***************************** */
					//Her ödeme için ayrı hareket girişi girilebiliyor.
					//ödeme hareketi girişi
					echo '<form action="talepHareketGiris.php"  method="post"  class="inline">'; 
					echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';
					echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">';
					echo '<input type="hidden" name="talepKonu" value="'.$row[2].'">';
					echo '<input type="hidden" name="talepAciklama" value="'.sqlMetinDuzenle($row[4]).'">';
					echo '<input type="hidden" name="islemTip" value="Yeni Kayıt">';            
					echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">'; 
					echo '<input type="hidden" name="kaynak" value="talepDetayListesi.php">';    
					echo '<button type="submit"><img src="OdemeGiris.png" height="20" width="20" title="Yeni Ödeme Hareket Girişi==>'.$row[0].'"/></button>';   //confirm 
					echo '</form>';
				}else{
					echo "<font style='color:red;'><br>Kabul bekleniyor!</font>";
				}
				
				


            }








        }

        //if($row[17]==$kullanici){

            //Hikayeye Git
            echo '<form action="GorevHikayesiListesi.php"  method="post" target="_blank"  class="inline">'; 
            echo '<input type="hidden" name="gorevSn" value="'.$row[20].'">';    
            echo '<input type="hidden" name="gonderenSayfa" value="talepDetayListesi.php">';    
            echo '<button type="submit"><img src="info.svg" height="20" width="20" title="Hikayeye Git==>'.$row[20].'"/></button>';
            //echo '<button type="submit">Detay</button>';
            echo '</form>';
        //}




        if( $topluOnayaIzinVer==1 && bostaTalepDetayVarMi($row[1])==true){

            //Tüm detayları onayla
            echo '<form action="#"  method="post"  class="inline" onclick="return ConfirmIslem(\''.$row[1].' Numaralı Talep içindeki, henüz atanmamış tüm detaylar size atanacaktır.?!\');">';             
            echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';            
            echo '<input type="hidden" name="islemTip" value="Talep Toplu Onay">';            
            echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';                         
            echo '<button type="submit"><img src="confirm_all.png" height="20" width="20" title="Talep Toplu Onayla==>'.$row[1].'"/></button>';   //confirm 
            echo '</form>';

       }


        

        











    }

//}



    //Belgesi varsa geliyor.

    if(belgesiVarMi("talepBelgeler", $row[1], $row[0])==true){
        
        //Belgeleri Göster
        echo '<form action="TalepBelgeleri.php"  target="_blank"  method="post"  class="inline">'; 
        echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';    
        echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 
        echo '<input type="hidden" name="tamamlandiMi" value="'.$row[12].'">'; 
        echo '<input type="hidden" name="kaynak" value="talepDetayListesi.php">';  
        echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$row[0].'"/></button>';
        //echo '<button type="submit">Detay</button>';
        echo '</form>';
    }

    //Dosya ekleme******************

    if($row[17]==$kullanici){

        echo '<form action="#"  method="post" enctype="multipart/form-data" class="inline">';
        echo '<input type="hidden" name="talepSn" value="'.$row[1].'">'; 
        echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">';    
        echo '<input type="hidden" name="dosyaYukle" value="1">';    
        echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';    
        echo '<input type="file" name="fileToUpload[]" id="fileToUpload" multiple>';
        echo '<button type="submit" value="Upload Image" name="submit"><img src="upload-file.svg" height="20" width="20" title="Belge Ekle==>'.$row[0].'" valign="middle"/></button>'; //
        //echo '<button type="submit">Hikaye</button>';
        echo '</form>';
    }


    if($iptalEdilmisGorunsun==1 && $row[15]==$kullanici && talepTamamlandiMi($row[1])=='f'){

        //Tüm detayları onayla
        echo '<form action="#"  method="post"  class="inline" onclick="return ConfirmIslem(\''.$row[0].' Numaralı Talep detayı iptali geri alınacak?!\');">';             
        echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">';            
        echo '<input type="hidden" name="islemTip" value="İptali Geri Al">';            
        echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';                         
        echo '<button type="submit"><img src="iptaliGeriAl.ico" height="20" width="20" title="İptali Geri Al==>'.$row[0].'"/></button>';   //confirm 
        echo '</form>';


        //Talep Detay düzenle
        echo '<form action="talepDetayGiris.php" method="post" target="_blank" class="inline">';
        echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">';            
        echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';            
        echo '<input type="hidden" name="aciklama" value="'.$row[4].'">';
        echo '<input type="hidden" name="miktar" value="'.$row[5].'">';
        echo '<input type="hidden" name="birim" value="'.$row[6].'">';
        echo '<input type="hidden" name="terminTarih" value="'.$row[7].'">';
        //echo '<input type="hidden" name="islemTip" value="Kaydet">';            
        echo '<input type="hidden" name="kayitMod" value="Düzenle">';            
        echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';                                    
        echo '<button type="submit"><img src="EditIcon.png" height="20" width="20" title="Talep Detay Düzenle==>'.$row[0].'" valign="middle"/></button>';
        //echo '<button type="submit">Hikaye</button>';
        echo '</form>';

    }





    //Burada satır iptal ediliyor.
    //Kullanıcı, kaydı açan veya root ilise
    //ve satınalma işlemi henüz bitmemiş ise.

    if(( $kullanici == $row[18] && $row[25]=="" ) || $_SESSION["root"]==1 ){// Sadece kendi açtığı kalemleri iptal edebilir.

        if((($kullanici == $row[15] || $_SESSION["root"]==1) && $row[16]=='f') && $row[12]=='f' ){
            echo '<form action="#"  method="post"  class="inline" onclick="return ConfirmIslem(\'Satınalma iptal edilecek!\');">';         
            echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 
            echo '<input type="hidden" name="islemTip" value="satirIptal">';
            echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';    
            //echo '<input type="hidden" name="kaynak" value="talepListesi.php">';  
            echo '<button type="submit"><img src="geriAl.png" height="20" width="20" title="Satır İptal==>'.$row[0].'"/></button>';
            //satir-iptal
            //echo '<button type="submit">Detay</button>';
            echo '</form>';

        }
    }else{
		
		echo '<button><img src="geriAl.png" height="20" width="20" title="Satır İptal==>'.$row[0].'" onclick="alert(\'Üzerinde alınmış siparişler iptal edilemez!\nÖncelikle satınalmacı üzerinden bırakmaldır!\');"></button>';
		
	}



    if($row[25]==$kullanici){
		//Yazdır
		echo '<form action="TalepYazdir.php" target="_blank"  method="post"  class="inline">'; 
		echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';        
		echo '<button type="submit"><img src="Print.jpg" height="20" width="20" title="Pdf Yap==>'.$row[1].'"/></button>';
		echo '</form>';

	}

	echo "</div>";
	echo "</td>";
    
    //Detay ve Hikaye Tuşları sonu  

   


  echo "</tr>";
    //$sayac+=1;
}

echo "</table>";



 //Modal
echo '<div id="myModal" class="modal">

 <!-- Modal content -->
 <div class="modal-content">
   <div class="modal-header" >
     <span class="close" onclick="spanKapat()">&times;</span>
     <h2>Satınalma Hareketleri</h2>
   </div>
   <div class="modal-body" id="icerik">    

   </div>
   <div class="modal-footer">
     <h3></h3>
   </div>
 </div>

</div>';




pg_close($db);

echo "<br><br><br>";
require("saat2.inc");
echo "<br><br><br>";


?>

</body>
</html>						