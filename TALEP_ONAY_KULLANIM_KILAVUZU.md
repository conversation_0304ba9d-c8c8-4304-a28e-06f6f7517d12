# 🎯 MEG Talep Onay Kartı Sistemi - Kullanım Kılavuzu

## 📋 Sistem Özeti

Bu sistem, talep tablosunda değişiklik olduğunda yöneticilere onay kartları gösterir ve onaylama/silme işlemlerini sağlar.

### 🚀 Oluşturulan Dosyalar

1. **`talepOnayDbConfig.js`** - Veritabanı konfigürasyonu
2. **`talepOnayKarti.js`** - Ana JavaScript modülü
3. **`talepOnayKarti.html`** - Web arayüzü
4. **`TalepOnayKarti.jsx`** - React Native Expo komponenti
5. **`talepOnayAPI.js`** - API sunucusu
6. **`test-talep-onay.js`** - Test sistemi

## 🔧 Kurulum

### 1. Gerekli Paketleri Yükleyin

```bash
npm install
```

### 2. Veritabanı Bağlantısını Kontrol Edin

`talepOnayDbConfig.js` dosyasında bağlantı bilgileri:
- **Host:** *************
- **Kullanıcı:** mehmet
- **Şifre:** Mb_07112024
- **Veritabanı:** bormeg_msg

## 🖥️ Web Arayüzü Kullanımı

### 1. API Sunucusunu Başlatın

```bash
npm run api
```

### 2. Tarayıcıda Açın

```
http://localhost:3001
```

### 3. Özellikler

- ✅ **Talep Kartları:** Onay bekleyen talepler görüntülenir
- 📊 **İstatistikler:** Toplam talep, tutar ve bugünkü talep sayısı
- 📋 **Detay Modal:** Tıklayarak talep detaylarını görüntüleyin
- ✅ **Onayla Butonu:** Talebi onaylar ve listeden kaldırır
- 🗑️ **Sil Butonu:** Talebi iptal eder ve listeden kaldırır
- 🔄 **Yenile:** Aşağı çekerek listeyi yenileyin

## 📱 React Native Expo Kullanımı

### 1. Komponenti İçe Aktarın

```jsx
import TalepOnayKarti from './TalepOnayKarti';

export default function App() {
  return <TalepOnayKarti />;
}
```

### 2. Gerekli Expo Paketleri


## Son Değişiklikler

Aşağıdaki dosyalar klasörde son zamanlarda değiştirildi veya güncellendi. Özetler dosyaların mevcut içeriklerine bakılarak hazırlanmıştır; isterseniz commit/diff bazlı kesin fark listesini de çıkarabilirim.

- `DogrudanHarcamaKaydet.php` — Formun otomatik gönderilmesi (onload), sepet yönetimi fonksiyonları (`sepetNoVer()`), paslama/token üretme (`paslamaKoduVer()`), fatura tarihi ve cari kodu doğrulamaları eklendi; "Yeni Kayıt" için INSERT sorgusu ve doğrudan harcama alanlarında güncellemeler yapıldı.

- `talepDogrudanHarcama.php` — Arayüz tarafında modal/pencere işlemleri için JavaScript fonksiyonları (e-Fatura listeleme, veri transferleri), AJAX çağrıları ve istemci tarafı doğrulamalar eklendi/güncellendi; tablo ve modal stillerinde iyileştirmeler yapıldı.

- `talepOlustur.php` — Talep oluşturma ekranında modal pencereler, tablo okuma/kaydetme (`tabloyuOku`), sayısal yuvarlama yardımcı fonksiyonu (`roundNumber`) ve zorunlu alan kontrolleri (`veriKontrol`) güncellendi.

- `talepDetayGiris.php` — Talep detay giriş ekranında stok/hizmet seçimi için veri aktarım fonksiyonları ve modal açma/kapatma rutinleri eklendi/düzeltildi.

- `TopluOnay.php` — Toplu onay ve beklemeye alma/iptal işlemlerine ilişkin AJAX tabanlı fonksiyonlar eklendi; tablo satırlarını gizleme/güncelleme mantığı ve UI davranışları iyileştirildi.

- `polling-sync.js` — Polling/senkronizasyon mekanizmasında zamanlama ve hata yakalama ile ilgili küçük düzeltmeler yapıldı.

Not: Bu özetler dosyaların mevcut içeriklerinden çıkarılmıştır; repo üzerinde commit/diff analizi isterseniz detaylı bir değişiklik raporu hazırlayabilirim.

## Gereksinimler - Durum

- `TALEP_ONAY_KULLANIM_KILAVUZU.md` dosyas g fcncellendi: Done
- Son deifikler tespiti (klas3rde en son deifik dosyalar incelendi): Done

E5Eri5iim notu: Bu tespitler dosyalarn mevcut i e7eriklerinden elde edilmitir; e1fer repo'da commit/versiyon farklar3 gerekliyse git log/diff ile kesin farklar raporlanabilir.

```bash
expo install expo-linear-gradient @expo/vector-icons
```

### 3. API Bağlantısını Güncelleyin

`TalepOnayKarti.jsx` dosyasında TODO kısımlarını güncelleyin:

```jsx
// TODO kısımlarını gerçek API endpoint'leriyle değiştirin
const response = await fetch('YOUR_API_ENDPOINT/onay-bekleyen-talepler');
```

## 🧪 Test Sistemi

### 1. Testleri Çalıştırın

```bash
npm run test
```

### 2. Test Kapsamı

- ✅ Database bağlantısı
- ✅ Talep getirme
- ✅ Talep detay alma
- ✅ Fonksiyon varlığı kontrolü
- ✅ Tarih ve para formatı

## 🔌 API Endpoints

### GET `/api/onay-bekleyen-talepler`
Onay bekleyen talepleri getirir

**Parametreler:**
- `limit` (opsiyonel): Getirilecek talep sayısı (varsayılan: 20)

**Yanıt:**
```json
{
  "success": true,
  "data": {
    "talepler": [...],
    "istatistikler": {
      "toplamTalep": 5,
      "toplamTutar": 15450.00,
      "bugunkuTalep": 2
    }
  }
}
```

### GET `/api/talep-detay/:talepSn`
Belirli bir talebin detayını getirir

### POST `/api/talep-onayla/:talepSn`
Talebi onaylar

**Body:**
```json
{
  "onayKullanici": "kullanici_adi"
}
```

### DELETE `/api/talep-sil/:talepSn`
Talebi siler (iptal eder)

**Body:**
```json
{
  "silmeKullanici": "kullanici_adi"
}
```

### GET `/api/health`
API sunucu durumunu kontrol eder

## 🎨 Özelleştirme

### 1. Renk Teması Değiştirme

HTML ve React Native dosyalarında renk kodlarını düzenleyin:
- **Ana Renk:** `#667eea`
- **Onay Rengi:** `#28a745`
- **Silme Rengi:** `#dc3545`

### 2. Talep Limiti Değiştirme

`talepOnayKarti.js` dosyasında `getOnayBekleyenTalepler(limit)` fonksiyonunu kullanın.

### 3. Tarih Formatı

Türkçe tarih formatı: `DD-MM-YYYY HH:mm`
`turkishDateFormat()` fonksiyonunu özelleştirin.

## 🚨 Sorun Giderme

### 1. Veritabanı Bağlantı Hatası

```bash
# Test çalıştırarak bağlantıyı kontrol edin
npm run test
```

### 2. API Sunucu Çalışmıyor

```bash
# Port 3001'in boş olduğundan emin olun
netstat -an | findstr :3001

# Sunucuyu yeniden başlatın
npm run api
```

### 3. React Native Hatası

```bash
# Expo cache temizleyin
expo r -c

# Node modules'ü yeniden yükleyin
rm -rf node_modules && npm install
```

## 📈 Performans Optimizasyonu

### 1. Veritabanı İndexleri

Talep tablosunda şu indexleri oluşturun:
```sql
CREATE INDEX idx_talep_tamamlandi ON talep(tamamlandi);
CREATE INDEX idx_talep_dogrudan ON talep(dogrudan);
CREATE INDEX idx_talep_yaratma_zaman ON talep(yaratma_zaman);
```

### 2. Sayfalama

Büyük veri setleri için sayfalama ekleyin:
```javascript
const talepler = await onayKarti.getOnayBekleyenTalepler(20, offset);
```

## 🔒 Güvenlik

### 1. Kullanıcı Doğrulama

API endpoint'lerine kimlik doğrulama ekleyin:
```javascript
app.use('/api', authenticateUser);
```

### 2. Rate Limiting

API çağrılarına limit koyun:
```javascript
const rateLimit = require('express-rate-limit');
app.use(rateLimit({ windowMs: 15 * 60 * 1000, max: 100 }));
```

## 📞 Destek

Herhangi bir sorun yaşarsanız:

1. **Test Çalıştırın:** `npm run test`
2. **Log Kontrol Edin:** Konsol çıktılarını inceleyin
3. **Veritabanı Kontrolü:** PostgreSQL bağlantısını doğrulayın

---

🎉 **Sistem başarıyla kuruldu ve kullanıma hazır!**