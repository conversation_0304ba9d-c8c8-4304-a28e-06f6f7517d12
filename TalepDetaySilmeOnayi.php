<?php
require('yetki.php');
$ekranKod = 316;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8 , width=device-width, initial-scale=1"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>
<script src="jquery-3.6.0.js"></script>


<style style="text/css">





.tusBosluk2 {
        margin-left :30px ;
        margin-right :30px ;
    }







    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}

    
	/* Define the default color for all the table rows */
	.sortable th{ 
        position:sticky;
        /*position: -webkit-sticky;*/
        top: 0;
        background: green;/*#727272;*/
        color: white; /*#c5c5c5;*/
        z-index: 0;

		/*padding:7px; border: #4e95f4 1px solid;*/
		/*#4e95f4 orjinal hali*/ 
}

	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
        margin-left :10px 
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }




    /** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}


</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">




	
function talepDetaySilmeIstegiYap(sender, gelenTalepNo, gelenTalepDetayNo){	
	
	let aciklama = prompt("Talep No: " + gelenTalepNo + "\nTalep Detay No: "+ gelenTalepDetayNo +"\nilgili talep detayının Silinme isteği açıklamasını yazın.");

	if (aciklama == null || aciklama=="") {
	  alert("İşlem iptal edildi.");
	  return false;
	}
	
	//Buradan sonra silme talebi kaydı oluşturuyor.
	
	sender.style.display = "none";
	
	$.ajax({
            method: "POST",
            url: "TalepSilmeTalebiYaz.php",
            async: true,
            data: { talepSn: gelenTalepNo, 
					talepDetaySn: gelenTalepDetayNo, 
                    aciklama: aciklama
                }        
            })
            .done(function( response ) {             
                response = response.trim();
                //console.log(response);

                

                if(response.indexOf("Okkk") !== -1){ 
					let cevap = response.split("#");				
					
				
                    alert(cevap[1]); 
                    //donenDeger = true;
					//tabloSatirGizle("tableMainMuzHasat", gelenSayac);

					

                    //return true;
                                
                } else{
                    alert("Silme Talebi kayıt edilemedi!\n"+response);
					sender.style.display = "";
                    //console.log("Hata");           
                    //donenDeger = false;
                    //return false;                
                }             
                
            });
	
	


	
	
}





function geri(gelenSayac){

	var trh2= 'tarih' + gelenSayac;

	var el = document.getElementById(trh2);

	if (el.value=='') return;	

	var tarih = new Date(el.value);

	tarih.setDate(tarih.getDate() -1);

	//console.log(tarih);

	var gun = tarih.getDate();
	//console.log(gun);
	var ay = tarih.getMonth() + 1;
	//console.log(ay);
	var yil = tarih.getFullYear();
	//console.log(yil);

	var yeniTarih = yil + '-'+ sifirEkle(ay) + '-' + sifirEkle(gun); 

	//console.log("Yeni  :" +yeniTarih);

	document.getElementById(trh2).value = yeniTarih; //"2014-02-09"

	//el.setDate(tarih);

	//console.log(yeniTarih);
	//console.log(ay);



}

function sifirEkle(gelen){
	
	var n = gelen.toString().length;	
	//console.log(n);	
	if(n==1) return "0" + gelen; else return gelen;
	
}



function ileri(gelenSayac){


	var trh2= 'tarih' + gelenSayac;

	//console.log(trh2);

	var el = document.getElementById(trh2);

	if (el.value=='') return;	

	var tarih = new Date(el.value);

	tarih.setDate(tarih.getDate() +1);

	//console.log(tarih);

	var gun = tarih.getDate();
	//console.log(gun);
	var ay = tarih.getMonth() + 1;
	//console.log(ay);
	var yil = tarih.getFullYear();
	//console.log(yil);

	var yeniTarih = yil + '-'+ sifirEkle(ay) + '-' + sifirEkle(gun); 

	//console.log("Yeni  :" +yeniTarih);

	document.getElementById(trh2).value = yeniTarih; //"2014-02-09"




}

function sifiraZorla(gelenNumber){
    
    var x = document.getElementById(gelenNumber).value;

    if(x=='') document.getElementById(gelenNumber).value = 0; 

}
















function proceed (gelenSayfa) {
    var form = document.createElement('form');
    form.setAttribute('method', 'post');    
    form.setAttribute('action', gelenSayfa);
    form.style.display = 'hidden';
    document.body.appendChild(form)
    form.submit();
}

function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sayac){
    
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edildi.");
		return false;
    }    

    var kisilerListesi = "";

    var i=1;
        for (i = 1; i <= sayac; i++) {               
                
                if(document.getElementById("kisiler-" + i).checked == true){
                    kisilerListesi+=document.getElementById("kisiler-" + i).value+';';
                }
        } 

    kisilerListesi = kisilerListesi.slice(0, -1);


    //console.log(kisilerListesi);

    if(kisilerListesi==""){
        alert("Görevlendirme için en az bir kişi seçilmelidir!\nİşlem iptal edildi.");
		return false;		
		
	}

    document.getElementById("kisiBilgileri").value = kisilerListesi;
	return true;	


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function searchTableColumns(gelenTablo, gelenAlan,gelenHedefAlan) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      var bulunanKayitAdet = 0; //Kayıt sayısı için
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
            bulunanKayitAdet++;
        } else {
            tr[i].style.display = "none";
        }
      }   

      //Kayıt adedi yazılıyor.

      if(bulunanKayitAdet > 0){
          document.getElementById(gelenHedefAlan).innerHTML = bulunanKayitAdet + " Adet kayıt listelendi"; 
      }else{
          document.getElementById(gelenHedefAlan).innerHTML = ""; 
      }





}




function kolonArama($gelenKolon, $kolonNo) {
  var input, filter, table, tr, td, i, txtValue;
  input = document.getElementById($gelenKolon);//"myInput"
  filter = input.value.toLocaleUpperCase("tr");
  table = document.getElementById("tableMain");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[$kolonNo];
    if (td) {
      txtValue = td.textContent || td.innerText;
      if (txtValue.toLocaleUpperCase("tr").indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	//console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function modalGoster(gelenKod, gelenKriter) {
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
    }
    };
    xhttp.open("POST", "talepHareketVer.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("talepDetaySn="+gelenKod+"&kriter="+gelenKriter);

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
}

function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}





</script>
</head>
<body>

<?php




function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}

function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn='$gorevSn';";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$row[0];	   
   }  

   return $liste;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
}


function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}


function talepHareketVarMi($gelenTalepNo, $gelenTalepDetayNo){

    global $db;

    $sorgu = "SELECT COUNT(*) FROM talep_hareket WHERE talep_sn = $gelenTalepNo AND talep_detay_sn = $gelenTalepDetayNo;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    } 

    $kayitAdedi = 0;

    while($row = pg_fetch_row($ret)){        
        $kayitAdedi = $row[0];	   
    }

    if($kayitAdedi>0) $donenDeger = true; else $donenDeger = false;

    return $donenDeger;
}


function talepHareketteNakitAcikVarMi($gelenTalepNo, $gelenTalepDetayNo){

    global $db;

    $sorgu = "SELECT COALESCE(SUM(nakit_odenen), 0), COALESCE(SUM(acik_odenen), 0) FROM talep_hareket WHERE talep_sn = $gelenTalepNo AND talep_detay_sn = $gelenTalepDetayNo;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    } 

    $kayitAdedi = 0;

    while($row = pg_fetch_row($ret)){  //Nakit veya Açık ödeme var ise?

        if($row[0]>0 || $row[1]>0) $donenDeger = true; else $donenDeger = false;     
           
    }    

    return $donenDeger;
}



function bostaTalepDetayVarMi($gelenTalepSn){

    global $db;    

    $sorgu = "SELECT COUNT(*) FROM talep_detay WHERE iptal=FALSE
                                                AND isleme_alindi = FALSE
                                                AND onay = TRUE
                                                AND tamamlandi = FALSE
                                                AND talep_sn = $gelenTalepSn;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    } 

    $kayitAdedi = 0;

    while($row = pg_fetch_row($ret)){        
        $kayitAdedi = $row[0];	   
    }

    if($kayitAdedi>1) $donenDeger = true; else $donenDeger = false;

    return $donenDeger;
}


function talepTamamlandiMi($gelenTalepSn){

    global $db;    

    $sorgu = "SELECT tamamlandi FROM talep WHERE sn = $gelenTalepSn;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    }     

    while($row = pg_fetch_row($ret)){        
        $donenDeger = $row[0];	   
    }    

    

    return $donenDeger;
}


function gorevYetkililerineEkle($gelenTalepSn){

    global $db; 
    global $kullanici;
    
    $gelenGorevSn = 0;
    $donenDeger = true;
    
    //Önce Görev Numarası bulunuyor.

    $sorguCumle = "SELECT sn FROM gorevlendirme WHERE talep_sn = $gelenTalepSn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        echo "Görev kodu alınamadı!!";
        exit;
    }

    while($row = pg_fetch_row($ret)){        
        $gelenGorevSn = $row[0];	   
    }

    if($gelenGorevSn==0){
        echo "Satınalmaya ait görev numarası bulunamadı!";
        //exit;
    }



    //Talebi üzerine alan kişi de görevi görebilir listesine ekleniyor.

    $sorguCumle="INSERT INTO gorev_alan(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, '$kullanici')";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        echo "Görev Alanlar kaydedilemedi!";
        exit;
    }


    //Talebi oluşturan kişi

    $sorguCumle="INSERT INTO gorev_gorebilen(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, '$kullanici')";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        echo "Görev Alanlar kaydedilemedi!";
        exit;
    }    

    return $donenDeger;
}




function nakitseAkinsoftaYaz($gelenTalepDetaySn){

    global $db; 

    $sorgu = "SELECT talep_hareket.sn, 
                     talep_hareket.nakit_odenen, 
                     nakit_odenen_cari_kod, 
                     talep_hareket.talep_sn, 
                     talep_detay.islem_kullanici, 
                     talep.dogrudan,
                     talep.sube,
                     talep.fatura_durumu /*7*/
                                        FROM talep_hareket 
                                        JOIN talep_detay ON talep_detay.sn = talep_hareket.talep_detay_sn
                                        JOIN talep ON talep.sn = talep_hareket.talep_sn 
                                        WHERE talep_hareket.nakit_odenen>0 
                                        AND talep_detay_sn = $gelenTalepDetaySn;";

    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
        echo "Tabloya ulaşılamadı!";
        exit;
    }     

    while($row = pg_fetch_row($ret)){  
        talepHarekettenAkinsoftdaOdemeYap($row[5],  /*Doğrudan Mı?*/
                                          $row[4],  /*Talebi üzerine alan kullanıcı*/
                                          $row[3],  /*Tale No*/
                                          $row[2],  /*Ödeme Yapılan Cari Kod*/
                                          $row[1],  /*Nakit Ödenen Tutar*/
                                          $row[0],  /*Talep Hareket No*/
                                          subeKoduVer($row[6]),  /*Talep Şubesi*/
                                          $row[7]  /*Fatura Durumu*/
                                        );
        $donenDeger = $row[0];	   
    }    

}




function talepUzerineAlmaLogYaz($gelenTalepSn, $gelenTalepDetaySn, $gelenKullanici, $gelenEylemTip){
	
	global $db;
	
	$sorgu = "INSERT INTO talep_uzerine_alma_hareket(talep_sn, talep_detay_sn, kullanici, islem_tip)
					VALUES($gelenTalepSn, $gelenTalepDetaySn, '$gelenKullanici', '$gelenEylemTip');";
					
					
	$ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo "Hata 02: " . pg_last_error($db). "\n$sorgu";
       echo "Tabloya ulaşılamadı!";
       exit();
    }
	
	
}




function oncedenGirilmistalepVarMi($gelenTalepSn, $gelenTalepDetaySn){
	
	global $db;
	
	$sorgu = "SELECT COUNT(sn) 
					FROM talep_silme_istegi 
					WHERE (talep_sn = $gelenTalepSn
					AND talep_detay_sn = $gelenTalepDetaySn) OR 
					(talep_sn = $gelenTalepSn
					AND talep_detay_sn = 0);"; //Tamamı için de istenmişse burada görünmemeli!
					
					
	$ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo "Hata 02: " . pg_last_error($db). "\n$sorgu";
       echo "Tabloya ulaşılamadı!";
       exit();
    }
	
	
	
	while($row = pg_fetch_row($ret)){
		
		if($row[0]>0){
			$donenDeger = true;
		}else{
			$donenDeger = false;
		}	
		
	}	
	
	return $donenDeger;
	
	
}



//Doğrudan harcamad da kullanılacak.
//O yüzden include yapmak daha mantıklı.

//require_once("TalepHarekettenAkinsoftdaOdemeYap.php");






//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */





//Bağlantı Yapılıyor... 
//require_once("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");
require("sqlMetinDuzenle.php");
//require("dosyalariYukle.php");
//require("AkinsoftIslemleri.php");
//require("BelgesiVarMi.php");



$gelenTalepSn = kullaniciGirisGuvenlikKontrol($_POST['talepSn']);

/*
$gelenTalepDetaySn = $_POST['talepDetaySn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];

//Harekey kaydının silinmesi için
$gelenTalepHareketSn = $_POST['talepHareketSn'];
$gelenTarih1 = $_POST['tarih1'];
$gelenTarih2 = $_POST['tarih2'];
*/



require("saat1.inc");



echo '<br><h1 style="text-align:center;">Talep Detay Silme Onayı</h1>';



echo '<button type="button" onclick="self.close();" style="width:200px;" >Kapat</button><br><br>';



if($gelenTalepSn==""){
	echo "Veri Hatası!";
	goto cikis;
}









$sorguCumle = "SELECT talep_detay.sn, 
                    talep_sn, 
                    talep.konu, 
                    talep.detay, 
                    aciklama, 
                    miktar, 
                    birim, 
                    termin_zaman, 
                    onay, 
                    onay_zaman, 
                    isleme_alindi, 
                    islem_zaman, 
                    talep_detay.tamamlandi, /*12*/
                    tamamlandi_zaman, 
                    talep.tamamlandi,
                    talep_detay.kullanici,
                    talep_detay.iptal, 
                    islem_kullanici,
                    talep_detay.kullanici,
                    talep_detay.onay_kullanici,
                    talep.gorev_sn,
                    talep.sube,
					talep.talep_eden, /*22*/
					talep.kullanici,
					talep_detay.teslim_kabul,
					talep_detay.islem_kullanici /*25*/
					FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn 
								   WHERE talep_detay.iptal = FALSE
								   AND talep_detay.talep_sn = $gelenTalepSn								   
								   ORDER BY talep_detay.yaratma_zaman, talep_detay.sn DESC;";

//echo $sorguCumle;

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
} 


echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" oninput="searchTableColumns(\'tableMain\',\'myInputHepsi\',\'bulunanKayitAdet\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">&nbsp;&nbsp;<label id="bulunanKayitAdet"></label>';


echo "<br><br>";  


echo '<table class= "sortable" valign="middle" id="tableMain">';
echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
echo '<th style="text-align:center;cursor:pointer;">Detay<br>S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Satınalma S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Konu</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Açıklama</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep<br>Miktar</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep<br>Birim</th>';
echo '<th style="text-align:center;cursor:pointer;">Termin Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">İlgili<br>Şube</th>';
echo '<th style="text-align:center;cursor:pointer;">Oluşturan</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep<br>Eden</th>';
echo '<th style="text-align:center;cursor:pointer;">Onaylayan</th>';
echo '<th style="text-align:center;cursor:pointer;">Onay<br>Zaman</th>';
//echo '<th style="text-align:center;cursor:pointer;">İşleme Alındı</th>';
echo '<th style="text-align:center;cursor:pointer;">Satınalmacı</th>';
echo '<th style="text-align:center;cursor:pointer;">İşlem Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">Tamamlandı</th>';
echo '<th style="text-align:center;cursor:pointer;">Tamamlanma Zaman</th>';
echo "<th style=\"text-align:center\">İşlem</th>";
echo "</tr>";

//$sayac = 1;

while($row = pg_fetch_row($ret)){
	
	//Sadece talep sahibi ve kaydı açan görebiliyor.
	/*
	if( ($gelenKriter=="1" || $gelenKriter == "2") && $_SESSION["root"] == 0 ){
		
		if($row[22]!=$kullanici && $row[23]!=$kullanici ){
			continue;
		}
		
	}
	*/

    //Gizli görev ve kullanıcı listede yoksa ve root değilse pas geçiliyor.

    //$goreviGorebilir = goreviGorebilir($row[6], $kullanici, $row[0]);

    //Sadece görevi alanlar
    //$sadeceGorevli = sadeceGorevli($row[6], $kullanici);
    /*
    if($_SESSION["root"] == 0){
        
        if( $row[7]=='t' && $goreviGorebilir == false ) continue;
    }
    */
//$gelenKriter

    echo "<tr class='item' title='". $row[3] ."'>";
    echo "<td onclick='modalGoster(\"$row[1]-$row[0]\",$gelenKriter)' style='text-align:center'>". $row[0] . "</td>"; //S/N       
    echo "<td onclick='modalGoster(\"$row[1]-$row[0]\",$gelenKriter)' style='text-align:center'>". $row[1] . "</td>"; //S/N       
    echo "<td onclick='modalGoster(\"$row[1]-$row[0]\",$gelenKriter)'>". $row[2] . "</td>"; //Konu
    echo "<td onclick='modalGoster(\"$row[1]-$row[0]\",$gelenKriter)'>". $row[4] . "</td>"; //Açıklama
	echo "<td style='text-align:center'>". $row[5] . "</td>"; //Miktar    
    echo "<td style='text-align:center'>". $row[6] . "</td>"; //Birim
    echo "<td sorttable_customkey='". tarihFormatla($row[7]) ."' style='text-align:center'>". tarihYaz($row[7]) . "</td>"; //TerminZaman
    echo "<td style='text-align:center'>". $row[21] . "</td>"; //Şube
    echo "<td style='text-align:center'>". $row[18] . "</td>"; //oluşturan
	echo "<td style='text-align:center'>". $row[22] . "</td>"; //Talep Eden

    echo "<td style='text-align:center'>". $row[19] . "</td>"; //Onay
    echo "<td sorttable_customkey='". tarihFormatla($row[9]) ."' style='text-align:center'>". tarihSaatYaz($row[9]) . "</td>"; //Onay Zaman
    //echo "<td style='text-align:center'>". trueFalse($row[10],'İşlemde') . "</td>"; //İşleme Alındı
    echo "<td style='text-align:center'>". $row[17] . "</td>"; //Satınalmacı
	

    echo "<td sorttable_customkey='". tarihFormatla($row[11]) ."' style='text-align:center'>". tarihSaatYaz($row[11]) . "</td>"; //İşlem Zaman

    echo "<td style='text-align:center'>". trueFalse($row[12],'Tamamlandı') . "</td>"; //Tamamlandı
    echo "<td sorttable_customkey='". tarihFormatla($row[13]) ."' style='text-align:center'>". tarihSaatYaz($row[13]) . "</td>"; //Tamamlanma Zaman
       


    //Detay, silme ve Hikaye Tuşları
    echo "<td style='vertical-align: middle; text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";

    

    if($row[8]=='t' && $duzeltYetki =  't'){ //Satınalma onaylanmış ve kullanıcının da DÜZELTME yekisi verilmiş ise?
	
		if(oncedenGirilmistalepVarMi($row[1], $row[0])){			
			echo "<font style='color:red;'>Önceden verilmiş bir istek var!</font>";
		}else{
			echo '<button type="button" class="inline" onclick="talepDetaySilmeIstegiYap(this, '.$row[1].', '.$row[0].');"><img src="warning-icon.png" height="20" width="20" title="Talep Detay Silme Onayı==>'.$row[0].'" valign="middle"/></button>';
		}	
		
    }


	echo "</div>";
	echo "</td>";
    
    //Detay ve Hikaye Tuşları sonu  

   


  echo "</tr>";
    //$sayac+=1;
}

echo "</table>";



 //Modal
 echo '<div id="myModal" class="modal">

 <!-- Modal content -->
 <div class="modal-content">
   <div class="modal-header" >
     <span class="close" onclick="spanKapat()">&times;</span>
     <h2>Satınalma Hareketleri</h2>
   </div>
   <div class="modal-body" id="icerik">    

   </div>
   <div class="modal-footer">
     <h3></h3>
   </div>
 </div>

</div>';




cikis:

pg_close($db);

echo "<br><br><br>";
require("saat2.inc");
echo "<br><br><br>";


?>

</body>
</html>						