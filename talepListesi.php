<?php
require('yetki.php');
$ekranKod = 58;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>
<script src="jquery-3.6.0.js"></script>





<style style="text/css">




/* Sortable tables */




.tusBosluk2 {
        margin-left :30px ;
        margin-right :30px ;
    }





table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}

    /* Define the default color for all the table rows */
	.sortable th{ 
        position:sticky;
        /*position: -webkit-sticky;*/
        top: 0;
        background: green;/*#727272;*/
        color: white; /*#c5c5c5;*/
        z-index: 0;

		/*padding:7px; border: #4e95f4 1px solid;*/
		/*#4e95f4 orjinal hali*/ 
}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
        margin-left :10px 
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }

     /** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}













</style>

<?php
require('siteBaslik.html');
require('menuKaynak.php');
//require('blink.html');
?>


<script type="text/javascript">





function talepDetaySilmeIstegiYap(sender, gelenTalepNo){
	
	alert("Tasarım halinde " + gelenTalepNo);
	
}
	
	
	
	
	
function talepSilmeIstegiYap(sender, gelenTalepNo){	
	
	let aciklama = prompt(gelenTalepNo + " numaralı talebin Silinme isteği açıklamasını yazın.");

	if (aciklama == null || aciklama=="") {
	  alert("İşlem iptal edildi.");
	  return false;
	}
	
	//Buradan sonra silme talebi kaydı oluşturuyor.
	
	sender.style.display = "none";
	
	$.ajax({
            method: "POST",
            url: "TalepSilmeTalebiYaz.php",
            async: true,
            data: { talepSn: gelenTalepNo, 
                    aciklama: aciklama
                }        
            })
            .done(function( response ) {             
                response = response.trim();
                //console.log(response);

                

                if(response.indexOf("Okkk") !== -1){ 
					let cevap = response.split("#");				
					
				
                    alert(cevap[1]); 
                    //donenDeger = true;
					//tabloSatirGizle("tableMainMuzHasat", gelenSayac);

					

                    //return true;
                                
                } else{
                    alert("Silme Talebi kayıt edilemedi!\n"+response);
					sender.style.display = "";
                    //console.log("Hata");           
                    //donenDeger = false;
                    //return false;                
                }             
                
            });
	
	


	
	
}






function geri(gelenSayac){

	var trh2= 'tarih' + gelenSayac;

	var el = document.getElementById(trh2);

	if (el.value=='') return;	

	var tarih = new Date(el.value);

	tarih.setDate(tarih.getDate() -1);

	//console.log(tarih);

	var gun = tarih.getDate();
	//console.log(gun);
	var ay = tarih.getMonth() + 1;
	//console.log(ay);
	var yil = tarih.getFullYear();
	//console.log(yil);

	var yeniTarih = yil + '-'+ sifirEkle(ay) + '-' + sifirEkle(gun); 

	//console.log("Yeni  :" +yeniTarih);

	document.getElementById(trh2).value = yeniTarih; //"2014-02-09"

	//el.setDate(tarih);

	//console.log(yeniTarih);
	//console.log(ay);



}

function sifirEkle(gelen){
	
	var n = gelen.toString().length;	
	//console.log(n);	
	if(n==1) return "0" + gelen; else return gelen;
	
}



function ileri(gelenSayac){


	var trh2= 'tarih' + gelenSayac;

	//console.log(trh2);

	var el = document.getElementById(trh2);

	if (el.value=='') return;	

	var tarih = new Date(el.value);

	tarih.setDate(tarih.getDate() +1);

	//console.log(tarih);

	var gun = tarih.getDate();
	//console.log(gun);
	var ay = tarih.getMonth() + 1;
	//console.log(ay);
	var yil = tarih.getFullYear();
	//console.log(yil);

	var yeniTarih = yil + '-'+ sifirEkle(ay) + '-' + sifirEkle(gun); 

	//console.log("Yeni  :" +yeniTarih);

	document.getElementById(trh2).value = yeniTarih; //"2014-02-09"




}

function sifiraZorla(gelenNumber){
    
    var x = document.getElementById(gelenNumber).value;

    if(x=='') document.getElementById(gelenNumber).value = 0; 

}







function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sayac){
    
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edildi.");
		return false;
    }    

    var kisilerListesi = "";

    var i=1;
        for (i = 1; i <= sayac; i++) {               
                
                if(document.getElementById("kisiler-" + i).checked == true){
                    kisilerListesi+=document.getElementById("kisiler-" + i).value+';';
                }
        } 

    kisilerListesi = kisilerListesi.slice(0, -1);


    //console.log(kisilerListesi);

    if(kisilerListesi==""){
        alert("Görevlendirme için en az bir kişi seçilmelidir!\nİşlem iptal edildi.");
		return false;		
		
	}

    document.getElementById("kisiBilgileri").value = kisilerListesi;
	return true;	


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}

//Tüm kolonlarda arama

function searchTableColumns(gelenTablo) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById("myInputHepsi");
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length-1; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
      }      
}

function kolonArama(gelenKolon, kolonNo) {
  var input, filter, table, tr, td, i, txtValue;
  input = document.getElementById(gelenKolon);//"myInput"
  filter = input.value.toLocaleUpperCase("tr");
  table = document.getElementById("tableMain");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[kolonNo];
    if (td) {
      txtValue = td.textContent || td.innerText;
      if (txtValue.toLocaleUpperCase("tr").indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function modalGoster(gelenKod) {
    var xhttp = new XMLHttpRequest();
	
	document.getElementById('baslik').innerHTML = "Satınalma Detayı";
	
	
	
    xhttp.onreadystatechange = function() {
		
		if (this.readyState == 4 && this.status == 200) {
		  document.getElementById("icerik").innerHTML = this.responseText;
		}
		
    };
    xhttp.open("POST", "talepDetayVer.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("talepSn="+gelenKod);

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
}



function modalGosterCariler(gelenKod) {
    var xhttp = new XMLHttpRequest();
	
	document.getElementById('baslik').innerHTML = "Alım Yapılan Cariler";
	
	
	
    xhttp.onreadystatechange = function() {
		
		if (this.readyState == 4 && this.status == 200) {
		  document.getElementById("icerik").innerHTML = this.responseText;
		}
		
    };
    xhttp.open("POST", "talepAlimHareketleri.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("talepSn="+gelenKod);

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
}





function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
	document.getElementById("icerik").innerHTML = "";
    document.getElementById('baslik').innerHTML = "";
    modal.style.display = "none";
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}

</script>

</head>

<body>



<?php




function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}

function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn='$gorevSn';";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$row[0];	   
   }  

   return $liste;
}



function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }



function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}


/*
function belgesiVarMiTamami($gelenTalepSn){

    $klasor = "/mnt/talepBelgeler/".$gelenTalepSn."/";


    if(!file_exists($klasor)) return false;

    $sayac = 0;


    $di = new RecursiveDirectoryIterator($klasor);    

    foreach (new RecursiveIteratorIterator($di) as $filename => $file) {
        //echo $filename . ' - ' . $file->getSize() . ' bytes <br/>';
        $sayac+=1;
    }



    //$dosyalar = scandir($klasor);

    //ilk iki dosya . ve .. oluyor!

    if($sayac>2) return true;else return false;

}
*/

function talepDetayAdet($talepSn){
	
	global $db;     
 
    $sorgu = "SELECT COUNT(sn) 
					FROM talep_detay
					WHERE talep_sn = $talepSn
					AND iptal=false
					AND tamamlandi=true;";
 
    $ret = pg_query($db, $sorgu);	
	
 
    if(!$ret){
		echo pg_last_error($db);
		echo "VT Ulaşılamadı!";
		exit();
    }
	
	
	$donenDeger = 0;
	
	while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }  
   
   
   return $donenDeger;	
	
}



//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */
//*********************************************************************** */



//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");
require("BelgesiVarMiTamami.php");




$gelenTalepSn = $_POST['talepSn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];

$gelenEylem = $_POST['eylem'];

$gelenTarih1 = $_POST['tarih1'];
$gelenTarih2 = $_POST['tarih2'];



require("saat1.inc");



//echo $gelenKriter;



if($gelenTarih1=="") $gelenTarih1 = date("Y-")."01-01";


if($gelenTarih2=="") $gelenTarih2 = date("Y-m-d"); //"2021-12-31";




if($gelenKriter=="")$gelenKriter = "2";



switch ($gelenKriter){
    case "1":
        echo '<br><br><h1 style="text-align:center;">Tüm Satınalmalar</h1>';
        break;
    case "2":
        echo '<br><br><h1 style="text-align:center;">Devam Eden Satınalmalar</h1>';
        break;
    case "3":
        echo '<br><br><h1 style="text-align:center;">Tamamlanmış Satınalmalar</h1>';
        break;
    
}




//Silme eylemi ile sayfa çağrılıyorsa önce kaydı silecek!!!
if($gelenEylem=="Sil" && $gelenTalepSn<>""){
    
    $sorguCumle = "DELETE FROM talep WHERE sn = $gelenTalepSn;";
    //Sorgu çalıştır.
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    //İlişikili klasör siliniyor.

    $yeniYol = "/mnt/talepBelgeler/".$gelenTalepSn;

    system("rm -rf ".escapeshellarg($yeniYol));




}

//**************************************



echo '<table border ="0">';

echo "<tr>";
echo "<td>";
echo '<form action="#" method="post">';
echo 'Kriter:';
echo "</td>";
echo "<td colspan='2'>";



echo '<select name="kriter">';
echo '<option value="1" ' .selectedKoy($gelenKriter, "1"). '>Tüm Satınalmalar</option>';
echo '<option value="2" ' .selectedKoy($gelenKriter, "2"). '>Devam Eden Satınalmalar</option>';
echo '<option value="3" ' .selectedKoy($gelenKriter, "3"). '>Tamamlanmış Satınalmalar</option>';
//echo '<option value="4" ' .selectedKoy($gelenKriter, "4"). '>Onay Bekleyenler</option>';
echo "</select>";


echo "</td>";

echo "</tr>";


echo '<tr>';
echo '<td>Talep Tarihi:</td>';
echo '<td>';
echo '<input type="date" id="tarih1" name="tarih1" class= "tusBosluk2" value="'.$gelenTarih1.'" title="Bu tarih ve sonrası!"/>';
echo '</td>';
echo '<td>';
echo '<input type="date" id="tarih2" name="tarih2" class= "tusBosluk2" value="'.$gelenTarih2.'" title="Bu tarihe kadar!"/>';
echo '</td>';


echo "</tr>";

echo '<tr>';
echo '<td>';
echo '</td>';
echo '<td>';
echo '<button type="button" class= "tusBosluk2" onclick="geri(1)">Geri</button>';
echo '<button type="button" class= "tusBosluk2" onclick="ileri(1)">İleri</button>';
echo '</td>';
echo '<td >';
echo '<button type="button" class= "tusBosluk2" onclick="geri(2)">Geri</button>';
echo '<button type="button" class= "tusBosluk2" onclick="ileri(2)">İleri</button>';
echo '</td>';




echo '</tr>';




echo '<tr>';
echo '<td colspan="3">';






echo '<input type="submit" value="Sorgula">';
echo '</form>';
echo '</td>';
echo '</tr>';

echo "</table>";




//**************************************







echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" onkeyup="searchTableColumns(\'tableMain\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">';

echo "<br><br>";






//Burada kullanıcılara bilgilendirme yapılacak.
$gidenKimden="<EMAIL>";
$gidenAdresler = [];
$gidenTelefonlar = [];
$mesaj = $gelenGorevSn. " Numaralı Görev için TAMAMLANDI TALEP veya TAMAMLANDI değişikliği yapıldı.\nLütfen Portaldan kontrol eder misiniz?";
$kisiler =[];



echo $gelenIslemTip;


if($gelenTalepDetaySn <> "" && $gelenIslemTip<>""){

    
    switch ($gelenIslemTip) {
        case "islemOnay":
            $sorguCumle = "UPDATE talep_detay SET isleme_alindi = TRUE, islem_zaman=NOW() 
                           WHERE sn=$gelenTalepDetaySn;"; 

            logYaz($gelenTalepDetaySn . " Numaralı satınalma detay işleme alındı.");

            //$gidenBilgi = $kullanici . " Görev kapatma talebi oluşturdu!";

            //Mesaj gidecek kişiler ve görevi oluşturan
            //$kisiler = gorevlendirilenleriArrayVer($gelenGorevSn);
            //Görevi veren de ekleniyor.
            //array_push($kisiler, gorevVereniVer($gelenGorevSn));

            
            break;
        case "tamamlandiOnay":
            $sorguCumle = "UPDATE talep_detay SET tamamlandi = TRUE, tamamlandi_zaman=NOW() 
                            WHERE sn=$gelenTalepDetaySn;"; 

            logYaz($gelenTalepDetaySn . " Numaralı satınalma detay işleme alındı.");

            //$gidenBilgi = $kullanici . " Görev kapatma talebi oluşturdu!";

            //Mesaj gidecek kişiler ve görevi oluşturan
            //$kisiler = gorevlendirilenleriArrayVer($gelenGorevSn);
            //Görevi veren de ekleniyor.
            //array_push($kisiler, gorevVereniVer($gelenGorevSn));            
            break;
        case "islemIptal":
            $sorguCumle = "UPDATE talep_detay SET isleme_alindi = FALSE, islem_zaman=NULL 
                           WHERE sn=$gelenTalepDetaySn;";            
            break; 
        case "tamamlandiIptal":
            $sorguCumle = "UPDATE talep_detay SET tamamlandi = FALSE, tamamlandi_zaman=NULL 
                            WHERE sn=$gelenTalepDetaySn;";            
            break;
        } 


    //Sorgu çalıştır.
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

}

/*    

    foreach ($kisiler as $kisi){
        array_push($gidenAdresler, kullanicidanBilgiVer('eposta', $kisi));

        $mesajX = $mesaj ."\n". paslamaKoduVer($kisi, 'GorevTamamlama.php', '', '');


        //Bu kısım KMS göndermek için.
        $sallaTel = []; 
        array_push($sallaTel, kullanicidanBilgiVer('cep_tel', $kisi) );
        //kisaMesajGonder($mesajX, $sallaTel, 1);
        unset($sallaTel);
        
    }

    

    if(count($gidenAdresler)>0) epostaGonder($gidenKimden, $gidenAdresler, $gidenBilgi, $mesaj);
    //if(count($gidenTelefonlar)>0) kisaMesajGonder($mesaj, $gidenTelefonlar, 1);






                    
}


*/

//  if($gelenGorevSn==""||$gelenIslemTip=="") exit;

//Gelen Değerler alınıyor...
/*
$gelenKullanici = $_POST['kullanici'];

$gelenTip = $_POST['tip'];
$gelenKriterTum = $_POST['kriterTum'];

$takas = explode(" - ", $gelenKullanici);

$gelenKullaniciKod = trim($takas[1]);

*/

//$aktifKullanici = $_SESSION["user"];


/*
CREATE TABLE public.gorevlendirme (
    sn integer NOT NULL,
    gorev_veren text NOT NULL,
    gorev_konu text NOT NULL,
    baslama_zaman timestamp without time zone DEFAULT now() NOT NULL,
    hedef_zaman timestamp without time zone DEFAULT now() NOT NULL,
    tamamlandi boolean DEFAULT false NOT NULL,
    bitis_zaman timestamp without time zone,
    uzatma_zaman timestamp without time zone,
    gorev_detay text DEFAULT ''::text,
    yaratma_zaman timestamp without time zone DEFAULT now() NOT NULL,
    duzeltme_zaman timestamp without time zone DEFAULT now() NOT NULL
);


if($_SESSION["root"] == 1){
    $sorguCumle = "SELECT gorev_veren, gorev_konu, baslama_zaman, uzatma_zaman, bitis_zaman, tamamlandi, gorevlendirme.sn, gizli, gorev_detay, hedef_zaman FROM gorevlendirme                     
                    ORDER BY hedef_zaman;";
}else{
    $sorguCumle = "SELECT gorev_veren, gorev_konu, baslama_zaman, uzatma_zaman, bitis_zaman, tamamlandi, sn, gizli, gorev_detay, hedef_zaman 
                    FROM gorevlendirme                    
                    ORDER BY baslama_zaman DESC;";

}
 */

$alanlar = "SELECT sn,                    
                    talep.konu, 
                    talep.detay, 
                    talep_eden,
                    kullanici,
                    yaratma_zaman,                    
                    talep.tamamlandi, 
                    tamamlanma_zaman,
                    sube ";

switch ($gelenKriter) {
    case "1": //Tamamı
        $sorguCumle = $alanlar .  "FROM talep 
								   WHERE dogrudan = FALSE
								   AND DATE(talep.yaratma_zaman)>='$gelenTarih1'
								   AND DATE(talep.yaratma_zaman)<='$gelenTarih2'		
								   ORDER BY sn DESC;";
      break;
    case "2": //Onay Bekleyenler

        $sorguCumle = $alanlar .  "FROM talep 
                        WHERE tamamlandi = FALSE
                        AND dogrudan = FALSE
						AND DATE(talep.yaratma_zaman)>='$gelenTarih1'
						AND DATE(talep.yaratma_zaman)<='$gelenTarih2'						
                        ORDER BY sn DESC;";              
      break;
    case "3": //Tamamlanmışlar
        $sorguCumle = $alanlar .  "FROM talep 
                                WHERE tamamlandi = TRUE
                                AND dogrudan = FALSE
								AND DATE(talep.yaratma_zaman)>='$gelenTarih1'
								AND DATE(talep.yaratma_zaman)<='$gelenTarih2'
                                ORDER BY sn DESC;"; 
      break;
        /*
    case "4": //Tamamlanmışlar
    $sorguCumle = $alanlar .  "FROM talep 
                            WHERE onaylandi      = FALSE
                            AND dogrudan = FALSE
                            ORDER BY sn DESC;"; 
    break; 
    */

  } 
//echo $sorguCumle;

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
} 


//echo "<br><br>";    


echo '<table class= "sortable" valign="middle" id="tableMain">';
echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
echo '<th style="text-align:center;cursor:pointer;">Satınalma<br>No</th>';
echo '<th style="text-align:center;cursor:pointer;">Konu</th>';
echo '<th style="text-align:center;cursor:pointer;">Detay</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Eden</th>';
echo '<th style="text-align:center;cursor:pointer;">Oluşturan</th>';
echo '<th style="text-align:center;cursor:pointer;">Şube</th>';
echo '<th style="text-align:center;cursor:pointer;">Oluşturma Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">Tamamlandı</th>';
echo '<th style="text-align:center;cursor:pointer;">Tamamlanma Zaman</th>';
echo "<th style=\"text-align:center\">İşlem</th>";
echo "</tr>";

//$sayac = 1;

while($row = pg_fetch_row($ret)){

    echo "<tr class='item' title='Satıra tıklayarak detayı görebilirsiniz.'>";
    echo "<td onclick='modalGoster(\"".$row[0]."\")'>". $row[0] . "</td>"; //S/N       
    echo "<td onclick='modalGoster(\"".$row[0]."\")'>". $row[1] . "</td>"; //Konu
    echo "<td onclick='modalGoster(\"".$row[0]."\")'>". $row[2] . "</td>"; //Detay
    echo "<td>". $row[3] . "</td>"; //Talep eden
    echo "<td>". $row[4] . "</td>"; //Kullanıcı
    echo "<td>". $row[8] . "</td>"; //Şube
    echo "<td sorttable_customkey='". tarihFormatla($row[5]) ."' style='text-align:center'>". tarihSaatYaz($row[5]) . "</td>"; //Yaratma Zaman
    echo "<td style='text-align:center'>". trueFalse($row[6],'Tamamlandı') . "</td>"; //Tamamlandı
    echo "<td sorttable_customkey='". tarihFormatla($row[7]) ."' style='text-align:center'>". tarihSaatYaz($row[7]) . "</td>"; //Tamamlanma Zaman
    



	

    //Detay, silme ve Hikaye Tuşları
    echo "<td style='vertical-align: middle; text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";


    
    if( $row[6]=='f' && $yeniYetki ==  't' && ($kullanici == $row[4] || $_SESSION["root"] == 1) ){ //Satınalma kapanmamış ve kullanıcının da YENİ yekisi verilmiş ise?
        echo '<form action="talepDetayGiris.php" target="_blank"  method="post"  class="inline">'; 
        echo '<input type="hidden" id="goreviVeren" name="goreviVeren" value="'.$kullanici.'"/>';
        echo '<input type="hidden" id="konu" name="konu" value="'.$row[1].'"/>';
        echo '<input type="hidden" id="detay" name="detay" value="'.$row[2].'" />';
        echo '<input type="hidden" id="talepEden" name="talepEden" value="'.$row[3].'"/>';
        echo '<input type="hidden" id="isteyenSube" name="isteyenSube" value="'.$row[8].'"/>';

        echo '<input type="hidden" id="kayitMod" name="kayitMod" value="Yeni Kayıt"/>';
        echo '<input type="hidden" name="detayGiris" value="2"/>'; //Varolan talepe detay giriliyor! 
		
		
		
		require('reloadKontrol11.php');
		
		
		
		

        echo '<input type="hidden" id="talepSn" name="talepSn" value="'.$row[0].'"/>';
        echo '<button type="submit"><img src="add-icon.png" height="20" width="20" title="Detay Giriş==>'.$x_form_check.'"></button>'; //  $row[0] 
        echo '</form>';   

    }

    //Sadece görüntüleme
    
    echo '<form action="talepDetayGiris.php" target = "_blank"  method="post"  class="inline">'; 
	require('reloadKontrol11.php');
    echo '<input type="hidden" id="goreviVeren" name="goreviVeren" value="'.$kullanici.'">';
    echo '<input type="hidden" id="konu" name="konu" value="'.$row[1].'">';
    echo '<input type="hidden" id="detay" name="detay" value="'.$row[2].'" >';
    echo '<input type="hidden" id="talepEden" name="talepEden" value="'.$row[3].'">';
    echo '<input type="hidden" id="isteyenSube" name="isteyenSube" value="'.$row[8].'">';
    echo '<input type="hidden" id="kayitMod" name="kayitMod" value="Görüntüle">'; 	
    echo '<input type="hidden" id="talepSn" name="talepSn" value="'.$row[0].'">';
    echo '<button type="submit"><img src="view-icon.png" height="20" width="20" title="Görüntüle==>'.$row[0].'"/></button>';    
    echo '</form>'; 
    
    //Sadece görevi oluşturan silebiliyor!
    if($silYetki=='t' && $row[6]=='f' &&  ($kullanici == $row[4] || $_SESSION["root"] == 1)){
        //Silme
        echo '<form action="#" method="post" class="inline">';// id="form3"
        echo '<input type="hidden" name="talepSn" value="'.$row[0].'">'; 
        echo '<input type="hidden" id="eylem" name="eylem" value="Sil">';           
        echo '<button type="submit" onclick="return silmeOnay();"><img src="Delete_Icon.png" height="20" width="20" title="Satınalma Sil==>'.$row[0].'" valign="middle"/></button>';        
        echo '</form>';
        }

    if($duzeltYetki=='t' && $row[6]=='f' && ($kullanici == $row[4] || $_SESSION["root"] == 1)){

        //Talep düzenle
        echo '<form action="talepOlustur.php" target = "_blank"  method="post" class="inline">';
        echo '<input type="hidden" name="talepSn" value="'.$row[0].'">';    
        echo '<input type="hidden" name="kayitMod" value="Düzenleme">';
        echo '<input type="hidden" name="talepOlusturan" value="'.$row[4].'">';          
        echo '<input type="hidden" name="yaz" value="1">';    
        echo '<button type="submit"><img src="EditIcon.png" height="20" width="20" title="Talep Düzenle==>'.$row[0].'" valign="middle"/></button>';
        //echo '<button type="submit">Hikaye</button>';
        echo '</form>';
        }


   //Belgesi varsa geliyor.

   if(belgesiVarMiTamami("talepBelgeler", $row[0])==true){
        
    //Belgeleri Göster
    echo '<form action="TalepBelgeleriAna.php"  target="_blank"  method="post"  class="inline">'; 
    echo '<input type="hidden" name="talepSn" value="'.$row[0].'">';            
    echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$row[0].'"/></button>';
    //echo '<button type="submit">Detay</button>';
    echo '</form>';
    }


    //Yazdır
    echo '<form action="TalepYazdir.php" target="_blank"  method="post"  class="inline">'; 
    echo '<input type="hidden" name="talepSn" value="'.$row[0].'">';        
    echo '<button type="submit"><img src="Print.jpg" height="20" width="20" title="Pdf Yap==>'.$row[1].'"/></button>';
    echo '</form>';
	
	
	if($gelenKriter=="3" && $silYetki=='t'){
		
		echo '<button type="button" class="inline" onclick="talepSilmeIstegiYap(this, '.$row[0].');"><img src="warning-icon.png" height="20" width="20" title="Talep Silme Onayı==>'.$row[0].'" valign="middle"/></button>';
		
		if(talepDetayAdet($row[0])>1 && $silYetki=='t' ){
			
			//echo '<button type="button" class="inline" onclick="talepDetaySilmeIstegiYap(this, '.$row[0].');"><img src="delete-file.png" height="20" width="20" title="Talep DETAY Silme Onayı==>'.$row[0].'" valign="middle"/></button>';
			echo '<form action="TalepDetaySilmeOnayi.php" method="post" target="_blank" class="inline">';// id="form3"
			echo '<input type="hidden" name="talepSn" value="'.$row[0].'">'; 
			//echo '<input type="hidden" id="eylem" name="eylem" value="Sil">';           
			echo '<button type="submit" onclick="this.style.display = \'none\';"><img src="delete-file.png" height="20" width="20" title="Talep DETAY Silme Onayı==>'.$row[0].'" valign="middle"/></button>';        
			echo '</form>';
			
			
			
		}
		
		
	}
	
	echo '<button type="button" class="inline" onclick="modalGosterCariler('.$row[0].');"><img src="traders.png" height="20" width="20" title="Cariler==>'.$row[0].'" valign="middle"/></button>';






    echo "</div>";
    echo "</td>";
    //Detay ve Hikaye Tuşları sonu

    echo "</tr>";
    //$sayac+=1;

}


//echo $sayac ;

echo "</table>";


//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>    
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';



pg_close($db);



echo "<br><br><br>";
require("saat2.inc");
echo "<br><br><br>";


?>

</body>
</html>
