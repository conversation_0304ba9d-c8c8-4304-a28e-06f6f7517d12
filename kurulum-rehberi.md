# MEG Talep Onaylama Sistemi Kurulum Rehberi

## <PERSON>ste<PERSON> Mimarisi

### MySQL Server (*************)
- `talepOnayla.js`: <PERSON><PERSON> gerçekleştiren ana script
- `mysql-trigger.sql`: MySQL trigger tanımları  
- `cron-checker.js`: Trigger'dan gelen istekleri i<PERSON>leyen cron job

### PostgreSQL Server (VPN Server)
- `polling-sync.js`: Mevcut senkronizasyon + yeni onay endpoint'i

## Kurulum Adımları

### 1. MySQL Server Kurulumu (*************)

#### 1.1. <PERSON><PERSON><PERSON><PERSON><PERSON> yerleştir
```bash
cd /path/to/meg/talep/
# Dosyalar zaten burada: talepOnayla.js, cron-checker.js, mysql-trigger.sql
```

#### 1.2. Node.js bağımlılıklarını yükle
```bash
npm install mysql2
```

#### 1.3. MySQL trigger'ı oluştur
```bash
mysql -u mehmet -p bormeg_msg < mysql-trigger.sql
```

#### 1.4. Cron job kurulumu
```bash
# Cron editörünü aç
crontab -e

# Bu satırı ekle (her dakika kontrol eder)
* * * * * cd /path/to/meg/talep && node cron-checker.js

# Cron'u restart et
sudo service cron restart
```

### 2. PostgreSQL Server Kurulumu (VPN Server)

#### 2.1. polling-sync.js zaten hazır
- `/talep/onay` endpoint'i mevcut
- Port 3080'de çalışıyor

#### 2.2. Servisi başlat
```bash
cd /path/to/meg/talep/
node polling-sync.js &
```

### 3. Konfigürasyon

#### 3.1. VPN Server IP'sini güncelle
`talepOnayla.js` dosyasında şu satırı düzenle:
```javascript
const POSTGRES_SERVER = {
    host: 'YOUR_VPN_SERVER_IP', // Gerçek VPN server IP'si
    port: 3080,
    endpoint: '/talep/onay'
};
```

## İşleyiş Akışı

### Normal Çalışma:
1. MySQL'de talep tablosunda `islemYapildi = 1` olur
2. Trigger tetiklenir ve `talep_onay_log` tablosuna kayıt ekler
3. Cron job (her dakika) bu kayıtları kontrol eder
4. `talepOnayla.js` çalıştırılır:
   - MySQL'de `talep_detay.onay = 1` yapar
   - MySQL'de `talep.dogrudan_onaylayan = 'eguler'` yapar
   - PostgreSQL server'a istek gönderir
5. PostgreSQL server aynı işlemleri kendi veritabanında yapar

### Log ve İzleme:
- `talep-onay.log`: talepOnayla.js işlem logları
- `cron-checker.log`: Cron job logları  
- `talep_onay_log` tablosu: Trigger ve işlem durumları

## Test Etme

### 1. Manuel test
```bash
# MySQL'de bir talebi işleme al
UPDATE talep SET islemYapildi = 1 WHERE sn = 12345;

# Log'ları kontrol et
tail -f talep-onay.log
tail -f cron-checker.log

# Veritabanını kontrol et
SELECT * FROM talep_onay_log ORDER BY id DESC LIMIT 5;
```

### 2. Script'i manual çalıştır
```bash
node talepOnayla.js 12345
```

### 3. Cron checker'ı manual çalıştır
```bash
node cron-checker.js
```

## Sorun Giderme

### 1. Cron çalışmıyor
```bash
# Cron servisini kontrol et
sudo service cron status

# Cron log'larını kontrol et  
tail -f /var/log/syslog | grep CRON
```

### 2. PostgreSQL bağlantı hatası
- VPN bağlantısını kontrol et
- Firewall ayarlarını kontrol et
- polling-sync.js servisinin çalıştığını kontrol et

### 3. MySQL bağlantı hatası
- Kullanıcı yetkileri
- Veritabanı bağlantı bilgileri
- mysql2 paketinin yüklü olduğunu kontrol et

## Güvenlik Notları
- Veritabanı şifreleri güvenli tutulmalı
- Log dosyaları periyodik olarak temizlenmeli
- HTTPS sertifikaları güncel tutulmalı