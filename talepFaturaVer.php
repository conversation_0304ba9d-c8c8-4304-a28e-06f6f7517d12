<?php
require('yetki.php');
$ekranKod = 65;
require('ekranYetkileri.php');

?>
<style style="text/css">
    table#tableMain2 thead { display:block; }
    table#tableMain2 tbody { height:300px; overflow-y:scroll; display:block; }

/*
    table#tableMain2 thead th .text {
            position:absolute;  
  */          
            }
</style>

<?php
require_once("PGConnect.php");
require_once("aktifSirket.php");
require_once("utf8MetinYap.php");


$gelenTalepDetaySn = $_POST['talepDetaySn'];




$sorguCumle = "SELECT F.BLKODU, F.FATURA_NO, F.TARIHI, C.TICARI_UNVANI, F.KAYIT_TARIHI, HR.KPB_KDVLI_TUTAR, F.TOPLAM_GENEL_KPB, F.SUBE_KODU from FATURA F 
                INNER JOIN FATURAHR HR  ON (HR.BLFTKODU = F.BLKODU)
                INNER JOIN CARI C ON (F.BLCRKODU = C.BLKODU)
                WHERE HR.EKBILGI_3 = '$gelenTalepDetaySn'
                AND F.SILINDI = 0;";



try {	
    $dbh = new PDO($aktifDB , 'sysdba', 'masterkey');
    
    $ret = $dbh->query($sorguCumle);
    //$adet =$dbh->query($sorguCumle)->fetchColumn();
    //$adet =$ret->fetchColumn();
    

    //$count = (int) $sil->fetchColumn();

    //echo $adet;

    

    /*
    //Bir şekilde bu çalışmadı
    //PG ile satır adedi almanın daha kısa bir yolu olmalı bence?!
    if($adet<1){
        echo '<p style="color:red">Fatura kaydı bulunamadı!</p>';
        pg_close($db);
        exit;
    }
    */
    
    
    //echo "xx";

    //Döngüye giriyor...

    echo '<table class= "sortable" valign="middle" id="tableMain2">';
    //echo "<thead>";
    echo "<tr>";
    echo '<th style="text-align:center;cursor:pointer;"><span class="text">BLKodu</th>';
    echo '<th style="text-align:center;cursor:pointer;"><span class="text">Fatura No</th>';
    echo '<th style="text-align:center;cursor:pointer;"><span class="text">Fatura Tarihi</th>';
    echo '<th style="text-align:center;cursor:pointer;"><span class="text">Kayıt Tarihi</th>';
    echo '<th style="text-align:center;cursor:pointer;"><span class="text">Ticari Ünvanı</th>';
    echo '<th style="text-align:center;cursor:pointer;"><span class="text">Detay Tutar</th>';
    echo '<th style="text-align:center;cursor:pointer;"><span class="text">Fatura Tutar</th>';
    echo '<th style="text-align:center;cursor:pointer;"><span class="text">Şube</th>';

    //echo '<th style="text-align:center;cursor:pointer;">Açık Ödenen</th>';
    //echo '<th style="text-align:center;cursor:pointer;">Fatura Detay Bilgisi</th>';
    echo "</tr>";
    //echo "</thead>";

    

    foreach($ret as $row) {
        echo "<tr>";
        echo "<td style='text-align:left'>". $row[0] . "</td>";   //BlKodu
        echo "<td style='text-align:left'>". $row[1] . "</td>";   //Fatura No
        echo "<td style='text-align:left'>". $row[2] . "</td>";   //Fatura Tarihi
        echo "<td style='text-align:left'>". $row[4] . "</td>";   //Kayıt Tarihi
        echo "<td style='text-align:left'>". utf8MetinYap($row[3]) . "</td>";   //Ünvan    

        echo "<td style='text-align:right'>". number_format($row[5], 2, ',', '.') . "</td>";   //Detay Tutar
        echo "<td style='text-align:right'>". number_format($row[6], 2, ',', '.') . "</td>";   //Fatura dip
        echo "<td style='text-align:left'>". utf8MetinYap($row[7]) . "</td>";   //Ünvan    
        echo "</tr>";
        
    }
    
    
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;
        $ret = null;
        die();
    }


/*
$count = pg_num_rows($ret); //Sorgu sonucunun satır sayısını veriyor.

if($count<1){
    echo '<p style="color:red">Hareket kaydı yok!</p>';
    pg_close($db);
    exit;
}
*/




$dbh = null;
$ret = null;

echo "</table>";

pg_close($db);


?>