[Unit]
Description=MEG Talep Polling Sync Service
After=network.target postgresql.service mysql.service
Requires=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/usr/src
ExecStart=/usr/bin/node /usr/src/polling-sync.js 30
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

Environment=NODE_ENV=production

RestartPreventExitStatus=SIGTERM
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target