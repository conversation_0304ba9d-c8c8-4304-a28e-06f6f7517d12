<?php
require('yetki.php');
$ekranKod = 175;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>



<style style="text/css">


div.dataTables_wrapper {
        width: "100%";
        margin: 0 auto;
    }



    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
        margin-left :10px 
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }


/** Modal pencere için */ 
    /* The Modal (background) */
    .modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.topics tr { line-height: 30px; }





</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">



function modalGoster1() {  
    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();
	
    xhttp.onreadystatechange = function() {
		if (this.readyState == 4 && this.status == 200) {
		  document.getElementById("icerik").innerHTML = this.responseText;
		  document.body.style.cursor  = 'default';
		  try{
		  document.getElementById("myInputHepsi2").focus();
		}catch{
		  //
		}
		}
    };

    
	document.getElementById('baslik').innerHTML ="Cari Listesi";
	xhttp.open("POST", "CariVerAkinsoftdan.php", true);
	xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xhttp.send();
    

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}


function formGuncelle(){    
    document.getElementById('postlinkTalepBolme').submit();
}


function modalGoster(gelenModelKod) {  
    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
      document.body.style.cursor  = 'default';
    }
    };

    if(gelenModelKod==0){
        document.getElementById('baslik').innerHTML ="Talep Listesi";
        xhttp.open("POST", "TalepListesiVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}

/*
$(document).ready(function() {
    $('#example').DataTable( {
        "scrollY": 200,
        "scrollX": true
    } );
} );

*/

function searchTableColumns(gelenTablo, gelenAlan,gelenHedefAlan) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      var bulunanKayitAdet = 0; //Kayıt sayısı için
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
            bulunanKayitAdet++;
        } else {
            tr[i].style.display = "none";
        }
      }   

      //Kayıt adedi yazılıyor.

      if(bulunanKayitAdet > 0){
          document.getElementById(gelenHedefAlan).innerHTML = bulunanKayitAdet + " Adet kayıt listelendi"; 
      }else{
          document.getElementById(gelenHedefAlan).innerHTML = ""; 
      }





}


function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}


function girisKontrol(sayac, maxDeger){

    if(parseFloat(document.getElementById("bolunecekMiktar"+sayac).value)>parseFloat(maxDeger)){

        document.getElementById("bolunecekMiktar"+sayac).value = maxDeger;

    }else if(parseFloat(document.getElementById("bolunecekMiktar"+sayac).value)<0){

        document.getElementById("bolunecekMiktar"+sayac).value = 0;

    }

}





function talepParcala(sender, sayac){
	alert (sayac);
	return false;
}


function talepBol(sender, sayac){

    var kopyaVarMi = 0;


    for (let index = 1; index < sayac+1; index++) {
        
        try {
            
            if(parseFloat(document.getElementById("bolunecekMiktar"+index).value)>0){
    
                kopyaVarMi = 1;
            }
                   
        } catch (error) {
            
            kopyaVarMi = 0;
            
        }

        
    }

    if(kopyaVarMi == 0){
        alert("En az bir bölünecek miktar mutlaka olmalıdır!");
        return false;
    }




    if(!ConfirmIslem("Talep bölünerek çoğaltılacaktır!\nDikkat! Geridönüşü mümkün değildir!")) return false;


    sender.style.display = "none";

    //Kum saati
    document.body.style.cursor  = 'wait';

    var table = document.getElementById("tableMain");
    var gidenDeger = "";
    var kopyalanacakAdet = 0;


    for (var i = 1, row; row = table.rows[i]; i++){ 

        kopyalanacakAdet = parseFloat(document.getElementById("bolunecekMiktar"+i).value);       

        if(kopyalanacakAdet>0){
            gidenDeger = gidenDeger + row.cells[0].innerText + ";" + row.cells[2].innerText + ";" + document.getElementById("bolunecekMiktar"+i).value + "#";            
        }

    }


    gidenDeger = gidenDeger.slice(0, -1);

    document.getElementById("talepBolmeListesi").value = gidenDeger; 

    if(gidenDeger=="") return false;else return true;

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}



function veriKontrol(sayac){
    
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edildi.");
		return false;
    }    

    var kisilerListesi = "";

    var i=1;
        for (i = 1; i <= sayac; i++) {               
                
                if(document.getElementById("kisiler-" + i).checked == true){
                    kisilerListesi+=document.getElementById("kisiler-" + i).value+';';
                }
        } 

    kisilerListesi = kisilerListesi.slice(0, -1);


    //console.log(kisilerListesi);

    if(kisilerListesi==""){
        alert("Görevlendirme için en az bir kişi seçilmelidir!\nİşlem iptal edildi.");
		return false;		
		
	}

    document.getElementById("kisiBilgileri").value = kisilerListesi;
	return true;	


}





function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}


function veriTransfer(gelenHizmetKod, gelenHizmetAd){
    document.getElementById("talepKod").value = gelenHizmetKod;
    document.getElementById("talepAd").innerHTML = gelenHizmetAd;  
    document.getElementById("talepAd2").value = gelenHizmetAd;  
    
    modalKapat();  
    formGuncelle();
}





function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>



<?php

echo '<br><h1 style="text-align:center;">Talep Bölme</h1>';


function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}





function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}



function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}



function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
    }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}


function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "***";
}

function renklendirme($gelen){
    if($gelen=='t') return "color:green;";else return "color:red;";
}



function isChecked($gelenGizliMi){

    if ($gelenGizliMi == 'TRUE') $donenDeger = " checked"; else $donenDeger = ""; 

    return $donenDeger;
    
}



function oncekiYetkileriSil($gelenKullaniciKod){

    global $db;

    $donenDeger=false;

    $sorgu="DELETE FROM yetkiler WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if($ret){
        $donenDeger=true;
    }else{
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";     
   }    

   return $donenDeger; 
}



function talepHareketiVarMi($gelenTalepSn){
	
	global $db;

    $donenDeger=true;

    $sorgu="SELECT COUNT(sn) FROM talep_hareket WHERE talep_sn = $gelenTalepSn;";	

	$ret = pg_query($db, $sorgu);
	
	
	if(!$ret){
      echo pg_last_error($db);
	  echo "Tabloya ulaşılamadı!";      
    }
	
	while($row = pg_fetch_row($ret)){
		
		if($row[0]>0){
			$donenDeger = true;			
		}else{
			$donenDeger = false;
		}	   
		
	}	
	
	return $donenDeger;   
	
	
}


//************************************************************************** */
//************************************************************************** */
//************************************************************************** */
//************************************************************************** */
//************************************************************************** */
//************************************************************************** */
//************************************************************************** */
//************************************************************************** */



//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");



$gelenTalepSn = $_POST['talepKod'];
$gelenTalepAd = $_POST['talepAd'];
$gelenTalepBolmeListesi = $_POST['talepBolmeListesi'];
$gelenIslemTip = $_POST['islemTip'];




if($gelenIslemTip == "Kopyala"){

    //echo "$gelenTalepBolmeListesi<br>$gelenTalepSn<br>";

    $sorguCumle = "INSERT INTO talep(konu, detay, talep_eden, kullanici, sube, gizli, hizmet_kod, stok_kod, cari_kod) 
					SELECT konu, 'Kopyalandı-$gelenTalepSn', talep_eden, kullanici, sube, gizli, hizmet_kod, stok_kod, cari_kod 
					 FROM talep WHERE SN = $gelenTalepSn 
					RETURNING sn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    }


    //Dönen SN alınıyor. 

    while($row = pg_fetch_row($ret)){
        $kopyalananSn = $row[0]; 
    }

    //echo $kopyalananSn; 

    //Kopyalama işlemi...

    $dilimler = explode("#", $gelenTalepBolmeListesi);


    foreach ($dilimler as $deger){

        $dilimler2 = explode(";", $deger);

        $kaynakDetaySn = $dilimler2[0];
        $kaynakMiktar = $dilimler2[1];
        $hedefMiktar = $dilimler2[2];

        //Kopya SQL oluşturuluyor.

        $sorguCumle = "INSERT INTO talep_detay(talep_sn, aciklama, miktar, birim, termin_zaman, onay, onay_zaman, isleme_alindi, islem_zaman, kullanici, islem_kullanici, ilgili_alim, onay_kullanici, hizmet_kod, stok_kod, teslim_kabul)
                       SELECT $kopyalananSn, aciklama, $hedefMiktar, birim, termin_zaman, onay, onay_zaman, isleme_alindi, islem_zaman, kullanici, islem_kullanici, ilgili_alim, onay_kullanici, hizmet_kod, stok_kod, teslim_kabul 
					   FROM talep_detay WHERE sn = $kaynakDetaySn;";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            exit;
        }

        //Kaynaktaki değişecek!

        $sorguCumle = "UPDATE talep_detay SET miktar = miktar - $hedefMiktar WHERE sn = $kaynakDetaySn;";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            exit;
        }

        //echo $sorguCumle . "<br><br>";

        echo '<script type="text/javascript"> alert("Kopyalama işlemi tamamlandı. Kopyalanmış Talep No: '.$kopyalananSn.'");</script>';







    }



    $gelenTalepSn = "";
    $gelenTalepAd = "";
    $gelenTalepBolmeListesi = "";
    $gelenIslemTip = "";
    
}



echo '<form action="#" id="postlinkTalepBolme" method="post">';

echo '<table border ="0">';
echo "<tr>";
echo "<td>Bölünecek Talep Sn:</td>";
echo "<td>";
echo '<input type="text" id="talepKod" onclick="modalGoster(0);" style="background-color:#fff333;" readonly size="4" name="talepKod" value="'.$gelenTalepSn.'"><label id="talepAd">&nbsp;'.$gelenTalepAd.'</label>';
echo "</td>";
echo "<tr>";




echo "</table>";
echo "<br>";

echo '<input type="hidden" id="talepAd2" name="talepAd" value="'.$gelenTalepAd.'">';
echo bosluk . bosluk .'<input type="submit" name="tusSorgula" value="Sorgula">';
//echo '<input type="submit" name="tusKopyala" class="tusBosluk" value="Kopyala" onclick="return ConfirmIslemx(\'Kopyalama işlemi yaplacaktır!\')">';
echo '</form>';
echo "<br><br>";




echo '<table class= "sortable" valign="middle" id="tableMain">';
echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
echo '<th style="text-align:center;cursor:pointer;">Sn</th>';
echo '<th style="text-align:center;cursor:pointer;">Açıklama</th>';
echo '<th style="text-align:center;cursor:pointer;">Miktar</th>';
echo '<th style="text-align:center;cursor:pointer;">Birim</th>';
echo '<th style="text-align:center;cursor:pointer;">Bölünecek Miktar</th>';
echo '<th style="text-align:center;cursor:pointer;">Talebi Açan</th>';  
echo '<th style="text-align:center;cursor:pointer;">Satınalmacı</th>';

//echo '<th style="text-align:center;cursor:pointer;">Tamamlanma Zaman</th>';
//echo "<th style=\"text-align:center\">İşlem</th>";
echo "</tr>";





if ($gelenTalepSn==""){
    goto cikis;
}


$sorguCumle = "SELECT sn, aciklama, miktar, birim, kullanici, islem_kullanici 
                    FROM talep_detay 
                    WHERE talep_sn = $gelenTalepSn AND tamamlandi = false AND onay = TRUE
                    ORDER BY sn;";


$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
}



//----------------------------------
//----------------------------------
//Kaynak kullanıcı hakları

 
$sayac = 0; 

while($row = pg_fetch_row($ret)){  
     
    $sayac+=1;

    echo "<tr class='item' >";
    echo "<td style='text-align:center;'>". $row[0] . "</td>"; //Program Kod
    echo "<td style='text-align:left;'>". $row[1] . "</td>"; //Açıklama
    echo "<td style='text-align:center;'>". $row[2] . "</td>"; //Miktar
    echo "<td style='text-align:center;'>". $row[3] . "</td>"; //Birim

    echo "<td style='vertical-align: middle;text-align:center'>";

    $maxDeger = $row[2]-1;

    if($maxDeger>0){
        
        echo '<input type="number" id="bolunecekMiktar'.$sayac.'" name="bolunecekMiktar" maxlength="5" size="10" min="0" max="'. $maxDeger. '" style="text-align:right" value="0" ';        
        echo 'title="bolunecekMiktar'.$sayac.'" step="1" oninput="girisKontrol('. $sayac. ', '. $maxDeger .');" />';		

    }


    echo "</td>"; //Program Kod
    echo "<td style='text-align:center;'>". $row[4] . "</td>"; //Kullanıcı
    echo "<td style='text-align:center;'>". $row[5] . "</td>"; //Satınalmacı
	
	
	
	
	

    

    echo "</tr>";

    

}




cikis: 

echo "</table>";


//Talep Bölme

if($sayac>0 && $yeniYetki=='t'){

    echo "<br><br><br>";    
    echo '<form action="#" method="post" class="inline">';
    echo '<input type="hidden" id="talepKod2" name="talepKod" value="'.$gelenTalepSn.'">';
    echo '<input type="hidden" id="islemTip" name="islemTip" value="Kopyala">';
    echo '<input type="hidden" id="talepBolmeListesi" name="talepBolmeListesi" value="">';
    echo bosluk.'<button type="submit" onclick="return talepBol(this, '.$sayac.');">Talebi Böl</button>';
    echo '</form>';

}











//En alta eklenmesi gereken kod

//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';




pg_close($db);


?>

</body>
</html>