<?php
require('yetki.php');
$ekranKod = 58;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<style style="text/css">
    .inline {
        display: inline;
        margin-left :10px 
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }
</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">


function silmeOnay(){
    if (confirm("Kayıt Silinecektir! İşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sayac){
    
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edildi.");
		return false;
    }    

    var kisilerListesi = "";

    var i=1;
        for (i = 1; i <= sayac; i++) {               
                
                if(document.getElementById("kisiler-" + i).checked == true){
                    kisilerListesi+=document.getElementById("kisiler-" + i).value+';';
                }
        } 

    kisilerListesi = kisilerListesi.slice(0, -1);


    //console.log(kisilerListesi);

    if(kisilerListesi==""){
        alert("Görevlendirme için en az bir kişi seçilmelidir!\nİşlem iptal edildi.");
		return false;		
		
	}

    document.getElementById("kisiBilgileri").value = kisilerListesi;
	return true;	


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>



<?php

echo '<br><h1 style="text-align:center;">Talep Belgeleri</h1>';


function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn='$gorevSn';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}

function talepKilitliMi($gelenTalepSn){

    //Tamamlandı mı?

    global $db;

    $donenDurum='f';

    $sorgu="SELECT tamamlandi FROM talep WHERE sn=$gelenTalepSn;";

	$ret = pg_query($db, $sorgu);

    $row = pg_fetch_row($ret);

    //echo $row[0] . "-". $row[1]."-".$row[2];

    while($row = pg_fetch_row($ret)){
        $donenDurum = $row[0];    
    }


    return $donenDurum;
}


function talepiUzerineAlan($gelenTalepDetaySn){

    //Tamamlandı mı?

    global $db;

    $donenDeger='';

    $sorgu="SELECT islem_kullanici FROM talep_detay WHERE sn=$gelenTalepDetaySn;";

	$ret = pg_query($db, $sorgu);

    //$row = pg_fetch_row($ret);

    //echo $row[0] . "-". $row[1]."-".$row[2];

    while($row = pg_fetch_row($ret)){
        $donenDeger = $row[0];    
    }


    return $donenDeger;
}

//********************************************************************************************** */
//********************************************************************************************** */
//********************************************************************************************** */
//********************************************************************************************** */
//********************************************************************************************** */
//********************************************************************************************** */
//********************************************************************************************** */
//********************************************************************************************** */
//********************************************************************************************** */


//Bağlantı Yapılıyor... 
require("PGConnect.php");

$gelenTalepSn = $_POST['talepSn'];



$klasor = "/mnt/talepBelgeler/".$gelenTalepSn."/";

$di = new RecursiveDirectoryIterator($klasor);






//$dosyalar = scandir($klasor);

foreach (new RecursiveIteratorIterator($di) as $filename => $file) {
    //echo $filename . ' - ' . $file->getSize() . ' bytes <br/>';
    $sayac+=1;
}


echo "<br><br>";    


echo '<table class= "hoverTable" valign="middle" id="tableMain">';
echo "  <tr>";
echo "    <th style='text-align:center'>S/N</th>";
echo "    <th style='text-align:center'>Dosya Adı</th>";
echo "    <th style='text-align:center'>Kayıt Tarihi</th>";
echo "    <th style='text-align:center'>İşlem</th>";
echo "  </tr>";

$sayac = 1;

foreach (new RecursiveIteratorIterator($di) as $dosya => $file){

    if (!is_dir($dosya)) {

        echo "<tr title='Belge'>";
        echo "<td style='text-align:center'>". $sayac . "</td>"; 

        //echo basename($dosya)."<br>";
        
        $dosyaAd = basename($dosya) ;    //Sadece dosya ismi. Yolu olmadan!
        $mod_date=date("d-m-Y H:i:s", filemtime($dosya));

        
        echo '<td>'.basename($dosya).'</td>';
        echo '<td style="text-align:center">'.$mod_date.'</td>';
        echo "<td style='vertical-align: middle; text-align:center'>";
            echo "<div style='margin: 0 auto; width: 100px'>";



            //Detay Göster
            echo '<form action="GorevBelgeGoster.php"  method="post"  class="inline">'; 
            //echo '<input type="hidden" name="gorevSn" value="'.$row[6].'">';
            echo '<input type="hidden" name="dosyaAd" value="'.$dosya.'">';            
            echo '<button type="submit"><img src="view-icon.png" height="20" width="20" title="Belgelere Git==>'.$dosyaAd.'"/></button>';            
            echo '</form>';



        echo "</div>";
        echo '</td>';


        echo "</tr>";
        $sayac += 1;

    }
    
}

echo'</table>';



echo '<br><br><br>';
echo "<center>";
echo '<form action="" method="post" onclick="self.close()">';
echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';    
//echo '<input type="hidden" id="tarih1" name="tarih1" value="'.$gelenTarih1.'">';
//echo '<input type="hidden" id="tarih2" name="tarih2" value="'.$gelenTarih2.'">';
echo '<input type="submit" value="Kapat" style="width:200px;">';
echo '</form>';







pg_close($db);

//echo "<hr><br><br><b>Dip Toplam :$toplam TL </b><hr>";   

//echo "<br>";
//echo "***Tüm geçmiş siparişleriniz listelenmiştir.";

?>



</body>
</html>
