<?php
require('yetki.php');
$ekranKod = 65;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>
<script src="jquery-3.5.1.js"></script>

<style style="text/css">

input {
    /*width: 100%;*/
    box-sizing: border-box;
}


    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}


.sortable th{ 
        position:sticky;
        /*position: -webkit-sticky;*/
        top: 0;
        background: green;/*#727272;*/
        color: white; /*#c5c5c5;*/
        z-index: 0;

		/*padding:7px; border: #4e95f4 1px solid;*/
		/*#4e95f4 orjinal hali*/ 
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }


    
/** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}










</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">


function degerKontrol(sender){
	
	//alert(sender.id);
	
	switch(sender.id){
		case "yeniBanka":
			if(sender.value==0 || sender.value==""){
				document.getElementById("yeniBankaKod").value = "";				
			}
			//alert("Banka");
		break;
		case "yeniKk":
			if(sender.value==0 || sender.value==""){
				document.getElementById("yeniKkKod").value = "";				
			}			
		break;
		
		
	}
	
	
	
	
}





function modalGoster(gelenModelKod) {  

    document.body.style.cursor  = 'wait';  
	
    var xhttp = new XMLHttpRequest();
	
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
      document.body.style.cursor  = 'default';
	  
	  try{
		  document.getElementById("myInputHepsi2").focus();
		}catch{
		  //
		}	  
	  
    }
	
    };
	
    if(gelenModelKod==0){
        document.getElementById('baslik').innerHTML ="Cari Listesi";
        xhttp.open("POST", "CariBakiyeVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");

        

        
		
		var gidecekParametre = "ekCariSecenekleriGizle=1";

        xhttp.send(gidecekParametre);
        
    }else if(gelenModelKod==1){
        document.getElementById('baslik').innerHTML ="Talepler Listesi";
        xhttp.open("POST", "TalepIlgiVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        cariKod = document.getElementById("cariKod").value;
        //console.log(cariKod);
        xhttp.send("cariKod=" + cariKod);
    }else if(gelenModelKod==2){
        document.getElementById('baslik').innerHTML ="Kredi Kartları";
        xhttp.open("POST", "KrediKartlariVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==3){
        document.getElementById('baslik').innerHTML ="Kredi Ödemeleri";
        xhttp.open("POST", "KrediOdemeleriVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==4){
        document.getElementById('baslik').innerHTML ="Hakedişler";
        xhttp.open("POST", "HakedisleriVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        cariKod = document.getElementById("cariKod").value;
        xhttp.send("cariKod=" + cariKod);
    }else if(gelenModelKod==5){
        document.getElementById('baslik').innerHTML ="Onaylanmış Ödeme Emirleri";
        xhttp.open("POST", "OdemeEmriVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        hedefFunc = "veriTransfer6";
        xhttp.send("hedefFunc="+ hedefFunc);
		
    }else if(gelenModelKod==6){ //Banka ver
		
		
		if(document.getElementById('yeniBanka').value>0){
			
			document.getElementById('baslik').innerHTML ="Banka Seçim";
			xhttp.open("POST", "BankaVer.php", true);
			xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
			xhttp.send("hedefFonksiyon=veriTransferBanka");	
			
		}else{
			alert("Banka ödeme tutarı olmalıdır!");
			document.body.style.cursor  = ''; 
			return false;
		}
        
    }else if(gelenModelKod==7){
		document.getElementById('baslik').innerHTML ="Doğrudan Talepler Listesi";
		xhttp.open("POST", "TalepDogrudanListesiVer.php", true);
		xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
		
		var cariKod=document.getElementById('cariKod').value;
		
		if(cariKod==""){
			alert("Cari kod mutlaka seçilmelidir!");
			document.body.style.cursor  = '';  
			return false;
		}
		
		xhttp.send("cariKod=" + cariKod+"&hedefTransfer=variTransferTalepKod&sadeceOnaylilarGorunsun=1");
		
		
	}else if(gelenModelKod==8){ //KK ver
		
		
		if(document.getElementById('yeniKk').value>0){
			
			document.getElementById('baslik').innerHTML ="Kredi Kartları";
			xhttp.open("POST", "KrediKartlariVer.php", true);
			xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
			xhttp.send("hedefFonksiyon=veriTransferKK");	
			
		}else{
			alert("Kredi Kartı ödeme tutarı olmalıdır!");
			document.body.style.cursor  = ''; 
			return false;
		}
	
	
		
		
		
		
	}

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}


function modalGoster1() {  
    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();
	
    xhttp.onreadystatechange = function() {
		if (this.readyState == 4 && this.status == 200) {
		  document.getElementById("icerik").innerHTML = this.responseText;
		  document.body.style.cursor  = 'default';
		  try{
		  document.getElementById("myInputHepsi2").focus();
		}catch{
		  //
		}
		}
    };

    
	document.getElementById('baslik').innerHTML ="Cari Listesi";
	xhttp.open("POST", "CariVerAkinsoftdan.php", true);
	xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xhttp.send();
    

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}


function variTransferTalepKod(cariKod){
	
	document.getElementById("talepSn").value=cariKod;   
	//document.getElementById("talepBakiye").value=0;   
    modalKapat();
	
}



function veriTransferKK(gelenKKNo, gelenBankaAd, gelenBakiye, gelenHesapKesimTarihi, kkBlKodu, kkTanim, kkBilgi){//Kredi kartı

document.getElementById("yeniKkKod").value = kkBilgi;
//ekraniGizle();
modalKapat();     

}


function veriTransferBanka(gelenBanka){
    //Dönen değeri yazıyor...    
    document.getElementById("yeniBankaKod").value=gelenBanka;	
    modalKapat(); 
}

function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
    document.body.style.cursor  = 'default';    
}



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
    document.body.style.cursor  = 'default';
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}



function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}

function searchTableColumns(gelenTablo, gelenAlan,gelenHedefAlan) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      var bulunanKayitAdet = 0; //Kayıt sayısı için
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
            bulunanKayitAdet++;
        } else {
            tr[i].style.display = "none";
        }
      }   

      //Kayıt adedi yazılıyor.

      if(bulunanKayitAdet > 0){
          document.getElementById(gelenHedefAlan).innerHTML = bulunanKayitAdet + " Adet kayıt listelendi"; 
      }else{
          document.getElementById(gelenHedefAlan).innerHTML = ""; 
      }





}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}



function tutarKontrol(){

    var secim = document.getElementById("cariKod").value;
    console.log(secim);

    var odemeMiktar = 0.0;

    odemeMiktar = parseFloat(document.getElementById("tutar").value);
    
    if(odemeMiktar<0) document.getElementById("tutar").value = 0;

    var bakiyeMiktar = 0.0; 
    bakiyeMiktar = parseFloat(document.getElementById("cariBakiye").value);

    var kkBakiye = 0.0; 
    kkBakiye = parseFloat(document.getElementById("kkBakiye").value);

    var krediBakiye = 0.0; 
    krediBakiye = parseFloat(document.getElementById("krediKalanTutar").value);


    switch(secim) {
    case "KREDİ KARTLARI":

        if(odemeMiktar>kkBakiye && !document.getElementById("avans").checked ){
            document.getElementById("p1").innerHTML = "Ödeme Tutarı KK bakiyesinden büyük!!!";
        }else{
            document.getElementById("p1").innerHTML = "";
        }
        
        break;
    case "KREDİ ÖDEMESİ":

        if(odemeMiktar>krediBakiye && !document.getElementById("avans").checked ){
            document.getElementById("p1").innerHTML = "Ödeme Tutarı Kredi Ödemesi bakiyesinden büyük!!!";
        }else{
            document.getElementById("p1").innerHTML = "";
        }
        
        break;
    case "TOPLU MAAŞ":
        // code block
        break;
    default:        
        if(odemeMiktar>bakiyeMiktar && !document.getElementById("avans").checked ){
            document.getElementById("p1").innerHTML = "Ödeme Tutarı bakiyeden büyük!!!";
        }else{
            document.getElementById("p1").innerHTML = "";
        }
    } 












}

function tumDivleriKapat(){
    //Tüm seçeneğe ba<ğlı DIV leri gizler!
    document.getElementById("cariBolumu").style.display = "none";
    document.getElementById("talepBolumu").style.display = "none";
    document.getElementById("kkBolumu").style.display = "none";
    document.getElementById("krediBolumu").style.display = "none";
    document.getElementById("hizmetBolumu").style.display = "none";

}

function veriTransfer(gelenCariKod, gelenCariAd, gelenBakiye){ //Cari liste seçili ise bu rutin çalışacak
    document.getElementById("cariKod").value = gelenCariKod;
    document.getElementById("cariAd").innerHTML = gelenCariAd;
    //document.getElementById("cariBakiye").value = roundNumber(gelenBakiye,2);
    //document.getElementById("tutar").value = roundNumber(gelenBakiye,2);


    

    modalKapat();     
}

function veriTransfer2(gelenCariKod, gelenCariAd, gelenBakiye){ //Talep seçimi için
    document.getElementById("talepKod").value = gelenCariKod;
    document.getElementById("talepKonu").innerHTML = gelenCariAd;    
    document.getElementById("talepBakiye").value = gelenBakiye;
    document.getElementById("tutar").value = gelenBakiye;

    modalKapat();     
}

function veriTransfer3(gelenKKNo, gelenBankaAd, gelenBakiye, gelenHesapKesimTarihi, kkBlKodu){//Kredi kartları seçimi
    document.getElementById("kkNo").value = gelenKKNo;
    document.getElementById("kkAd").innerHTML = gelenBankaAd; 

    var d = new Date();
    hesapKesim = today = d.getFullYear()+"-"+("0"+(d.getMonth()+1)).slice(-2)+"-"+("0"+gelenHesapKesimTarihi).slice(-2);

    //console.log(hesapKesim);

    document.getElementById("hesapKesimTarih2").value = hesapKesim;
    document.getElementById("kkBlKodu").value = kkBlKodu;
    

    document.getElementById("tutar").value = gelenBakiye;
    document.getElementById("kkBakiye").value = roundNumber(gelenBakiye,2);

    document.getElementById("aciklama").value = gelenKKNo + " " + gelenBankaAd ;

    //Temizlenmesi gereken alanları siliniyor.
    document.getElementById("talepKod").value = "";
    document.getElementById("talepKonu").innerHTML = "";    
    document.getElementById("talepBakiye").value = "";


    modalKapat();     
}


function veriTransfer4(gelenKKNo, gelenBankaAd, gelenBakiye, kalanTutar){//Kredi Ödemeleri seçimi
    document.getElementById("krediNo").value = gelenKKNo;
    document.getElementById("krediAd").innerHTML = gelenBankaAd; 
    document.getElementById("aciklama").value = gelenBankaAd ;
    document.getElementById("krediBakiye").value = roundNumber(gelenBakiye, 2);
    document.getElementById("tutar").value = roundNumber(kalanTutar, 2);
    document.getElementById("krediKalanTutar").value = roundNumber(kalanTutar, 2);


    console.log("Gelen Bakiye:" + gelenBakiye);
    console.log("Gelen Tutar:" + kalanTutar);
    console.log("Gelen kkno:" + gelenKKNo);
    console.log("**************************");
    


    //Temizlenmesi gereken alanları siliniyor.
    document.getElementById("talepKod").value = "";
    document.getElementById("talepKonu").innerHTML = "";    
    document.getElementById("talepBakiye").value = "";

    document.getElementById("kkNo").value = "";
    document.getElementById("kkAd").innerHTML = "";    
    document.getElementById("kkBakiye").value = "";
    


    modalKapat();     
}


function veriTransfer5(gelenHakedisKod, gelenHakedisAd, gelenHakedisBakiye, gelenSube){ //Talep seçimi için
    document.getElementById("hakedisKod").value = gelenHakedisKod;
    document.getElementById("hakedisKonu").innerHTML = gelenHakedisAd;    
    document.getElementById("hakedisBakiye").value = gelenHakedisBakiye;
    document.getElementById("tutar").value = gelenHakedisBakiye;
    document.getElementById("sube").value = gelenSube;
    //console.log(gelenSube);

    

    modalKapat();     
}


function veriTransfer6(gelenSn){ //Hedef Ödeme Emri seçimi için
    document.getElementById("odemeEmriHedefSn").value = gelenSn;
	ekraniGizle();    

    modalKapat();     
}



function veriKontrol(sender){
	
	//Veriler değişmiş mi kontrolü
	var talepHareketSn = document.getElementById("talepHareketSn").value; 
	var eskiNakit = document.getElementById("eskiNakit").value;
	var eskiBankadan = document.getElementById("eskiBankadan").value;
	var eskiEvrakla = document.getElementById("eskiEvrakla").value;
	var eskiKkOdenen = document.getElementById("eskiKkOdenen").value;
	var eskiAcikOdenen = document.getElementById("eskiAcikOdenen").value;
	
	var eskiKkKod = document.getElementById("eskiKkKod").value;
	var eskiBankaKod = document.getElementById("eskiBankaKod").value;
	
	var eskiKdv = document.getElementById("eskiKdv").value;
	var eskiTevkifat = document.getElementById("eskiTevkifat").value;
	
	//------
	var yeniNakit = document.getElementById("yeniNakit").value;
	var yeniBanka = document.getElementById("yeniBanka").value;
	var yeniEvrak = document.getElementById("yeniEvrak").value;
	var yeniKk = document.getElementById("yeniKk").value;
	var yeniAcik = document.getElementById("yeniAcik").value;
	var yeniKdv = document.getElementById("yeniKdv").value;
	var yeniTevkifat = document.getElementById("yeniTevkifat").value;
	var yeniKkKod = document.getElementById("yeniKkKod").value;
	var yeniBankaKod = document.getElementById("yeniBankaKod").value;
	var aciklama = document.getElementById("aciklama").value;
	
	var yeniKdv = document.getElementById("yeniKdv").value;
	var yeniTevkifat = document.getElementById("yeniTevkifat").value;
	
	
	//------
	var silinsin = document.getElementById("silinsin").checked;
	//------
	
	if(talepHareketSn==""){
		alert("Talep Hareket Kodu boş geçilemez!");
		return false;
	}
	
	
	
	
	if(silinsin){
		
		if(aciklama==""){
			alert("Silme sebebi mutlaka girilmelidir!\nİşlem iptal edilecek.");
			return false;
		}
		
		if(!confirm("Doğrudan harcamanın silinmesi istenecektir!\Emin misiniz?")){
			return false;
		}
		
	}else if(eskiNakit==yeniNakit && 
			 eskiBankadan==yeniBanka && 
			 eskiEvrakla==yeniEvrak && 
			 eskiKkOdenen==yeniKk &&
			 eskiAcikOdenen==yeniAcik &&
			 eskiKdv==yeniKdv &&
			 eskiTevkifat==yeniTevkifat &&
			 eskiKkKod==yeniKkKod &&
			 eskiBankaKod==yeniBankaKod
			 ){
		alert("Hiçbir değişiklik yapılmamış! Kayıt yapılamaz!");
		return false;
		
	}else if( (yeniBanka>0 && yeniBankaKod=="" ) || (yeniKk>0 && yeniKkKod=="" )){
		
		alert("Banka veya KK bilgisi mutlaka girilmelidir!");
		return false;
		
		
	}
	


	

    if(aciklama==""){
        alert("Değişiklik sebebi mutlaka girilmelidir!\nİşlem iptal edilecek.");
        return false;
    }
	
	if (!confirm("Değişiklik talebi oluşturulacaktır!\nOnaylıyor musunuz?")){
          return false;
    }	
	
	
	sender.style.display = "none";
	
	//return false;
	
	//Buradan sonra kayıt işlemi yapılıyor.
	
	$.ajax({
        method: "POST",
        url: "TalepHarcamaDegisiklikTalepKaydet.php",
        async: false,
        data: { talepHareketSn:talepHareketSn,
			    nakit: yeniNakit, 
                bankadan: yeniBanka, 
                evrakla: yeniEvrak,
				kk:yeniKk,
				acik: yeniAcik,                		
				kkKod : yeniKkKod,
				bankaKod : yeniBankaKod,
				kdv : yeniKdv,
				tevkifat : yeniTevkifat,
				silinecek : silinsin,
				aciklama : aciklama								
              }        
        })
        .done(function( response ) { 

            response = response.trim();
            //console.log(response);

            if(response=="Okkk"){
                alert("Değişiklik talebi başarı ile oluşturuldu!");
				ekraniGizle();
				return true;
            }else{
				alert(response);
				sender.style.display = "block";
				return false;
            }

                  
            
        });
	
	
	
    


}


function bilgiTemizle(){
	document.getElementById("bankaKkBilgileri").value="";
	
}

function ekraniGizle(){
	
	try{
		document.getElementById("tableMainIlk").style.display = "none";
		document.getElementById("tableMainYeni").style.display = "none";
		document.getElementById("kaydetTusu").style.display = "none";
		console.log("Ekran Gizle");
	}catch(err) {
		//Do Nothing
	}
	
}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function cariKodYaz(gelenKod, gelenAd, gelenBakiye){

    //document.getElementById("cariKod").innerHTML=gelenMesaj;
    document.getElementById("cariKod").value = gelenKod;
    document.getElementById("cariAd").innerHTML = gelenAd;
    document.getElementById("cariBakiye").value = gelenBakiye;

    //var x = document.getElementById("cariDIV");
    //x.style.display = "none";


    //console.log(gelenMesaj);
    //alert(gelenMesaj);
    
}

function divAc(gelenDiv) {  
  var x = document.getElementById(gelenDiv);
  x.style.display = "block";
  console.log( gelenDiv + ' DIV açıldı');
}

function divKapat(gelenDiv) {  
  var x = document.getElementById(gelenDiv);
  x.style.display = "none";
  console.log( gelenDiv + ' DIV Kapandı');
}

function odemeKaynakTikle(){

    var secim = document.getElementById("odemeKaynak").value;

    if(secim=="Talep"){
        divAc("talepBolumu");
        divKapat("hizmetBolumu");
        divAc("subeBolumu");

        //Hizmet Değerleri sıfırlıyor.
        document.getElementById('hakedisKod').value="";
        document.getElementById('hakedisBakiye').value="0";
        document.getElementById('hakedisKonu').value="";
    }else{
        divKapat("talepBolumu");
        divAc("hizmetBolumu");
        divKapat("subeBolumu");
        //Talep Değerleri sıfırlıyor.

        document.getElementById('talepKod').value="";
        document.getElementById('talepBakiye').value="0";
        document.getElementById('talepKonu').value="";
    }
    

}

function ekstreBorcuGuncelle(){
    var hesapKesim1 = document.getElementById("hesapKesimTarih1").value;
    var hesapKesim2 = document.getElementById("hesapKesimTarih2").value;
    var kkBlKodu = document.getElementById("kkBlKodu").value;
    var kkKartNoX = document.getElementById("kkNo").value;

    if(kkKartNoX==""){
        alert("Kredi kartı seçilmelidir!");
        return false;
    } 

    if(kkBlKodu==""){
        alert("Kredi kartı seçilmelidir!");
        return false;
    } 

    if(hesapKesim1=="" || hesapKesim2=="" ){
        alert("Hesap Kesim Tarih Aralığı Girilmelidir!");
        return false;
    } 
    //console.log(hesapKesim);
    //console.log(kkBlKodu);

    //return false;

    $.ajax({
        method: "POST",
        url: "KrediKartiDonemBorcuVer.php",
        async: false,
        data: { tarih1: hesapKesim1, 
                tarih2: hesapKesim2, 
                kkNo: kkBlKodu,
                kkKartNo : kkKartNoX
              }        
        })
        .done(function( response ) { 

            response = response.trim();
            //console.log(response);

            if(response=="Hata"){
                alert("Bu Kredi kartında mükerrer ÖDEME var!");
            }else{
                document.getElementById("kkBakiye").value = response;
                document.getElementById("tutar").value = response;
            }

                  
            
        });







    return false;
}


function girisKontrol(){
	
	if(document.getElementById("cariKod").value == "" || document.getElementById("talepSn").value == ""){
		alert("Cari Kod ve Talep No mutlaka girilmiş olmalıdır!");
		return false;
	}
	
	//console.log(document.getElementById("odemeEmriHedefSn").value);
	
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>


<br>



<?php


function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}


function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) return 'selected';
    
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
      
   while($row = pg_fetch_row($ret))
   {
       array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function isChecked($gelenAvans){

    if($gelenAvans == 't' ) $donenDeger = "checked";     
    

    return $donenDeger;
    
}

function kilitliMi($gelenMod){
    if($gelenMod == "Görüntüleme") return ' onclick="return false;" onkeydown="return false;" '; else return '';
}

function textKilitliMi($gelenMod){
    if($gelenMod == "Görüntüleme") return ' readonly '; else return '';
}

function subeListesiVer(){
    
    global $aktifSirketBLKodu;
    global $aktifSirketDB;

    $donenListe=[];

    $sorguCumle = "SELECT SUBE_ADI FROM SUBE WHERE BLSRKODU=$aktifSirketBLKodu AND AKTIF = 1 ORDER BY SUBE_ADI;";

    try {	
        $dbh = new \PDO($aktifSirketDB , 'sysdba', 'masterkey');
        
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            array_push($donenListe, utf8MetinYap($row[0]));
        }
        $dbh = null;
        $ret = null;
        
        }
        catch (PDOException $e) {
            print "Hata!: " . $e->getMessage() . "<br/>";
            $dbh = null;
            $ret = null;
            die();
        }

    return $donenListe;
}

function fileButtonKilitliMi($gelenMod){
    if($gelenMod == "Görüntüle") return ' disabled '; else return '';
}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }


 function cariListeVer(){

    global $aktifDB;

    $sorguCumle ="SELECT CARIKODU, TICARI_UNVANI FROM CARI 
                WHERE SILINDI=0 AND BLKODU>0
                ORDER BY TICARI_UNVANI ;";

    $donenListe = [];
    try {
        $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
        $donenListe = $dbh->query($sorguCumle);      	
        
        
        //VT sıfırlanıyor...
        $dbh = null;
        $ret = null;
        
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        die();
    }
    
    
    return $donenListe;

 }

 function talepKonuVer($gelenTalepKod){

    if($gelenTalepKod =="") return "";

    global $db;

    $sorgu="SELECT konu FROM talep WHERE sn=$gelenTalepKod;";

	$ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Talep bulunamadı";
        exit;
    } 

    $donenDeger = "";

    while($row = pg_fetch_row($ret)){
        $donenDeger = $row[0];
    }

    return $donenDeger;
 }

 function talepBakiyeVer($gelenTalepKod){

    if($gelenTalepKod =="") return "";

    global $db;

    $sorgu="SELECT (nakit_odenen + bankadan_odenen + evrakla_odenen + kk_odenen + acik_odenen) FROM talep WHERE sn=$gelenTalepKod;";

	$ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Talep bulunamadı";
        exit;
    } 

    $donenDeger = "";

    while($row = pg_fetch_row($ret)){
        $donenDeger = $row[0];
    }

    return $donenDeger;
 }





 function cariAdVer($gelenCariKod){

    global $aktifDB;

    $gelenCariKod = isoMetinYap($gelenCariKod);

    $sorguCumle ="SELECT TICARI_UNVANI FROM CARI 
                WHERE CARIKODU = '$gelenCariKod';";

    $donenAd ="";

    try {
        $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');

        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            $donenAd = utf8MetinYap($row[0]);
        }      	
        
        
        //VT sıfırlanıyor...
        $dbh = null;
        $ret = null;
        
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        die();
    }
    
    
    return $donenAd;

 }

 function cariBakiyeOku($gelenCariKod){
	
	global $aktifDB;
	
	//$TurkceKredi = iconv('utf-8','iso-8859-9', 'KREDİ');
	
	//if($subeKullan==1) $subeSorgu = " AND CH.SUBE_KODU = '$gelenSube' ";else $subeSorgu = " ";
	
	$donenDeger = 0;
	
	$sorguCumle ="SELECT SUM(KPB_ATUT), SUM(KPB_BTUT) FROM CARIHR CH
								JOIN CARI C ON (C.BLKODU = CH.BLCRKODU)
								WHERE CH.BLKODU > 0								 
								 AND C.AKTIF IN(1) 
                                 AND C.CARIKODU = '$gelenCariKod'								 
								 AND CH.SILINDI=0;";
								 
		 //echo $sorguCumle;
		 
				try {
						$dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
						$ret = $dbh->query($sorguCumle);
						
						foreach($ret as $row) {
							$donenDeger=$row[0] - $row[1];
						}
						
						//VT sıfırlanıyor...
						$dbh = null;
						$ret = null;
						
					}
					catch (PDOException $e) {
						print "Hata!: " . $e->getMessage() . "<br/>";
						die();
					}
					
					
		return $donenDeger;
	
}


//--------------------------
function krediAdVer($gelenBankaKrediNo){ //Krediden bilgi çeker.

    global $aktifDB;

    if($gelenBankaKrediNo=="") return "";

    $donenDeger = "";

    $sorguCumle = "SELECT BLBNHSKODU_KAYNAK, BLBNHSKODU_HEDEF FROM BANKA_KREDI_KUL WHERE BLKODU = $gelenBankaKrediNo;";

    try {
		$dbh = new PDO($aktifDB , 'sysdba', 'masterkey');
		$ret = $dbh->query($sorguCumle);
		
			foreach($ret as $row) {
				$donenDeger= bankaBilgisiVer($row[0]). " ==> ". bankaBilgisiVer($row[1]);
			} 				
			
			//VT sıfırlanıyor...
			$dbh = null;
			$ret = null;
			
		}
		catch (PDOException $e) {
			print "Hata!: " . $e->getMessage() . "<br/>";
			//VT sıfırlanıyor...
			$dbh = null;
			$ret = null;
			die();
		}        
		
	 return $donenDeger;

}


//--------------------------
function bankaBilgisiVer($gelenBankaNo){ //burası banka hesap no. Buradan bankaya gidilecek.

    global $aktifDB;

    if($gelenBankaNo=="") return "";

    $donenDeger = "";

    $sorguCumle = "SELECT BANKA_ADI, TANIMI FROM BANKA_ADI 
                                    JOIN BANKA_HESAP ON BANKA_HESAP.BLBNKODU = BANKA_ADI.BLKODU
                                    WHERE BANKA_HESAP.BLKODU=$gelenBankaNo;";

    //consoleYaz($sorguCumle);

    try {
		$dbh = new PDO($aktifDB , 'sysdba', 'masterkey');
		$ret = $dbh->query($sorguCumle);
		
			foreach($ret as $row) {
				$donenDeger= $row[0]. " ". $row[1];
			} 				
			
			//VT sıfırlanıyor...
			$dbh = null;
			$ret = null;
			
		}
		catch (PDOException $e) {
			print "Hata!: " . $e->getMessage() . "<br/>";
			//VT sıfırlanıyor...
			$dbh = null;
			$ret = null;
			die();
		}	        
		
	 return $donenDeger;

}





function kkBakiyeOku($gelenkkNo){
	
	global $aktifDB;

    
    $donemKodu = date("Y") . date("m"); //202107 şeklinde gelmesi beklenir

    
	
	$donenDeger = 0;    
	
	$sorguCumle ="SELECT sum(tutar_borc) - sum(tutar_alacak) from KREDI_KARTI
                                 JOIN BANKA_KK_DETAY ON BANKA_KK_DETAY.BLKODU = KREDI_KARTI.BLKKKODU 
                                 WHERE BANKA_KK_DETAY.KK_NO = '$gelenkkNo' AND DONEM_KODU <='$donemKodu';";
								 
		 //echo $sorguCumle;
		 
				try {
						$dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
						$ret = $dbh->query($sorguCumle);
						
						foreach($ret as $row) {
							$donenDeger=$row[0];
						}
						
						//VT sıfırlanıyor...
						$dbh = null;
						$ret = null;
						
					}
					catch (PDOException $e) {
						print "Hata!: " . $e->getMessage() . "<br/>";
						die();
					}
					
					
		return round($donenDeger,2);
	
}


function krediTaksitTutarı($gelenKrediNo){
	
	global $aktifDB;

    
    $donemYil = date("Y"); //2021 şeklinde gelmesi beklenir
    $donemAy = date("m"); //07 şeklinde gelmesi beklenir

    
	
	$donenDeger = 0;
	
	$sorguCumle ="SELECT TUTAR_TAKSIT from BANKA_KREDI_KUL_DETAY WHERE  BLMASKODU = $gelenKrediNo AND extract(month from VADESI) = $donemAy AND  extract(year from VADESI) = $donemYil;";							 
		 
		 
				try {
						$dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
						$ret = $dbh->query($sorguCumle);
						
						foreach($ret as $row) {
							$donenDeger=$row[0];
						}
						
						//VT sıfırlanıyor...
						$dbh = null;
						$ret = null;
						
					}
					catch (PDOException $e) {
						print "Hata!: " . $e->getMessage() . "<br/>";
						die();
					}
					
					
		return $donenDeger;
	
}


function hizmetAlimKonuVer($gelenHizmetAlimNo){

    if($gelenHizmetAlimNo =="") return "Bulunamadı!";

    global $db;

    $sorgu="SELECT hizmet_ad FROM hizmet_alim
                                JOIN hizmet_alim_hakedis_detay ON hizmet_alim_hakedis_detay.hizmet_alim_no = hizmet_alim.sn
                                WHERE hizmet_alim_hakedis_detay.sn=$gelenHizmetAlimNo;";

	$ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Hizmet alım bulunamadı";
        exit;
    }
    $donenDeger = "Hata!";

    while($row = pg_fetch_row($ret)){
        $donenDeger = $row[0];
    }

    return $donenDeger;
}


function hakedisBakiyeVer($gelenhakedisNo){

    if($gelenhakedisNo =="") return -1;

    global $db;

    $sorgu="SELECT SUM (miktar*birim_fiyat) FROM hizmet_alim_hakedis_detay                                
                                           WHERE master_sn=$gelenhakedisNo;";

	$ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Hizmet alım bulunamadı";
        exit;
    }
    $donenDeger = -1;

    while($row = pg_fetch_row($ret)){
        $donenDeger = $row[0];
    }

    return $donenDeger;
}


function trueFalse($gelen, $mesaj, $degilse){
    if($gelen=='t') return $mesaj;else return $degilse;
}










//************************************************** */
//************************************************** */
//************************************************** */
//************************************************** */
//************************************************** */
//************************************************** */


//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("AkinsoftIslemleri.php");
require("mesajlasma.php");
require("logYaz.php");
//require("dosyalariYukle.php");


//Gelen Değerler alınıyor...

$gelenTalepHareketSn = $_POST["talepHareketSn"];


if($gelenTalepHareketSn == ""){
	echo "Veri Hatası!";
	pg_close($db);
	exit();	
}


//Talep bilgileri alınıyor.

$sorguCumle = "SELECT talep.sn, talep.konu
					FROM talep
					JOIN talep_hareket ON talep_hareket.talep_sn = talep.sn
				WHERE talep_hareket.sn = $gelenTalepHareketSn;";

//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);


while($row = pg_fetch_row($ret)){
	$sn = $row[0];	
	$konu = $row[1];	
}








//Önceden açılmış mı?

$sorguCumle = "SELECT COUNT(sn) FROM talep_dogrudan_degisiklik
							   WHERE talep_hareket_sn = $gelenTalepHareketSn
							   AND onay = FALSE;";

//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);


while($row = pg_fetch_row($ret)){
	$adet = $row[0];	
}

if($adet>0){
	echo '<script type="text/javascript"> alert("Bu Ödeme emri ile ilgili bir Değişiklik talebi zaten bulunuyor!");</script>';
	echo '<br><br><br><br><br><br><center><button type="button" onclick="self.close();" style="width:200px;" >Kapat</button></center>';
	goto cikis;
}






//*******************************************

//Ödeme emri bilgileri çağrılıyor.

$sorguCumle ="SELECT sn, 					 
					 nakit_odenen,
					 bankadan_odenen,
					 evrakla_odenen,
					 kk_odenen,					 
					 acik_odenen,
					 kk_odenen_kk_kod,
					 odenen_banka_kod,
					 kdv_oran,
					 tevkifat_oran,
					 duzeltme_zaman
				 FROM talep_hareket
				 WHERE sn = $gelenTalepHareketSn;";
				 
$ret = pg_query($db, $sorguCumle);

if(!$ret){
	echo pg_last_error($db);
	exit();
}


echo "<br><br><h5>Talep S/N: $sn</h5>";
echo "<h5>Talep Konu: $konu</h5>";


echo "<center><h3>Varolan Bilgiler</h3></center>";

echo '<table class= "sortable" valign="middle" id="tableMainIlk">';
echo "<tr>";
echo '<th style="text-align:center;cursor:pointer;">S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Nakit Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Bankadan Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Evrakla Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">K.K. Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Açık Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">K.K.</th>';
echo '<th style="text-align:center;cursor:pointer;">Banka</th>';
echo '<th style="text-align:center;cursor:pointer;">KDV</th>';
echo '<th style="text-align:center;cursor:pointer;">Tevkifat</th>';
echo '<th style="text-align:center;cursor:pointer;">İşlem Tarihi</th>';
echo "</tr>";



while($row = pg_fetch_row($ret)){
	
	$nakitOdenen = $row[1]; 
	$bankadanOdenen = $row[2]; 
	$evraklaOdenen = $row[3]; 
	$kkOdenen = $row[4]; 
	$acikOdenen = $row[5]; 
	
	$kkKod = $row[6]; 
	$bankaKod = $row[7]; 
	
	$eskiKdv = $row[8]; 
	$eskiTevkifat = $row[9]; 
	
	
		
	
	echo "<tr>";
	echo "<td style='text-align:center;'>$row[0]</td>"; //Sn	
	echo "<td style='text-align:right;'>".number_format($row[1], 2, ",", ".") ."</td>"; //Nakit Ödenen
	echo "<td style='text-align:right;'>".number_format($row[2], 2, ",", ".") ."</td>"; //Bankadan Ödenen
	echo "<td style='text-align:right;'>".number_format($row[3], 2, ",", ".") ."</td>"; //Evrakla Ödenen
	echo "<td style='text-align:right;'>".number_format($row[4], 2, ",", ".") ."</td>"; //KK Ödenen
	echo "<td style='text-align:right;'>".number_format($row[5], 2, ",", ".") ."</td>"; //Açık Ödenen
	echo "<td style='text-align:center;'>$row[6]</td>"; //KK Kod
	echo "<td style='text-align:center;'>$row[7]</td>"; //Banka Kod
	echo "<td style='text-align:center;'>$row[8]</td>"; //KDV
	echo "<td style='text-align:center;'>$row[9]</td>"; //Tevkifat
	echo "<td sorttable_customkey='". tarihFormatla($row[10]) ."' style='text-align:center'>". tarihYaz($row[10]) . "</td>"; //Son Tarihi
	
	echo "</tr>";	
}

//Java'dan ulaşmak için
echo '<input type="hidden" id="talepHareketSn" value="'.$gelenTalepHareketSn.'">';
echo '<input type="hidden" id="eskiNakit" value="'.$nakitOdenen.'">';
echo '<input type="hidden" id="eskiBankadan" value="'.$bankadanOdenen.'">';
echo '<input type="hidden" id="eskiEvrakla" value="'.$evraklaOdenen.'">';
echo '<input type="hidden" id="eskiKkOdenen" value="'.$kkOdenen.'">';
echo '<input type="hidden" id="eskiAcikOdenen" value="'.$acikOdenen.'">';


echo '<input type="hidden" id="eskiKkKod" value="'.$kkKod.'">';
echo '<input type="hidden" id="eskiBankaKod" value="'.$bankaKod.'">';


echo '<input type="hidden" id="eskiKdv" value="'.$eskiKdv.'">';
echo '<input type="hidden" id="eskiTevkifat" value="'.$eskiTevkifat.'">';


echo "</table>";


//*************
//*************
//İkinci kısım...
//*************
//*************


echo "<br><br><hr>";


echo "<center><h3>Değişecek Bilgiler</h3></center>";


//Form başlıyor...

//echo '<form action="#" method="post">';


echo '<table class= "sortable" valign="middle" id="tableMainYeni">';
echo "<tr>";
echo '<th style="text-align:center;cursor:pointer;">Nakit Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Bankadan Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Evrakla Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">K.K. Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Açık Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Banka</th>';
echo '<th style="text-align:center;cursor:pointer;">K.K</th>';
//echo '<th style="text-align:center;cursor:pointer;">Banka</th>';
echo '<th style="text-align:center;cursor:pointer;">KDV</th>';
echo '<th style="text-align:center;cursor:pointer;">Tevkifat</th>';
echo '<th style="text-align:center;cursor:pointer;color:yellow;">Silinsin!</th>';
echo '<th style="text-align:center;cursor:pointer;">Açıklama</th>';
echo "</tr>";


echo "<tr>";

//-----------------

echo "<td style='text-align:center;'>";
echo '<input style="text-align:right;width:100%;" type="number" id="yeniNakit" name="yeniNakit" required size="15" value="'.$nakitOdenen.'" step="0.01" min="0.00">'; //Yeni Tutar
echo "</td>"; 

//-----------------

echo "<td style='text-align:center;'>";
echo '<input style="text-align:right;width:100%;" type="number" oninput="degerKontrol(this);" id="yeniBanka" name="yeniBanka" required size="15" value="'.$bankadanOdenen.'" step="0.01" min="0.00">'; //Yeni Tutar
echo "</td>"; 


//-----------------

echo "<td style='text-align:center;'>";
echo '<input style="text-align:right;width:100%;" type="number" id="yeniEvrak" name="yeniEvrak" required size="15" value="'.$evraklaOdenen.'" step="0.01" min="0.00">'; //Yeni Tutar
echo "</td>"; 

//-----------------

echo "<td style='text-align:center;'>";
echo '<input style="text-align:right;width:100%;" type="number" oninput="degerKontrol(this);" id="yeniKk" name="yeniKk" required size="15" value="'.$kkOdenen.'" step="0.01" min="0.00">'; //Yeni Tutar
echo "</td>"; 

//-----------------

echo "<td style='text-align:center;'>";
echo '<input style="text-align:right;width:100%;" type="number" id="yeniAcik" name="yeniAcik" required size="15" value="'.$acikOdenen.'" step="0.01" min="0.00">'; //Yeni Tutar
echo "</td>"; 


//-----------------

echo "<td style='text-align:center;'>";
echo '<input type="text" style="width:100%;" id="yeniBankaKod" onclick="modalGoster(6);" name="yeniBankaKod" readonly value="'.$bankaKod.'" title="Banka seçimini yapın!">';
echo "</td>"; 



//-----------------

echo "<td style='text-align:center;'>";
echo '<input type="text" style="width:100%;" id="yeniKkKod" onclick="modalGoster(8);" name="yeniKkKod" readonly value="'.$kkKod.'" title="Kredi Kartı seçimini yapın!">';
echo "</td>"; 



//-----------------

echo "<td style='text-align:center;'>";
echo '<input style="text-align:right;width:100%;" type="number" id="yeniKdv" name="yeniKdv" required size="15" value="'.$eskiKdv.'" step="0.01" min="0.00">'; //Yeni KDV
echo "</td>"; 


//-----------------

echo "<td style='text-align:center;'>";
echo '<input style="text-align:right;width:100%;" type="number" id="yeniTevkifat" name="yeniTevkifat" required size="15" value="'.$eskiTevkifat.'" step="0.01" min="0.00">'; //Yeni Tevkifat
echo "</td>"; 


//-----------------


echo "<td style='text-align:center;'>";
echo '<input style="text-align:right;width:100%;" type="checkbox" id="silinsin" name="silinsin" value="sil" >'; //Kayıt silinsin!
echo "</td>"; 


//-----------------

echo "<td style='text-align:center;'>"; //Açıklama

echo '<input type="text" style="width:100%;" id="aciklama" name="aciklama" value="'.$yeniAciklama.'" title="Açıklama">';

echo "</td>";

echo "</tr>";

echo "</table>";


echo "<br><br><br><br>";

//Kaydet Buton
//echo '<input type="hidden" name="islemTip" value="Kaydet">';
//echo '<input type="hidden" name="odemeEmriHedefSn" value="'.$gelenOdemeEmriHedefSn.'">';


if($yeniYetki=='t'){
	echo '<button type="button" id="kaydetTusu" onclick="return veriKontrol(this);" style="width:200px;" >Değişiklik İsteği Yap</button> ';
	
}


echo '<br><br><br>';

echo '<button type="button" onclick="self.close();" style="width:200px;" >Kapat</button> ';

echo '<br><br><br>';



if($gelenKapatGorunsun=="1"){
	echo "İşlem sonrasında önceki listeyi tazelemeyi unutmayın!";	
}


cikis:


//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';





pg_close($db);

//echo "<hr><br><br><b>Dip Toplam :$toplam TL </b><hr>";   

//echo "<br>";
//echo "***Tüm geçmiş siparişleriniz listelenmiştir.";

?>

</body>
</html>
