<?php
require('yetki.php');
$ekranKod = 57;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>

<style style="text/css">
    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
        margin-left :10px
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }
</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">

function searchTableColumns(gelenTablo) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById("myInputHepsi");
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length-1; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
      }      
}

function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function kolonArama(gelenKolon, kolonNo) {
  var input, filter, table, tr, td, i, txtValue;
  input = document.getElementById(gelenKolon);//"myInput"
  filter = input.value.toLocaleUpperCase("tr");
  table = document.getElementById("tableMain");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[kolonNo];
    if (td) {
      txtValue = td.textContent || td.innerText;
      if (txtValue.toLocaleUpperCase("tr").indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sayac){
    
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edildi.");
		return false;
    }    

    var kisilerListesi = "";

    var i=1;
        for (i = 1; i <= sayac; i++) {               
                
                if(document.getElementById("kisiler-" + i).checked == true){
                    kisilerListesi+=document.getElementById("kisiler-" + i).value+';';
                }
        } 

    kisilerListesi = kisilerListesi.slice(0, -1);


    //console.log(kisilerListesi);

    if(kisilerListesi==""){
        alert("Görevlendirme için en az bir kişi seçilmelidir!\nİşlem iptal edildi.");
		return false;		
		
	}

    document.getElementById("kisiBilgileri").value = kisilerListesi;
	return true;	


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>



<?php




function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}

function textKilitliMi($gelenMod){
    if($gelenMod == "Görüntüleme") return ' readonly '; else return '';
}

function kilitliMi($gelenMod){
    if($gelenMod == "Görüntüleme") return ' onclick="return false;" onkeydown="return false;" '; else return '';
}

function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn='$gorevSn';";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$row[0];	   
   }  

   return $liste;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }



function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}


function gorevSnVer($gelenTalepNo){

    global $db;
 
    $sorgu = "SELECT sn FROM gorevlendirme WHERE talep_sn = $gelenTalepNo;";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
       echo "Ayar parametresi bulunamadı";    
    } 
    $donenDeger =-1;

    while($row = pg_fetch_row($ret)){
        $donenDeger =$row[0]        ;
    }
    
 
   return $donenDeger;

}

//*************************************************************************** */
//*************************************************************************** */
//*************************************************************************** */
//*************************************************************************** */


//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");
require("BelgesiVarMi.php");

$gelenTalepDetaySn = $_POST['talepDetaySn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];

if($gelenKriter=="")$gelenKriter = "2";

switch ($gelenKriter){
    case "1":
        echo '<br><br><h1 style="text-align:center;">Tüm Talepler</h1>';
        break;
    case "2":
        echo '<br><br><h1 style="text-align:center;">Onay Bekleyen Talepler</h1>';
        break;
    case "3":
        echo '<br><br><h1 style="text-align:center;">Onaylanmış Talepler</h1>';
        break;
    case "4":
        echo '<br><br><h1 style="text-align:center;">İptal Edilmiş Talepler</h1>';
        break;
}

echo '<form action="#" method="post">';
echo '<br>Kriter: ';
echo '<select name="kriter"  onchange="this.form.submit()">';
echo '<option value="1" ' .selectedKoy($gelenKriter, "1"). '>Tüm Talepler</option>';
echo '<option value="2" ' .selectedKoy($gelenKriter, "2"). '>Onay Bekleyen Talepler</option>';
echo '<option value="3" ' .selectedKoy($gelenKriter, "3"). '>Onaylanmış Talepler</option>';
echo '<option value="4" ' .selectedKoy($gelenKriter, "4"). '>İptal Edilmiş Talepler</option>';
echo "</select>";
echo "<br><br>";

echo '<input type="submit" value="Sorgula">';
echo '</form>';
echo "<br><br>";

echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" onkeyup="searchTableColumns(\'tableMain\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">';
/*
echo 'Talep Konusu: <input type="text" id="myInput" onkeyup="kolonArama(\'myInput\', 2)" placeholder="Talep Konusu ara.." title="Konu yaz"><br><br>';
echo 'Talep Detay: <input type="text" id="myInputKisi" onkeyup="kolonArama(\'myInputKisi\', 3)" placeholder="Talep Detay ara.." title="Detay yaz"><br><br>';
echo 'Talep Açıklama: <input type="text" id="myInputAciklama" onkeyup="kolonArama(\'myInputAciklama\', 4)" placeholder="Talep Açıklama ara.." title="Açıklama yaz">';
*/
echo "<br><br>";


//Burada kullanıcılara bilgilendirme yapılacak.
$gidenKimden="<EMAIL>";
$gidenAdresler = [];
$gidenTelefonlar = [];

$kisiler =[];


if($gelenTalepDetaySn <> "" && $gelenIslemTip<>""){

    
    switch ($gelenIslemTip) {
        case "onayla":
            $sorguCumle = "UPDATE talep_detay SET onay = TRUE, onay_zaman=NOW(), onay_kullanici='$kullanici'
                           WHERE sn=$gelenTalepDetaySn;"; 

            logYaz($gelenTalepDetaySn . " Numaralı satınalma talebi onaylandı.");

            //Satınalma ilgililerine bilgi verilecek.
            $mesaj = $gelenTalepDetaySn. " Numaralı talep ONAYLANDI";
            
            
            if($kullanici=="eguler"){

                $sallaTel = []; 
                $mesajX = $mesaj ."\n". paslamaKoduVer('abilgin', 'talepDetayListesi.php', 'kriter', '3');
                array_push($sallaTel, kullanicidanBilgiVer('cep_tel', 'abilgin') );
                //kisaMesajGonder($mesajX, $sallaTel, 0);
                //******** */
                $sallaTel = [];
                $mesajX = $mesaj ."\n". paslamaKoduVer('tsecilgin', 'talepDetayListesi.php', 'kriter', '3'); 
                array_push($sallaTel, kullanicidanBilgiVer('cep_tel', 'tsecilgin') );
                //kisaMesajGonder($mesajX, $sallaTel, 0);
                //******** */
                $sallaTel = [];
                $mesajX = $mesaj ."\n". paslamaKoduVer('fcan', 'talepDetayListesi.php', 'kriter', '3'); 
                array_push($sallaTel, kullanicidanBilgiVer('cep_tel', 'fcan') );
                //kisaMesajGonder($mesajX, $sallaTel, 0);

            }else{
                $sallaTel = []; 
                $mesajX = $mesaj ."\n". paslamaKoduVer('eguler', 'talepDetayListesi.php', 'kriter', '3'); 
                array_push($sallaTel, kullanicidanBilgiVer('cep_tel', 'eguler') );
                //kisaMesajGonder($mesajX, $sallaTel, 0);
                //******** */
                $sallaTel = []; 
                $mesajX = $mesaj ."\n". paslamaKoduVer('tsecilgin', 'talepDetayListesi.php', 'kriter', '3'); 
                array_push($sallaTel, kullanicidanBilgiVer('cep_tel', 'tsecilgin') );
                //kisaMesajGonder($mesajX, $sallaTel, 0);
            }           
            
            unset($sallaTel);           

            
            break;
        case "onaylaIptal":
            $sorguCumle = "UPDATE talep_detay SET onay = FALSE, onay_zaman=NULL 
                           WHERE sn=$gelenTalepDetaySn;";    
                           
            logYaz($gelenTalepDetaySn . " Numaralı satınalma talebi onayı iptal edildi.");

            break;  
        case "satirIptal":
            $sorguCumle = "UPDATE talep_detay SET iptal = TRUE, iptal_kullanici='$kullanici'
                           WHERE sn=$gelenTalepDetaySn;";

            logYaz($gelenTalepDetaySn . " Numaralı satınalma satır iptal edildi.");

            break;

        case "satirSil":
            $sorguCumle = "DELETE FROM talep_detay WHERE sn=$gelenTalepDetaySn;";

            logYaz($gelenTalepDetaySn . " Numaralı satınalma satırı silindi.");

            break;

        } 


    //Sorgu çalıştır.
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

}



$alanlar = "SELECT talep_detay.sn, 
                    talep_sn, 
                    talep.konu, 
                    talep.detay, 
                    aciklama, 
                    miktar, 
                    birim, 
                    termin_zaman, 
                    onay, 
                    onay_zaman, 
                    isleme_alindi, 
                    islem_zaman, 
                    talep_detay.tamamlandi, 
                    tamamlandi_zaman,
                    talep_detay.onay_kullanici,
                    iptal_kullanici, 
                    iptal,
                    talep.talep_eden,
                    talep_detay.yaratma_zaman,
                    talep_detay.duzeltme_zaman "; //19

switch ($gelenKriter) {
    case "1": //Tamamı
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn WHERE talep.tamamlandi=FALSE AND iptal=FALSE ORDER BY talep_detay.yaratma_zaman, talep_detay.sn DESC;";
      break;
    case "2": //Onay Bekleyenler

        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                        WHERE talep_detay.onay = FALSE
                        AND talep.tamamlandi=FALSE
                        AND iptal=FALSE
                        ORDER BY talep_detay.yaratma_zaman, talep_detay.sn DESC;";              
      break;
    case "3": //Tamamlanmışlar
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                        WHERE talep_detay.onay = TRUE
                        AND talep.tamamlandi=FALSE
                        AND iptal=FALSE
                        ORDER BY talep_detay.yaratma_zaman, talep_detay.sn DESC;"; 
      break; 
      
    case "4": //Tamamlanmışlar
    $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
                    WHERE iptal=TRUE
                    ORDER BY talep_detay.yaratma_zaman, talep_detay.sn DESC;"; 
      break;  



    default:
        $sorguCumle = $alanlar .  "FROM talep_detay LEFT JOIN talep ON talep.sn=talep_detay.talep_sn
            WHERE talep_detay.tamamlandi = FALSE
            AND talep.tamamlandi=FALSE
            AND iptal=FALSE
            ORDER BY talep_detay.yaratma_zaman, talep_detay.sn DESC;";
    
  } 
//echo $sorguCumle;

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
} 


//echo "<br><br>";    


echo '<table class= "sortable" valign="middle" id="tableMain">';
echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
echo '<th style="text-align:center;cursor:pointer;">Detay S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Konu</th>';
//echo '<th style="text-align:center;cursor:pointer;">Talep Detay</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Açıklama</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Miktar</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Birim</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Eden</th>';
echo '<th style="text-align:center;cursor:pointer;">Termin Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">Onaylayan</th>';
echo '<th style="text-align:center;cursor:pointer;">Onay Zaman</th>';
echo "<th style=\"text-align:center\">İşlem</th>";
echo "</tr>";

//$sayac = 1;

while($row = pg_fetch_row($ret)){

    //Gizli görev ve kullanıcı listede yoksa ve root değilse pas geçiliyor.

    //$goreviGorebilir = goreviGorebilir($row[6], $kullanici, $row[0]);

    //Sadece görevi alanlar
    //$sadeceGorevli = sadeceGorevli($row[6], $kullanici);
    /*
    if($_SESSION["root"] == 0){
        
        if( $row[7]=='t' && $goreviGorebilir == false ) continue;
    }
    */


    echo "<tr class='item' title='". $row[3] ."'>";
    echo "<td style='text-align:center'>". $row[0] . "</td>"; //S/N       
    echo "<td style='text-align:center'>". $row[1] . "</td>"; //S/N 

    echo "<td>";

        if($gelenKriter=="2"){ //Renk kontrolü yapılacak

            if($row[18]==$row[19]){ //Düzeltme olmadıysa?
                echo $row[2];
            }else { // Düzeltme yapılmış
                echo '<span class="blink">'.$row[2].'</span>';
            }

        }else {
            echo $row[2];        
        }
    
    echo "</td>"; //Konu



    //echo "<td>". $row[3] . "</td>"; //Detay
    echo "<td>". $row[4] . "</td>"; //Açıklama
    echo "<td style='text-align:center'>". $row[5] . "</td>"; //Miktar
    echo "<td style='text-align:center'>". $row[6] . "</td>"; //Birim
    echo "<td style='text-align:center'>". $row[17] . "</td>"; //Talep Eden
    echo "<td sorttable_customkey='". tarihFormatla($row[7]) ."' style='text-align:center'>". tarihYaz($row[7]) . "</td>"; //TerminZaman
    //echo "<td style='text-align:center'>". trueFalse($row[8],'Onaylandı') . "</td>"; //Onay
    echo "<td style='text-align:center'>". $row[14] . "</td>"; //Onaylayan
    echo "<td sorttable_customkey='". tarihFormatla($row[9]) ."' style='text-align:center'>". tarihSaatYaz($row[9]) . "</td>"; //Onay Zaman
       


    //İŞLEMLER kısmı
    //Detay, silme ve Hikaye Tuşları
    echo "<td style='vertical-align: middle; text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";


    if($_SESSION["root"] == 1 && $row[8]=='f'){
        
            if($row[8]=='f'){
                //Tamamlanma Talep Onayla?
                echo '<form action="#"  method="post" class="inline" onclick="this.disabled=true;return true;">'; 
                echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 
                echo '<input type="hidden" name="islemTip" value="onayla">';   
                echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';   
                echo '<button type="submit"><img src="onay.svg" height="20" width="20" title="Onayla==>'.$row[0].'"/></button>';    
                echo '</form>';
            }else{
                //Tamamlanma Talep Onay İptal?
                echo '<form action="#"  method="post"  class="inline">'; 
                echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 
                echo '<input type="hidden" name="islemTip" value="onaylaIptal">'; 
                echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';        
                echo '<button type="submit"><img src="cancel.png" height="20" width="20" title="Onayla İptal==>'.$row[0].'"/></button>';    
                echo '</form>';
            }
    }


    if(belgesiVarMi("talepBelgeler", $row[1], $row[0])==true){
        
        //Belgeleri Göster
        echo '<form action="TalepBelgeleri.php" target="_blank"  method="post"  class="inline">'; 
        echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';    
        echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">';  
        echo '<input type="hidden" name="kaynak" value="talepOnay.php">';  
        echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$row[0].'"/></button>';
        //echo '<button type="submit">Detay</button>';
        echo '</form>';
    }



    


    if( $_SESSION["root"] == 1 || $duzeltYetki=='t' ){

        $bulunanGorevSn = gorevSnVer($row[1]);


            //Hikayeye Git
            echo '<form action="GorevHikayesiListesi.php"  method="post"  class="inline">'; 
            echo '<input type="hidden" name="gorevSn" value="'.$bulunanGorevSn.'">';    
            echo '<input type="hidden" name="gonderenSayfa" value="talepDetayListesi.php">';    
            echo '<button type="submit"><img src="info.svg" height="20" width="20" title="Hikayeye Git==>'.$bulunanGorevSn.'"/></button>';
            //echo '<button type="submit">Detay</button>';
            echo '</form>';









        if($row[16]=='f'){
            echo '<form action="#"  method="post"  class="inline" onclick="return ConfirmIslem(\'Satır İPTAL edilecek!\');">';         
            echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 
            echo '<input type="hidden" name="islemTip" value="satirIptal">';
            echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';        
            //echo '<input type="hidden" name="kaynak" value="talepListesi.php">';  
            echo '<button type="submit"><img src="satir-iptal.png" height="20" width="20" title="Satır İptal==>'.$row[0].'"/></button>';
            //echo '<button type="submit">Detay</button>';
            echo '</form>';
        }elseif($row[16]=='t'){ //Burada iptal edilmiş satır siliniyor.

            echo '<form action="#"  method="post"  class="inline" onclick="return silmeOnay();">';         
            echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 
            echo '<input type="hidden" name="islemTip" value="satirSil">';
            echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';        
            //echo '<input type="hidden" name="kaynak" value="talepListesi.php">';  
            echo '<button type="submit"><img src="Delete_Icon.png" height="20" width="20" title="Satır Sil==>'.$row[0].'"/></button>';
            //echo '<button type="submit">Detay</button>';
            echo '</form>';


        }
        
    }




    




    echo "</div>";
    echo "</td>";
    //Detay ve Hikaye Tuşları sonu

    echo "</tr>";
    //$sayac+=1;
}

echo "</table>";




pg_close($db);


?>

</body>
</html>
