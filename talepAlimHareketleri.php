<?php
require('yetki.php');
$ekranKod = 58;
require('ekranYetkileri.php');

?>
<style>   
  table#tableMain2 thead { display:block; }
  table#tableMain2 tbody { height:300px; overflow-y:scroll; display:block; }
</style>
</style>

<?php



function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}

function talepHareketTutar($gelenTalepSn, $gelenTalepDetaySn){
	
	global $db;
	
	$sorguCumle = "SELECT SUM(nakit_odenen + bankadan_odenen + evrakla_odenen + kk_odenen + acik_odenen ) 
				   FROM talep_hareket 
				   WHERE talep_sn = $gelenTalepSn AND talep_detay_sn = $gelenTalepDetaySn;";

    $ret = pg_query($db, $sorguCumle);   

	if(!$ret){
		echo pg_last_error($db);
		exit;
	}
	
	$donenDeger = 0;
    

    while($row = pg_fetch_row($ret)){
        $donenDeger = $row[0];
    }  
	
	return $donenDeger; 	
	
}

//*********************************************************
//*********************************************************
//*********************************************************
//*********************************************************
//*********************************************************
//*********************************************************
//*********************************************************


require("AkinsoftCariAdVer.php");
require("AkinsoftIslemleri.php");


$gelenTalepSn = $_POST['talepSn'];

//echo "$gelenTalepSn<br>";



$sorguCumle = "SELECT sn, 
					  nakit_odenen_cari_kod,
					  nakit_odenen, 
					  bankadan_odenen, 
					  evrakla_odenen, 
					  kk_odenen, 
					  acik_odenen, 					   
					  fatura_no, 
					  kk_odenen_kk_kod    
                FROM talep_hareket WHERE talep_sn = $gelenTalepSn;";

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
}

$count = pg_num_rows($ret); //Sorgu sonucunun satır sayısını veriyor.

if($count<1){
    echo '<p style="color:red">Detay kaydı yok!</p>';
    pg_close($db);
    exit;
}


echo '<table class= "sortable" valign="middle" id="tableMain2" >';
//echo "<table >";
echo "<tr>";
echo '<th style="text-align:center;cursor:pointer;">S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Cari Kod</th>';
echo '<th style="text-align:center;cursor:pointer;">Cari Ad</th>';
echo '<th style="text-align:center;cursor:pointer;">Nakit</th>';
echo '<th style="text-align:center;cursor:pointer;">Bankadan</th>';
echo '<th style="text-align:center;cursor:pointer;">Evrakla Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Kredi Kartı</th>';
echo '<th style="text-align:center;cursor:pointer;">Açık Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Fatura No</th>';
echo '<th style="text-align:center;cursor:pointer;">KK No</th>';
echo "</tr>";

while($row = pg_fetch_row($ret)){  
  
    echo "<tr>";
    echo "<td style='text-align:center'>". $row[0] . "</td>";   //S/N
	echo "<td style='text-align:center'>". $row[1] . "</td>";   //Cari Kod
	
	echo "<td style='text-align:left'>". akinsoftCariAdVer($row[1], 0) . "</td>";   //S/N
	
	echo "<td style='text-align:right'>". number_format($row[2], 2, ',', '.') . "</td>";   //Nakit
	echo "<td style='text-align:right'>". number_format($row[3], 2, ',', '.') . "</td>";   //Bankadan
	echo "<td style='text-align:right'>". number_format($row[4], 2, ',', '.') . "</td>";   //Evrakla
	echo "<td style='text-align:right'>". number_format($row[5], 2, ',', '.') . "</td>";   //K.K.
	echo "<td style='text-align:right'>". number_format($row[6], 2, ',', '.') . "</td>";   //Açık
	
	echo "<td style='text-align:center'>". $row[7] . "</td>";   // Fatura No
	echo "<td style='text-align:center'>". $row[8] . "</td>";   // KK No
	
	
    echo "</tr>";

    
}
echo "</table>";

pg_close($db);


?>