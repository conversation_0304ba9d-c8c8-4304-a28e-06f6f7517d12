<?php
//Toplu onayda talep detayı iptal eder.

require('yetki.php');


$kullanici = $_SESSION["user"];
//require_once('ekranYetkileri.php');
require("PGConnect.php");
require("logYaz.php");

//-----------------------------
require("mesajlasma.php");
require("KullanicidanBilgiVer.php");
require("TalepDetayYoksaTalepKapat.php");

//************************************ */
//************************************ */
//************************************ */
//************************************ */
//************************************ */

$gelenTalepSn = $_POST['talepSn'];
$gelenTalepDetaySn = $_POST['talepDetaySn'];
$gelenTalepEdenKullanici = $_POST['talepEdenKullanici'];

if($gelenTalepSn=="" || $gelenTalepDetaySn=="" || $gelenTalepEdenKullanici==""){
    echo "Hatalı veri!";
	pg_close($db);
    exit();
}


//echo "$gelenTalepSn - $gelenTalepDetaySn - $gelenTalepEdenKullanici";
//exit;


$sorguCumle ="UPDATE talep_detay SET iptal = TRUE WHERE sn = $gelenTalepDetaySn;";

$ret = pg_query($db, $sorguCumle);


if(!$ret){
    echo pg_last_error($db);
    exit;    
}



if(pg_affected_rows($ret)>0){
	
	//Tüm detaylar iptal ise ana kaydı da kapatır. 
	talepDetayYoksaTalepKapat($gelenTalepSn);
	

    $HTMLMesaj = "$gelenTalepEdenKullanici oluşturduğu, Talep no: $gelenTalepSn, Talep detay no: $gelenTalepDetaySn Edip Bey tarafından İPTAL edildi.";
    logYaz($HTMLMesaj);
    $HTMLMesaj = $HTMLMesaj . "<br><br><a href='https://portal.bormegplastik.com'>Giriş sayfası için tıklayın.</a><br><br>Kolay gelsin.<br>Bormeg Plastik Yönetim Sistemi";
    //------------------------------
    $gidenKimden = "<EMAIL>";
    $gidenBilgi = "Talebiniz iptal edildi!";
    $gidenAdresler = [];
    //------------------------------
    array_push($gidenAdresler, kullanicidanBilgiVer("eposta", $gelenTalepEdenKullanici));
    epostaGonder($gidenKimden, $gidenAdresler, $gidenBilgi, $HTMLMesaj, 0);
	
	
	
	ob_clean();
    echo "Okkk"; 
}else{
	ob_clean();
    echo "Hata!"; 
}

pg_close($db);

?>