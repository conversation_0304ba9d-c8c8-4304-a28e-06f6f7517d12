<?php
require('yetki.php');
$ekranKod = 56;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">




<script src="sorttable.js"></script>
<script src="jquery-3.6.0.js"></script>


<style style="text/css">


/** Modal pencere için */ 
    /* The Modal (background) */
    .modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}





/* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }




</style>

<?php
require('siteBaslik.html');
require('menuKaynak.php');
require('blink.html');
?>


<script type="text/javascript">


function yeniStokEkle(sender){
	
	document.getElementById("stokEklemeForm").submit();
	
}






function yeniSatirGiris(tabloAd, hedefFonksiyonAd){
	//alert(kullanici);
	
	var yeniAd = prompt("Yeni ilgili alım girin", "");
	
	if (yeniAd == null) {
		alert("İşlem iptal edildi");
		return;
	}
	//console.log(tabloAd);
	//console.log(hedefFonksiyonAd);
	//console.log('<label onclick="'+ hedefFonksiyonAd +'(\''+ yeniAd +'\');">'+ yeniAd +'</label>');
	yeniAd = yeniAd.toLocaleUpperCase("tr");
	
	//Buradan sonra yeni giriş yapılıyor. 
	//Kum saati
    document.body.style.cursor  = 'wait';

    $.ajax({
        method: "POST",
        url: "YeniIlgiAlimKaydet.php",
        async: false,
        data: { alimAd: yeniAd
            }        
    })
        .done(function( response ) { 

            
            response = response.trim();          

            

            if(response.indexOf("Okkk") !== -1){                    
                donenDeger = true; 
                //Normal Saat
                document.body.style.cursor  = 'default';  
                alert("İşlem tamam. Yeni giriş eklendi");
				const yeniKodP = response.split("-");
				yeniKod = yeniKodP[1];
                
                            
            } else{
                //Normal Saat
                document.body.style.cursor  = 'default';
                alert("Hata: " + response);
                console.log(response);
                //console.log("Hata");           
                donenDeger = false;
                //return false;                
            }   
                    
            
        });
		
	if(donenDeger == true ){ //Tabloya satır eklenecek!
	
		let tableRef = document.getElementById(tabloAd);
		let newRow = tableRef.insertRow(-1);
		//newRow.innerHTML='<tr onclick="alert(15);"/>';
		
		let newCell = newRow.insertCell(0);		
		
		newCell.style.textAlign = "left";
		newCell.style.verticalAlign = "middle";
		newCell.innerHTML='<label onclick="'+ hedefFonksiyonAd +'(\''+ yeniKod +'\', \''+ yeniAd +'\');">'+ yeniAd +'</label>';
		
		//-------------
		let newCell1 = newRow.insertCell(0);		
		
		newCell1.style.textAlign = "left";
		newCell1.style.verticalAlign = "middle";
		newCell1.innerHTML='<label onclick="'+ hedefFonksiyonAd +'(\''+ yeniKod +'\', \''+ yeniAd +'\');">'+ yeniKod +'</label>';
		
		
		//let newText = document.createTextNode(yeniAd);
		//newCell.appendChild(newText);
	
	
		
	}
	
	
	
	
	
}


function veriTransferNeIcinAlindi(gelenSn, gelenNeIcinAlindi){
	document.getElementById("ilgiliAlim2").value = gelenSn; 
	
	document.getElementById("ilgiliAlimAd").innerHTML = gelenNeIcinAlindi; 
	document.getElementById("ilgiliAlimAd2").value = gelenNeIcinAlindi;
	
	console.log (gelenNeIcinAlindi);
	
    modalKapat(); 
	
}


function modalGoster(gelenModelKod) {  
    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
      document.body.style.cursor  = 'default';
	  try{
		  document.getElementById("myInputHepsi2").focus();
	  }catch{
		  //
	  }
    }
    };

    if(gelenModelKod==0){
        document.getElementById('baslik').innerHTML ="Hizmet Kartları";
        xhttp.open("POST", "HizmetKartVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==1){
        document.getElementById('baslik').innerHTML ="Stok Kartları";
        xhttp.open("POST", "StokKodVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("veriTransferAd=veriTransferStokKod");

    }else if(gelenModelKod==2){
        document.getElementById('baslik').innerHTML ="Ne için Alındı";
        xhttp.open("POST", "NeIcinAlindiVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("veriTransferAd=veriTransferNeIcinAlindi");

    }

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}

function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
    document.body.style.cursor  = 'default';    
}



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
    document.body.style.cursor  = 'default';
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}

function veriTransferStokKod(gelenStokBlKodu, gelenStokKod, gelenStokAd){
    document.getElementById("stokKod").value = gelenStokKod;
    document.getElementById("stokAd").innerHTML = gelenStokAd;
    document.getElementById("stokAd2").value = gelenStokAd;

    //Hizmet bilgilerini sıfırlıyor.
    document.getElementById("hizmetKod").value = "";
    document.getElementById("hizmetAd").innerHTML = "";
    document.getElementById("hizmetAd2").value = "";

    
    modalKapat();     
}




function veriTransfer(gelenHizmetKod, gelenHizmetAd){
    document.getElementById("hizmetKod").value = gelenHizmetKod;
    document.getElementById("hizmetAd").innerHTML = gelenHizmetAd;
    document.getElementById("hizmetAd2").value = gelenHizmetAd;

    //Stok bilgileri sıfırlanıyor...
    document.getElementById("stokKod").value = "";
    document.getElementById("stokAd").innerHTML = "";
    document.getElementById("stokAd2").value = "";
    
    modalKapat();     
}

function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}




function searchTableColumns(gelenTablo, gelenAlan) {
      // Declare variables 

      //console.log(gelenTablo + gelenAlan);

      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
      }      
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sender){

    var hataVar = false;
	
	var kilometre = parseInt(document.getElementById("kilometre").value);
	
	const yakitStokKodlari = ['MGS0097', 'MGS0096', 'MGS0095', 'MGS0098']; //Dizel, benzin, lpg, Oto Elektrik
	
	
	if(yakitStokKodlari.indexOf(stokKod) != -1){ // is in array	
	
		if(kilometre=="" || kilometre==0 || isNaN(kilometre)){
			alert("Yakıt alımında mutlaka Kilometre yazılmalıdır!");
			return false;
		}
		
	} 
	
	
	
	

    if(document.getElementById("aciklama").value==""){
        alert("Açıklama boş geçilemez!\nİşlem iptal edilecek.");
		hataVar = true;
    }

    if(document.getElementById("miktar").value==0){
        alert("Miktar SIFIR'dan büyük olmalı!\nİşlem iptal edilecek.");
		hataVar = true;
    }

    if(document.getElementById("birim").value=="---"){
        alert("Birim boş geçilemez!\nİşlem iptal edilecek.");
		hataVar = true;
    }

    if(document.getElementById("hizmetKod").value=="" && document.getElementById("stokKod").value==""){
        alert("Masraf Kod VEYA Stok Kod alanlarından en az bir tanesi mutlaka seçilmelidir!\nDetay bilmiyorsanız MUHASEBE ile görüşün!");
		hataVar = true;
    }
	
	
	if(document.getElementById("ilgiliAlim2").value==""){
        alert("Ne için alındı alanı boş bırakılamaz!");
		hataVar = true;
    }




   

    if(hataVar){
        alert("Veri giriş hatası!\nİşlem iptal edildi.");
        return false;


    }else {
        //document.getElementById("kisiBilgileri").value = kisilerListesi;
        sender.style.display = "none";
        return true;
    }   


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}

function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>


<br>



<?php

function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gorevKilitliMi($gorevSn){

    //Tamamlandı mı?

    global $db;

    $donenDurum='f';

    $sorgu="SELECT tamamlandi FROM gorevlendirme WHERE sn=$gorevSn;";

	$ret = pg_query($db, $sorgu);

    $row = pg_fetch_row($ret);

    //echo $row[0] . "-". $row[1]."-".$row[2];
    while($row = pg_fetch_row($ret)){
        $donenDurum = $row[0];  
    }

    return $donenDurum;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }

 function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret)){
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}

function kilitliMi($gelenMod){
    if($gelenMod == "Görüntüle") return ' onclick="return false;" onkeydown="return false;" '; else return '';
}

function textKilitliMi($gelenMod){
    if($gelenMod == "Görüntüle") return ' readonly '; else return '';
}


function fileButtonKilitliMi($gelenMod){
    if($gelenMod == "Görüntüle") return ' disabled '; else return '';
}

function satianlmaciAtanmisMi($gelentalepDetayNo){

    global $db;

    $sorgu="SELECT isleme_alindi FROM talep_detay WHERE sn = $gelentalepDetayNo;";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Talep Detay bulunamadı";
      exit;
   }     

   while($row = pg_fetch_row($ret))   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger; 

}

function hizmetAdVer($gelenHizmetKod){

    global $aktifDB;

    $sorguCumle ="SELECT ADI FROM HIZMET 
                WHERE BLKODU = $gelenHizmetKod;";


    try {
        $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
        $ret = $dbh->query($sorguCumle);        

        

        foreach($ret as $row) { 
            $donenDeger = utf8MetinYap($row[0]);
        }
    
    //VT sıfırlanıyor...
    $dbh = null;
    $ret = null;
    
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";        
        die();
    } 


    return $donenDeger;  
}

function StokAdVer($gelenStokKod){

    global $aktifDB;

    $sorguCumle ="SELECT STOK_ADI FROM STOK 
                WHERE STOKKODU = '$gelenStokKod';";

    $sorguCumle = isoMetinYap($sorguCumle);

    try {
        $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
        $ret = $dbh->query($sorguCumle);        

        

        foreach($ret as $row) { 
            $donenDeger = utf8MetinYap($row[0]);
        }
    
    //VT sıfırlanıyor...
    $dbh = null;
    $ret = null;
    
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";        
        die();
    } 


    return $donenDeger;  
}


//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */
//************************************************************************************************************ */

//Bağlantı Yapılıyor... 

require("AkinsoftIslemleri.php");
require("mesajlasma.php");
require("logYaz.php");
require("dosyalariYukle.php");
require("sqlMetinDuzenle.php");
require("AkinsoftBirimleriVer.php");
require("BelgesiVarMi.php");



require('reloadKontrol2.php');





$gelenTalepSn = $_POST['talepSn'];
$gelenKayitMod = $_POST['kayitMod'];
$gelenIslemTip = $_POST['islemTip']; //Burası düzenleme modunda varolan kaydın değişmesi için!

$gelenKonu = sqlMetinDuzenle($_POST["konu"]);

//$gelenDetay = $_POST['detay'];
$gelenDetay = sqlMetinDuzenle($_POST["detay"]);

$gelenTalepEden = $_POST['talepEden'];
$gelenSube = $_POST['isteyenSube'];

//$gelenAciklama =  $_POST['aciklama'];
$gelenAciklama = sqlMetinDuzenle($_POST["aciklama"]);

$gelenMiktar =  $_POST['miktar'];
$gelenBirim =  $_POST['birim'];
$gelenTerminTarih =  $_POST['terminTarih'];
$gelenDetayGiris =  $_POST['detayGiris'];

$gelenTalepDetaySn = $_POST['talepDetaySn'];

$gelenSilinecekTalepDetaySn = $_POST['silinecekTalepDetaySn'];

$gelenHizmetKod = $_POST['hizmetKod'];
$gelenHizmetAd = $_POST['hizmetAd'];

$gelenStokKod = $_POST['stokKod'];
$gelenStokAd = $_POST['stokAd'];


$target_dir = "/mnt/talepBelgeler/";


$gelenIlgiliAlim = $_POST['ilgiliAlim'];

$gelenCariKod = $_POST['cariKod'];
$gelenBorclanacakCariKod = $_POST['borclanacakCariKod'];


//Sonradan Eklenen
$gelenIlgiliAlim2 = $_POST['ilgiliAlim2'];
$gelenIlgiliAlimAd  = $_POST['ilgiliAlimAd'];


$gelenKilometre = $_POST['kilometre'];




if($gelenMiktar=="")$gelenMiktar=1;

if($gelenTalepSn=="") $gelenTalepSn = 0;

if($gelenTerminTarih=='') $gelenTerminTarih = date("Y-m-d"); //"2021-12-31";





//-------------------------------
if(isset($_POST['acilTalep'])) { // checkbox seçilmişse "on" değeri gönderiliyor
    //echo 'Onayladınız!';
    $gelenAcil = 'TRUE';
} else { // seçilmemişse bu değer sayfaya hiç gönderilmiyor
    //echo 'Onaylamadınız.';
    $gelenAcil = 'FALSE';
}





//Burada düzenleme modunda talep tablosu bilgileri çekiliyor.

//******************************* */
//******Talep tablosu okunuyor.** */
//******************************* */


if($gelenKayitMod=="Düzenle" && $gelenTalepSn<>"" && $gelenDetayGiris==""){

    $sorguCumle ="SELECT konu, sube FROM talep WHERE sn=$gelenTalepSn;";
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    }

    while($row = pg_fetch_row($ret)){
        $gelenKonu = str_replace("'", "''", $row[0]);
        $gelenSube = $row[1];        
    }

    //Detaydan bilgi çekiyor

    $sorguCumle ="SELECT ilgili_alim FROM talep_detay WHERE sn=$gelenTalepDetaySn;";
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo "Hata 155: " . pg_last_error($db);
        exit;
    }

    while($row = pg_fetch_row($ret)){
        $gelenIlgiliAlim = sqlMetinDuzenle($row[0]);        
    }




}



//******************************* */
//**********Detay silme********** */
//******************************* */

if($gelenKayitMod=="detaySil" && $gelenSilinecekTalepDetaySn<>""){

    $sorguCumle ="DELETE FROM talep_detay WHERE sn=$gelenSilinecekTalepDetaySn;";
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    $gelenKayitMod="Yeni Kayıt";
    $gelenTalepDetaySn = "";

    $gelenAciklama = "";
    $gelenMiktar = "1";
    $gelenBirim = "";
    $gelenIlgiliAlim = "";

    
    

}









//Bu durumda doğrudan yeni kayıt oluyor.
if($gelenKayitMod=="Yeni Kayıt" && $gelenTalepSn==0){

    $sorguCumle = "INSERT INTO talep(konu, detay, talep_eden, kullanici, sube, cari_kod, borclanacak_cari_kod, acil) 
                   VALUES('$gelenKonu', '$gelenDetay', '$gelenTalepEden', '$kullanici', '$gelenSube', '$gelenCariKod', '$gelenBorclanacakCariKod', $gelenAcil) RETURNING sn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo "Hata 122: " .  pg_last_error($db);
        exit;
    }

    while($row = pg_fetch_row($ret)){
        $gelenTalepSn = $row[0];	       
    }

    //Buradan sonra GÖREV oluşturulacak.
    //SIFIR değilse bir kayıt

    if($gelenTalepSn!=0){

        $gelenKonuX= $gelenKonu . " (Satınalmadan Oluşturulmuştur)";

        //Görev oluşturuluyor.
        $sorguCumle="INSERT INTO gorevlendirme(gorev_veren, gorev_konu, hedef_zaman, gorev_detay, talep_sn) 
                VALUES('$kullanici', '$gelenKonuX', '2025-12-31', '$gelenDetay', $gelenTalepSn) RETURNING sn;";


                $ret = pg_query($db, $sorguCumle);

                if(!$ret){
                    echo pg_last_error($db);
                    exit;
                }

                while($row = pg_fetch_row($ret)){
                    $gelenGorevSn = $row[0];	       
                }



        //******************************************** */
        // Otomatik Görev oluşturuluyor. 
        //******************************************** */

        //Burada ön tanımlı satınalma ilgilileri giriliyor.
        $sorguCumle="INSERT INTO gorev_alan(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, 'abilgin')";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            echo "Görev Alanlar kaydedilemedi!";
            exit;
         }

         //İkinci kişi

        $sorguCumle="INSERT INTO gorev_alan(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, 'tsecilgin')";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            echo "Görev Alanlar kaydedilemedi!";
            exit;
        }

        //Talebi oluşturan kişi

        $sorguCumle="INSERT INTO gorev_alan(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, '$kullanici')";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            echo "Görev Alanlar kaydedilemedi!";
            exit;
        }




        //******************************************** */

        //Burada ön tanımlı satınalma ilgilileri giriliyor.
        $sorguCumle="INSERT INTO gorev_gorebilen(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, 'abilgin')";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            echo "Görev Alanlar kaydedilemedi!";
            exit;
        }

        //İkinci kişi

        $sorguCumle="INSERT INTO gorev_gorebilen(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, 'tsecilgin')";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            echo "Görev Alanlar kaydedilemedi!";
            exit;
        }


        //Talebi oluşturan kişi

        $sorguCumle="INSERT INTO gorev_gorebilen(gorev_sn, kullanici_kod) VALUES($gelenGorevSn, '$kullanici')";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            echo "Görev Alanlar kaydedilemedi!";
            exit;
        }



        //******************************************** */
        // Otomatik Görev oluşturuluyor SONU. 
        //******************************************** */

        //******************************************** */
        // Burada Edip Beye onay için mesaj gönderilecek. 
        //******************************************** */

        logYaz($gelenTalepSn . " Numara ile yeni talep ve ". $gelenGorevSn." numara ile ilişkili Yeni Görevlendirme oluşturuldu!");

        $mesajX = $kullanici ." Kullanıcısı ". $gelenTalepSn . " Numara ile yeni talep oluşturuldu.\n";     
		
		
		//Edip beyin isteği ile iptal edildi!
		//2025/02/26
		/*

		

        $sallaTel = []; 
        array_push($sallaTel, kullanicidanBilgiVer('cep_tel', 'eguler'));       

        //kisaMesajGonder($mesajX, $sallaTel, 0);

        //İkinci kişi için yeniden hazırlanıyor...

        $mesajX = $gelenTalepSn . " Numara ile yeni talep oluşturuldu.\n".  paslamaKoduVer('abilgin', 'talepOnay.php', '', '');       

        $sallaTel = []; 
        array_push($sallaTel, kullanicidanBilgiVer('cep_tel', 'abilgin'));       

        //kisaMesajGonder($mesajX, $sallaTel, 0);
		*/


        //Eposta gönderiliyor...
        $gidenKimden="<EMAIL>";
        $gidenAdresler = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];        
        $gidenBilgi="Yeni TALEP Girildi!";
        epostaGonder($gidenKimden, $gidenAdresler, $gidenBilgi, $mesajX);    




        //************************************************ */
        // Mesajlaşma sonu.
        //************************************************ */




        //Burada görev numarası talep tablosuna işleniyor ve işlem bitiyor.

        $sorguCumle ="UPDATE talep SET gorev_sn = $gelenGorevSn WHERE sn = $gelenTalepSn;";

        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            exit;
        }

    }


}

//Bu durumda Talep detay yeni girişi oluyor. 
if($gelenKayitMod=="Yeni Kayıt" && $gelenDetayGiris == 1){


    if($gelenHizmetKod == "") $gelenHizmetKod = 0;
	if($gelenIlgiliAlim2=="") $gelenIlgiliAlim2 = 0;
	if($gelenKilometre=="") $gelenKilometre = 0;

    

    $sorguCumle = "INSERT INTO talep_detay(talep_sn, aciklama, miktar, birim, termin_zaman, kullanici, ilgili_alim, hizmet_kod, stok_kod, ilgili_alim_2, kilometre) 
                    VALUES($gelenTalepSn, '$gelenAciklama', $gelenMiktar, '$gelenBirim', '$gelenTerminTarih', '$kullanici', '$gelenIlgiliAlim', $gelenHizmetKod, '$gelenStokKod', $gelenIlgiliAlim2, $gelenKilometre) RETURNING sn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo "Hata 12: <br> $sorguCumle ".  pg_last_error($db);
        exit;
    }

    while($row = pg_fetch_row($ret)){
        $gelenTalepDetaySn = $row[0];	       
    }

    //Değerler sıfırlanıyor.
    $gelenAciklama =  "";
    $gelenMiktar =  1;
    $gelenBirim =  "";
    $gelenTerminTarih =  date("Y-m-d"); //"2021-12-31";
    $gelenIlgiliAlim = "";
    $gelenHizmetKod = "";
    $gelenHizmetAd = "";
    $gelenStokKod = "";
    $gelenStokAd = "";
	$gelenIlgiliAlim2 = "";
	$gelenIlgiliAlimAd = "";
	$gelenKilometre = "";
   





    //Dosya geliyorsa kaydedecek.
    dosyalariYukle("/mnt/talepBelgeler/".$gelenTalepSn."/".$gelenTalepDetaySn."/", "Talep"); 




} //Bu durumda Talep detay yeni girişi sonu. 


//Düzenleme modunda kayıt yapılıyor.
if($gelenKayitMod=="Düzenle" && $gelenDetayGiris == 1 && $gelenTalepDetaySn <>""){

    if($gelenHizmetKod == "") $gelenHizmetKod = 0;
	if($gelenKilometre == "") $gelenKilometre = 0;

    $sorguCumle = "UPDATE talep_detay SET aciklama = '$gelenAciklama', 
                                          miktar = $gelenMiktar, 
                                          birim = '$gelenBirim', 
                                          ilgili_alim = '$gelenIlgiliAlim',
                                          termin_zaman = '$gelenTerminTarih',
                                          onay = FALSE,
                                          onay_zaman = NULL,
                                          onay_kullanici = '',
                                          hizmet_kod = $gelenHizmetKod,
                                          stok_kod = '$gelenStokKod',
										  ilgili_alim_2 = $gelenIlgiliAlim2,
										  kilometre = $gelenKilometre										  
									  WHERE sn = $gelenTalepDetaySn;";   
    
    
    $ret = pg_query($db, $sorguCumle);

    if($ret){
        logYaz($gelenTalepDetaySn . " Numaralı Talep Detay üzerinde değişiklik yapıldı.");
        echo "<script> alert('Değişiklik tamamlandı.');window.close(); </script>";
        
    }else {
        echo "Hata1213: " . pg_last_error($db);
        exit();
    }


    //Dosya geliyorsa kaydedecek.
    dosyalariYukle("/mnt/talepBelgeler/".$gelenTalepSn."/".$gelenTalepDetaySn."/", "Talep"); 



    

}



//Bu durumda varolan talebe yeni detay giriliyor.
if($gelenKayitMod=="Yeni Kayıt" && $gelenDetayGiris == 2){

    
    //Değerler sıfırlanıyor.
    $gelenAciklama =  "";
    $gelenMiktar =  1;
    $gelenBirim =  "";
    $gelenTerminTarih =  date("Y-m-d"); //"2021-12-31";    
    $gelenIlgiliAlim = "";
    $gelenHizmetKod = "";
    $gelenHizmetAd = "";

      

} //Bu durumda Talep detay yeni girişi sonu. 



//Bu durumda sadece GÖRÜNTÜLEME.
if($gelenKayitMod=="Görüntüle" && $gelenTalepDetaySn<>""){

    echo "uğradı.";

    //Öncelikle master kayıt alınıyor.

    $sorguCumle="SELECT konu, detay, sube FROM talep WHERE sn=$gelenTalepSn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    while($row = pg_fetch_row($ret)){

        $gelenKonu = $row[0];
        $gelenDetay = $row[1];
        $gelenSube = $row[2];

    }

    //Öncelikle detay kayıt alınıyor.

    $sorguCumle="SELECT aciklama, miktar, birim, termin_zaman, hizmet_kod FROM talep_detay WHERE sn=$gelenTalepDetaySn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    while($row = pg_fetch_row($ret)){

        $gelenAciklama = $row[0];
        $gelenMiktar = $row[1];
        $gelenBirim = $row[2];
        $gelenTerminTarih = $row[3];
        $gelenHizmetKod = $row[4];
        $gelenHizmetAd = hizmetAdVer($row[4]);

    }



}

echo '<h1 style="text-align:center;">Talep Detay Giriş ('.$gelenKayitMod .')</h1>';

//Gelen Değerler alınıyor...
/*


$gelenTip = $_POST['tip'];
$gelenKriterTum = $_POST['kriterTum'];

$takas = explode(" - ", $gelenKullanici);

$gelenKullaniciKod = trim($takas[1]);

*/

//if($gelenGorevTarih=='') $gelenGorevTarih = date("Y-m-d"); //"2021-12-31";

//$sayac = 1;

//echo "<center>";

echo '<form action="#" method="post" enctype="multipart/form-data">';
echo "<br>";
echo "<b>Talep Sn:</b><br>";
echo '<input type="text" id="talepSn" readonly name="talepSn" value="'.$gelenTalepSn.'"><br>';


echo "<b>Konu:</b><br>";
echo '<input type="text" id="konu" readonly style="font-family: Arial;font-size: 12pt; width:100%;height:100%"  name="konu" value="'.$gelenKonu.'"><br>';
//if($gelenKayitMod=="Düzenleme") echo '<input type="text" id="fname" name="fname" value="'.$gelenHikayeSn.'">';
/*
echo "<b>Detay: </b>";
echo '<textarea type="text" size="150" id="detay" name="detay" readonly maxlength="1000000" style="font-family: Arial;font-size: 12pt; width:100%;height:100%">';
echo $gelenDetay.'</textarea>';
*/
echo "<br>";
echo "<b>Talep Eden Şube:</b><br>";
echo '<input type="text" id="isteyenSube" readonly style="width:100%;height:100%" name="isteyenSube" value="'.$gelenSube.'"><br>';

echo "<hr>";


//Buradan sonrası Talep detay girişi için.

echo "<b>Talep Detay Sn:</b><br>";
echo '<input type="text" id="talepDetaySn" readonly name="talepDetaySn" value="'.$gelenTalepDetaySn.'"><br><br>';


echo "<b>Satın Alınacak Malzeme: </b>";
echo '<textarea type="text" size="150" autofocus id="aciklama" '.textKilitliMi($gelenKayitMod).' name="aciklama" maxlength="1000000" style="font-family: Arial;font-size: 12pt; width:100%;height:100%">';
echo $gelenAciklama.'</textarea><br>';


echo "<br><b>Miktar: </b><br>";
echo '<input type="number" id="miktar" style="text-align:right"  '.textKilitliMi($gelenKayitMod).'  name="miktar" maxlength="10" size="10" min="0" value="'.$gelenMiktar.'" title="Talep Miktarı girilebilir. :)" step="0.01">';



echo "<br><br><b>Birim: </b><br>";
echo '<select name="birim" id="birim">';

if($gelenBirim=="") echo '<option value="---" selected >---</option>';


    foreach (birimListesiVer() as $key => $birim) {
        echo '<option value="' . $birim . '"' . selectedKoy($gelenBirim, $birim). '>'.$birim.'</option>';	           
    }

echo "</select>";
echo "<br><br>";




//echo "<br><br><b>Birim:</b><br>";
//echo '<input type="text" id="birim"  '.textKilitliMi($gelenKayitMod).'  name="birim" value="'.$gelenBirim.'"><br>';

echo "<br><b>Ne için Alındı (Eski):</b><br>";
echo '<input type="text" id="ilgiliAlim" readonly name="ilgiliAlim" value="'.$gelenIlgiliAlim.'" style="width:100%;height:100%"><br>';



echo "<br><b>Ne için Alındı (Parametrik):</b><br>";

if($gelenKayitMod=="Düzenleme"){
	echo '<input type="text"  id="ilgiliAlim2" readonly name="ilgiliAlim2" size="4" value="'.$gelenIlgiliAlim2.'"><br>';
	
}else{
	echo '<input type="text" id="ilgiliAlim2" onclick="modalGoster(2);" readonly size="4"  name="ilgiliAlim2" value="'.$gelenIlgiliAlim2.'" style="background-color : #fff333;height:100%"/>'.bosluk . bosluk.'<label id="ilgiliAlimAd">'.$gelenIlgiliAlimAd.'</label><br>';
	
}


echo "<br><br><hr>";

echo "<font style='color:red;'>";
echo "<b>Aynı anda Sadece bir seçenek girilebilir! Stok kodu veya Hizmet kodu <br><br></b>";
echo "</font>";

echo "<b>Stok Kod: </b><br>";
echo '<input type="text" id="stokKod" style="background-color : #fff333;" onclick="modalGoster(1);" readonly size="20" name="stokKod" value="'.$gelenStokKod.'">'.bosluk . bosluk.'<label id="stokAd">&nbsp;'.$gelenStokAd.'</label>';


echo bosluk . bosluk.'<button type="button" onclick="yeniStokEkle(this);"><img src="add-icon.png" height="20" width="20" title="Kayıt Ekle" valign="middle"/></button> <br><br>';

echo "<b>Hizmet Kod: </b><br>";
echo '<input type="text" id="hizmetKod" style="background-color : #fff333;" onclick="modalGoster(0);" readonly size="4" name="hizmetKod" value="'.$gelenHizmetKod.'">'.bosluk . bosluk.'<label id="hizmetAd">&nbsp;'.$gelenHizmetAd.'</label><br>';


echo "<br><b>Araç Kilometresi: </b><br>";
echo '<input type="number" id="kilometre" style="text-align:right;" '.textKilitliMi($gelenKayitMod).'  name="kilometre" maxlength="5" size="15" min="0" value="'.$gelenKilometre.'" title="Yakıt alınmış ise Araç Kilometresi girilmelidir!" step="1"><br><br>';



echo "<hr>";


echo "<br><b>Termin: </b><br>";
echo '<input type="date" id="terminTarih"  '.textKilitliMi($gelenKayitMod).'  name="terminTarih" value="'.$gelenTerminTarih.'" title="Termin Zamanı">';

echo "<br><br><br>";
echo "<b>Dosya Ekle: </b><br>";
echo '<input type="file" '.fileButtonKilitliMi($gelenKayitMod).' name="fileToUpload[]" id="fileToUpload" multiple>';

echo "<br><br><br>";
echo '<input type="hidden" name="kayitMod" value="'.$gelenKayitMod.'">';  
echo '<input type="hidden" name="talepSn" value="'.$gelenTalepSn.'">';      
echo '<input type="hidden" name="talepDetaySn" value="'.$gelenTalepDetaySn.'">';      
echo '<input type="hidden" name="ilgiliAlimAd" id="ilgiliAlimAd2" value="'.$gelenIlgiliAlimAd.'">';      
echo '<input type="hidden" name="detayGiris" value="1">'; 


require('reloadKontrol1.php');

echo '<input type="hidden" id="hizmetAd2" name="hizmetAd" value="'.$gelenHizmetAd.'">';
echo '<input type="hidden" id="stokAd2" name="stokAd" value="'.$gelenStokAd.'">';

if($gelenKayitMod<>"Görüntüle"){
	//require('reloadKontrol1.php');
	$x_form_check=180;
	$_SESSION['x_form_check']=$x_form_check;
	echo '<input type="hidden" value='.$x_form_check.' name="x-form-check"/>';
	
    echo '<input type="submit" style="width:200px;" value="Kaydet" onclick="return veriKontrol(this);">';
}  

//echo "</center>";
/*
if($yeniYetki == 't'){
    //echo '<input type="hidden" id="islemMod" name="islemMod" value="Kaydet">';
	//echo '<input type="submit" class= "tusBosluk" value="Kaydet" formaction="isTanimDuzenle.php">';	
}
*/
echo '</form>';


//echo '<input type="button" value="Click Me" style="float: right;">';


echo "<br><br>"; 

if($gelenKayitMod=="Yeni Kayıt"){

    echo '<form action="index.php" method="post">';
    echo '<input type="submit" value="Ana Menüye Dön" style="width:200px;">';
    echo '</form>';    
    echo "<br>";
}else {
    //Kapat
    echo '<button type="button" style="width:200px;" onclick="window.close();">Kapat</button> ';    
    echo "<br><br>";
    
    
}






$sorguCumle = "SELECT talep_detay.sn, 
					  talep_sn, 
					  aciklama, 
					  miktar, 
					  birim, 
					  termin_zaman, 
					  talep_detay.yaratma_zaman, 
					  talep_detay.duzeltme_zaman, 
					  talep_detay.kullanici, 
					  ilgili_alim, 
					  hizmet_kod, 
					  stok_kod, 
					  talep_detay.ilgili_alim_2, /*12*/
					  ilgili_alim.ilgili_alim_ad   /*13*/
                FROM talep_detay
				LEFT JOIN ilgili_alim ON ilgili_alim.sn = talep_detay.ilgili_alim_2 
                WHERE talep_sn = $gelenTalepSn AND iptal = FALSE 
                ORDER BY sn DESC;";

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
} 


echo '<table class= "hoverTable" valign="middle" id="tableMain">';
echo "  <tr>";
echo "    <th style='text-align:center'>S/N</th>";
echo "    <th style='text-align:center'>Talep S/N</th>";
echo "    <th style='text-align:center'>Açıklama</th>";
echo "    <th style='text-align:center'>Miktar</th>";
echo "    <th style='text-align:center'>Birim</th>";
echo "    <th style='text-align:center'>Ne için Alındı</th>";
echo "    <th style='text-align:center'>Termin</th>";
echo "    <th style='text-align:center'>Yaratma</th>";
echo "    <th style='text-align:center'>Düzenleme</th>";
echo "    <th style='text-align:center'>Kullanıcı</th>";
echo "    <th style='text-align:center'>İşlem</th>";
echo "  </tr>";

while($row = pg_fetch_row($ret)){
	echo "<tr>";
    echo "<td style='text-align:center'>". $row[0] . "</td>";   
    echo "<td style='text-align:center'>". $row[1] . "</td>";    
    echo "<td>". $row[2] . "</td>";          
    echo "<td style='text-align:center'>". $row[3] . "</td>";
    echo "<td style='text-align:center'>". $row[4] . "</td>";  
    echo "<td style='text-align:center'>". $row[9] ." " .$row[13]  . "</td>";  
    
	echo "<td style='text-align:center'>". tarihYaz($row[5]) . "</td>";  

    echo "<td style='text-align:center'>". tarihSaatYaz($row[6]) . "</td>";  
    echo "<td style='text-align:center'>". tarihSaatYaz($row[7]) . "</td>";  

    echo "<td>". $row[8] . "</td>";  



	echo "<td style='vertical-align: middle; text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";

    


    //Tamamlanmış göreve müdahale edemiyor.
    
/*
            if($row[4] == $kullanici && $silYetki=='t'){

                //Silme
                echo '<form action="GorevHikayeSil.php" method="post" class="inline">';// id="form3"
                echo '<input type="hidden" name="gorevSn" value="'.$gelenGorevSn.'">';            
                echo '<input type="hidden" name="gorevHikayeSn" value="'.$row[0].'">';            
                echo '<button type="submit" onclick="return silmeOnay();"><img src="Delete_Icon.png" height="20" width="20" title="Görev Sil==>'.$row[0].'" valign="middle"/></button>';
                //echo '<button type="submit">Hikaye</button>';
                echo '</form>';

                //echo '<button form="form3" class="tusBosluk2"><img src="Delete_Icon.png" height="20" width="20" title="Görev Sil==>'.$row[6].'" valign="middle" /> </button>';


            }
            */

            if($duzeltYetki=='t'&& $gelenKayitMod<>"Görüntüle" && satianlmaciAtanmisMi($row[0])=='f' ){
                //Düzenle Tuşu 
                echo '<form action="#"  method="post" class="inline">';                
                echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';            
                echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">';
                echo '<input type="hidden" name="aciklama" value="'.$row[2].'">';
                echo '<input type="hidden" name="miktar" value="'.$row[3].'">';
                echo '<input type="hidden" name="birim" value="'.$row[4].'">';
                echo '<input type="hidden" name="ilgiliAlim" value="'.$row[9].'">';
                echo '<input type="hidden" name="terminTarih" value="'.$row[5].'">';
                echo '<input type="hidden" name="hizmetKod" value="'.$row[10].'">';
                echo '<input type="hidden" name="hizmetAd" value="'.hizmetAdVer($row[10]).'">';
                echo '<input type="hidden" name="stokKod" value="'.$row[11].'">';
                echo '<input type="hidden" name="stokAd" value="'.stokAdVer($row[11]).'">';
				echo '<input type="hidden" name="ilgiliAlim2" id="ilgiliAlim2" value="'.$row[12].'">';      
				echo '<input type="hidden" name="ilgiliAlimAd" id="ilgiliAlimAd3" value="'.$row[13].'">';      
				
				require('reloadKontrol11.php');
                
                //Bu kısım master kayıt için aktarılıyor.                           
                echo '<input type="hidden" name="konu" value="'.$gelenKonu.'">';         
                echo '<input type="hidden" name="detay" value="'.$gelenDetay.'">';         
                echo '<input type="hidden" name="isteyenSube" value="'.$gelenSube.'">'; 
                //-----------------                
                echo '<input type="hidden" name="kayitMod" value="Düzenle">';                                        
                echo '<button type="submit"><img src="EditIcon.png" height="20" width="20" title="Detay Dzüenle==>'.$row[0].'" valign="middle"/></button>';
                echo '</form>';
            }            

            if($silYetki=='t'&& $gelenKayitMod<>"Görüntüle" && satianlmaciAtanmisMi($row[0])=='f'){
                //Silme Tuşu       	

                echo '<form action="#"  method="post" class="inline" onclick="return ConfirmIslem(\'Detay Silinecek?!\')">';                
                echo '<input type="hidden" name="talepSn" value="'.$gelenTalepSn.'">';            
                echo '<input type="hidden" name="konu" value="'.$gelenKonu.'">';         
                echo '<input type="hidden" name="detay" value="'.$gelenDetay.'">';       
				
				require('reloadKontrol11.php');

				
                echo '<input type="hidden" name="isteyenSube" value="'.$gelenSube.'">';  
                echo '<input type="hidden" name="kayitMod" value="detaySil">';                        
                echo '<input type="hidden" name="silinecekTalepDetaySn" value="'. $row[0].'">';                             
                echo '<button type="submit"><img src="Delete_Icon.png" height="20" width="20" title="Detay Sil==>'.$row[0].'" valign="middle"/></button>';
                echo '</form>';
            }



            //Belgesi varsa geliyor.

            if(belgesiVarMi("talepBelgeler", $row[1], $row[0])==true){
                    
                //Belgeleri Göster
                    echo '<form action="TalepBelgeleri.php"  target="_blank"  method="post"  class="inline">'; 
                    echo '<input type="hidden" name="talepSn" value="'.$row[1].'">';    
                    echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 
                    echo '<input type="hidden" name="kaynak" value="talepDetayListesi.php">';  
                    echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$row[0].'"/></button>';
                    //echo '<button type="submit">Detay</button>';
                    echo '</form>';
                }

            /*

                //Belge Ekle tuşu     

                
                echo '<form action="gorevlendirmeBelgeYukle.php"  method="post" enctype="multipart/form-data" class="inline">';
                echo '<input type="hidden" name="gorevSn" value="'.$gelenGorevSn.'">';    
                echo '<input type="file" name="fileToUpload[]" id="fileToUpload" multiple>';
                echo '<button type="submit" value="Upload Image" name="submit"><img src="upload-file.svg" height="20" width="20" title="Belge Ekle==>'.$row[0].'" valign="middle"/></button>'; //
                //echo '<button type="submit">Hikaye</button>';
                echo '</form>';

            //Detay tuşu sonu
            
            */

    

    echo "</div>";

    echo "</td>";

	echo "<tr>";

}

echo'</table>';

echo "<br><br><br>";


echo '<form action="AkinsoftStokOlustur.php" method="post" target="_blank" id="stokEklemeForm">';
echo '<input type="hidden" name="geriDon" value="1">';  
echo "</form>";




//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';











pg_close($db);

//echo "<hr><br><br><b>Dip Toplam :$toplam TL </b><hr>";   

//echo "<br>";
//echo "***Tüm geçmiş siparişleriniz listelenmiştir.";

?>

</body>
</html>
