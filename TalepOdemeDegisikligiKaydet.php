<?php
/*
Muhasebe ekranından doğrudan OLMAYAN (!) harcamalarda sıkıntı olduğunda bu rutin ile kaydı yapılır.
*/
require('yetki.php');
$ekranKod = 65;
//require_once('ekranYetkileri.php');
$kullanici = $_SESSION["user"];

require("PGConnect.php");
require("logYaz.php");


$gelenTabloBilgileri = $_POST['tabloBilgileri'];
$gelenTabloBilgileriIlk = $_POST['tabloBilgileriIlk'];

if($gelenTabloBilgileri == "" || $gelenTabloBilgileriIlk == "" ){
	echo "Veri hatası!";
	pg_close($db);
	exit;
	
}

//echo $gelenTabloBilgileri;


$donenDeger = true;


$dilimler = explode("|", $gelenTabloBilgileri);

foreach ($dilimler as &$dilimlerX){
    //echo $dilimlerX;

    $değer = explode(";", $dilimlerX); 
        

        $sorguCumle = "UPDATE talep_hareket SET nakit_odenen = $değer[1], 
                                                bankadan_odenen = $değer[2], 
                                                evrakla_odenen = $değer[3],
                                                kk_odenen = $değer[4],
                                                acik_odenen = $değer[5]
                                                WHERE sn= $değer[0];";

        //echo $sorguCumle;


        $ret = pg_query($db, $sorguCumle);

        if(!$ret){
            echo pg_last_error($db);
            echo "Talep Detay Ödeme Güncellenemedi!";

            $donenDeger = false;
            exit;
        }
        

}

unset($dilimlerX);


ob_clean();


if($donenDeger==true){
    logYaz("{$değer[0]} numaralı detay ödemede değişiklik yapıldı. İlk durum:{$gelenTabloBilgileriIlk} Düzenlenen durum: {$gelenTabloBilgileri}");
    echo "Ok";

}  else echo "Talep detay ödeme kaydına ulaşılamadı!";



pg_close($db);
?>