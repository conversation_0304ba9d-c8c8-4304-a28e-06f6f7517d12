#!/usr/bin/env node

/**
 * MEG Talep Onay Checker - Cron Job
 * talep_onay_log tablosunu kontrol eder ve TRIGGER_FIRED durumundaki kayıtları işler
 * Her dakika çalışacak şekilde cron'a eklenecek: * * * * * cd /path/to/meg/talep && node cron-checker.js
 */

const mysql = require('mysql2/promise');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// MySQL bağlantı konfigürasyonu (local)
const mysqlConfig = {
    host: 'localhost',
    user: 'mehmet',
    password: 'Mb_07112024',
    database: 'bormeg_msg',
    charset: 'utf8mb4',
    timezone: '+03:00'
};

// Log dosyası
const LOG_FILE = path.join(__dirname, 'cron-checker.log');
const TALEP_ONAYLA_SCRIPT = path.join(__dirname, 'talepOnayla.js');

/**
 * Log mesajı yaz
 */
function writeLog(message, level = 'INFO') {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    const logMessage = `[${timestamp}] [CRON-CHECKER] [${level}] ${message}\n`;
    
    console.log(logMessage.trim());
    
    try {
        fs.appendFileSync(LOG_FILE, logMessage);
    } catch (error) {
        console.error('Log yazma hatası:', error);
    }
}

/**
 * talepOnayla.js scriptini çalıştır
 */
function runTalepOnaylaScript(talepSn) {
    return new Promise((resolve, reject) => {
        const command = `node "${TALEP_ONAYLA_SCRIPT}" ${talepSn}`;
        
        exec(command, { timeout: 30000 }, (error, stdout, stderr) => {
            if (error) {
                reject(new Error(`Script çalışma hatası: ${error.message}`));
                return;
            }
            
            if (stderr) {
                writeLog(`Script stderr: ${stderr}`, 'WARN');
            }
            
            if (stdout) {
                writeLog(`Script stdout: ${stdout}`, 'INFO');
            }
            
            resolve();
        });
    });
}

/**
 * Bekleyen onay kayıtlarını işle
 */
async function processPendingApprovals() {
    let connection;
    
    try {
        connection = await mysql.createConnection(mysqlConfig);
        
        // TRIGGER_FIRED durumundaki kayıtları bul
        const selectQuery = `
            SELECT id, talep_sn 
            FROM talep_onay_log 
            WHERE durum = 'TRIGGER_FIRED' 
            ORDER BY islem_zaman ASC 
            LIMIT 10
        `;
        
        const [rows] = await connection.execute(selectQuery);
        
        if (rows.length === 0) {
            writeLog('Bekleyen onay işlemi bulunamadı');
            return;
        }
        
        writeLog(`${rows.length} bekleyen onay işlemi bulundu`);
        
        for (const row of rows) {
            try {
                // Durumu PROCESSING yap
                await connection.execute(
                    'UPDATE talep_onay_log SET durum = "PROCESSING", aciklama = CONCAT(COALESCE(aciklama, ""), " - İşlem başladı: ", NOW()) WHERE id = ?',
                    [row.id]
                );
                
                writeLog(`Talep SN=${row.talep_sn} için onay işlemi başlatılıyor (Log ID=${row.id})`);
                
                // talepOnayla.js scriptini çalıştır
                await runTalepOnaylaScript(row.talep_sn);
                
                // Başarılı olarak işaretle
                await connection.execute(
                    'UPDATE talep_onay_log SET durum = "SUCCESS", aciklama = CONCAT(COALESCE(aciklama, ""), " - İşlem başarılı: ", NOW()) WHERE id = ?',
                    [row.id]
                );
                
                writeLog(`Talep SN=${row.talep_sn} için onay işlemi tamamlandı (Log ID=${row.id})`, 'SUCCESS');
                
            } catch (error) {
                // Hata olarak işaretle
                await connection.execute(
                    'UPDATE talep_onay_log SET durum = "ERROR", aciklama = CONCAT(COALESCE(aciklama, ""), " - Hata: ", ?, " - ", NOW()) WHERE id = ?',
                    [error.message, row.id]
                );
                
                writeLog(`Talep SN=${row.talep_sn} için onay işlemi hatası (Log ID=${row.id}): ${error.message}`, 'ERROR');
            }
        }
        
    } catch (error) {
        writeLog(`Cron checker hatası: ${error.message}`, 'ERROR');
        throw error;
        
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

/**
 * Ana fonksiyon
 */
async function main() {
    try {
        writeLog('=== CRON CHECKER BAŞLADI ===');
        
        // talepOnayla.js dosyasının varlığını kontrol et
        if (!fs.existsSync(TALEP_ONAYLA_SCRIPT)) {
            throw new Error(`talepOnayla.js dosyası bulunamadı: ${TALEP_ONAYLA_SCRIPT}`);
        }
        
        await processPendingApprovals();
        
        writeLog('=== CRON CHECKER TAMAMLANDI ===');
        
    } catch (error) {
        writeLog(`=== CRON CHECKER HATASI ===`, 'ERROR');
        writeLog(`Hata: ${error.message}`, 'ERROR');
        process.exit(1);
    }
}

/**
 * Command line'dan çağrıldığında
 */
if (require.main === module) {
    main().catch((error) => {
        console.error('Beklenmeyen hata:', error);
        process.exit(1);
    });
}

module.exports = {
    main,
    processPendingApprovals,
    runTalepOnaylaScript
};