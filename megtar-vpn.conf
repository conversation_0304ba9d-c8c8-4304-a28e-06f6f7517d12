dev tun
persist-tun
persist-key
data-ciphers AES-256-GCM:AES-128-GCM:CHACHA20-POLY1305:AES-256-CBC
data-ciphers-fallback AES-256-CBC
auth SHA256
tls-client
client
resolv-retry infinite
remote *********** 1196 udp4
nobind
verify-x509-name "EvOfisTLSCA-Cert" name
auth-user-pass /etc/openvpn/client/auth.txt
pkcs12 /etc/openvpn/client/Megtar-pfSense-UDP4-1196-masik.p12
tls-auth /etc/openvpn/client/Megtar-pfSense-UDP4-1196-masik-tls.key 1
remote-cert-tls server
explicit-exit-notify
log /var/log/openvpn-client.log
daemon
