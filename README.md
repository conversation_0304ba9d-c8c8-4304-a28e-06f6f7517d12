# MEG Talep Takip Sistemi

Node.js tabanlı talep süreçlerini ve onay işlemlerini takip eden monitoring sistemi.

## Özellikler

- Son 20 talep detayının tablo halinde görüntülenmesi
- Onay süreçlerinin takibi
- PostgreSQL veritabanı entegrasyonu
- Otomatik log dosyası oluşturma
- Türkçe tarih formatlaması
- İstatistiksel özet bilgileri

## Kurulum

### 1. Gereksinimler
- Node.js (v14.0.0 veya üzeri)
- npm veya yarn
- PostgreSQL veritabanı erişimi

### 2. Bağımlılıkları Yükleyin
```bash
npm install
```

### 3. Veritabanı Yapılandırması
`dbconfig.js` dosyasını düzenleyerek PostgreSQL bağlantı bilgilerinizi girin:

```javascript
const dbConfig = {
    host: 'localhost',
    port: 5432,
    database: 'your_database_name',    // Veritabanı adınız
    user: 'your_username',            // Kullanıcı adınız
    password: 'your_password',        // Şifreniz
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
};
```

## Kullanım

### Tek Seferlik Çalıştırma
```bash
node process.js
```

### Sürekli İzleme (Development)
```bash
npm run dev
```

### /usr/src/ konumuna kopyalama
```bash
# Dosyaları /usr/src/ konumuna kopyalayın
sudo cp process.js /usr/src/
sudo cp dbconfig.js /usr/src/
sudo cp package.json /usr/src/

# Gerekli izinleri verin
sudo chmod +x /usr/src/process.js

# Bağımlılıkları yükleyin
cd /usr/src && sudo npm install

# Çalıştırın
sudo node /usr/src/process.js
```

## Çıktı Formatı

Script çalıştırıldığında `/usr/src/log.txt` dosyasında (veya yerel `log.txt`) aşağıdaki format kullanılır:

```
==================================================================================
MEG TALEP TAKİP SİSTEMİ - SON 20 TALEP DETAYI
Güncelleme Zamanı: 25.08.2025 14:30:15
==================================================================================

D.SN   | T.SN   | KONU                     | AÇIKLAMA            | MİKTAR   | BİRİM    | TALEP EDEN  | DURUM       | ONAYLAYAN   | ONAY ZAMAN       
----------------------------------------------------------------------------------
123    | 45     | Ofis Malzemeleri         | Kalem, Kağıt       | 100      | Adet     | johndoe     | ONAYLANDI   | admin       | 24.08.2025 10:15
124    | 46     | Temizlik Malzemesi       | Deterjan           | 5        | Şişe     | janedoe     | BEKLEMEDE   | -           | -               
```

## Durum Kodları

- **BEKLEMEDE**: Henüz onaylanmamış talepler
- **ONAYLANDI**: Onaylanmış ancak henüz tamamlanmamış talepler  
- **TAMAMLANDI**: Tamamlanmış talepler
- **İPTAL**: İptal edilmiş talepler

## Veritabanı Tabloları

Script aşağıdaki PostgreSQL tablolarını kullanır:

### `talep` tablosu
- `sn`: Talep sıra numarası
- `konu`: Talep konusu
- `talep_eden`: Talebi oluşturan kullanıcı
- `tamamlandi`: Talep tamamlanma durumu

### `talep_detay` tablosu
- `sn`: Detay sıra numarası
- `talep_sn`: Bağlı olduğu talep numarası
- `aciklama`: Detay açıklaması
- `miktar`: Talep edilen miktar
- `birim`: Miktar birimi
- `onay`: Onay durumu (boolean)
- `onay_kullanici`: Onaylayan kullanıcı
- `onay_zaman`: Onay verilen zaman
- `iptal`: İptal durumu (boolean)
- `iptal_kullanici`: İptal eden kullanıcı
- `tamamlandi`: Detay tamamlanma durumu
- `tamamlandi_zaman`: Tamamlanma zamanı
- `yaratma_zaman`: Oluşturulma zamanı
- `duzeltme_zaman`: Son güncelleme zamanı

## Hata Ayıklama

### Yaygın Hatalar

1. **Database connection error**
   - `dbconfig.js` dosyasındaki bağlantı bilgilerini kontrol edin
   - PostgreSQL servisinin çalıştığından emin olun

2. **Permission denied for /usr/src/**
   - Script otomatik olarak yerel dizine `log.txt` oluşturacak
   - Manuel olarak sudo ile çalıştırabilirsiniz

3. **Module not found**
   - `npm install` komutunu çalıştırdığınızdan emin olun

## Lisans

ISC License

## İletişim

MEG Development Team