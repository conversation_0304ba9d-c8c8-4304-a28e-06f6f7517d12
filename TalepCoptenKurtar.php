<?php
require('yetki.php');
$ekranKod = 287;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8 , width=device-width, initial-scale=1"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">

<script src="sorttable.js"></script>
<script src="jquery-3.6.0.js"></script>


<style style="text/css">

    .tusBosluk {
		margin-left :15px 
		}


    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }
	
	
	.sortable th{ 
        position:sticky;
        /*position: -webkit-sticky;*/
        top: 0;
        background: green;/*#727272;*/
        color: white; /*#c5c5c5;*/
        z-index: 0;

		/*padding:7px; border: #4e95f4 1px solid;*/
		/*#4e95f4 orjinal hali*/ 
}






    .inline {
        display: inline;
        margin-left :10px 
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}

    .tusBosluk2 {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }

    textarea {
	border: none;
	width: 100%;
   -webkit-box-sizing: border-box; /* <=iOS4, <= Android  2.3 */
      -moz-box-sizing: border-box; /* FF1+ */
           box-sizing: border-box; /* Chrome, IE8, Opera, Safari 5.1*/
    }




    /** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.topics tr { line-height: 30px; }




</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>

<script type="text/javascript">


function sifirEkle(gelen){//Gelen sayı olacak
	
	var n = gelen.toString().length;	
	//console.log(n);	
	if(n==1) return "0"+gelen;else return gelen;
	
}



function geri(gelenSayac){

    var trh2= 'tarih' + gelenSayac;

    var el = document.getElementById(trh2);

    if (el.value=='') return;	

    var tarih = new Date(el.value);

    tarih.setDate(tarih.getDate() -1);

    //console.log(tarih);

    var gun = tarih.getDate();
    //console.log(gun);
    var ay = tarih.getMonth() + 1;
    //console.log(ay);
    var yil = tarih.getFullYear();
    //console.log(yil);

    var yeniTarih = yil + '-'+ sifirEkle(ay) + '-' + sifirEkle(gun); 

    //console.log("Yeni  :" +yeniTarih);

    document.getElementById(trh2).value = yeniTarih; //"2014-02-09"

    //el.setDate(tarih);

    //console.log(yeniTarih);
    //console.log(ay);



}

function ileri(gelenSayac){

    var trh2= 'tarih' + gelenSayac;

    var el = document.getElementById(trh2);

    if (el.value=='') return;	

    var tarih = new Date(el.value);

    tarih.setDate(tarih.getDate() +1);

    //console.log(tarih);

    var gun = tarih.getDate();
    //console.log(gun);
    var ay = tarih.getMonth() + 1;
    //console.log(ay);
    var yil = tarih.getFullYear();
    //console.log(yil);

    var yeniTarih = yil + '-'+ sifirEkle(ay) + '-' + sifirEkle(gun); 

    //console.log("Yeni  :" +yeniTarih);

    document.getElementById(trh2).value = yeniTarih; //"2014-02-09"
}


function saatYap(){
   



    var trh1= 'tarih1' ;
    var trh2= 'tarih2' ;

    var t1 = document.getElementById(trh1);
    var t2 = document.getElementById(trh2);


    if(t1.value == "" || t2.value ==""){
        alert('Tarih aralığı seçilmelidir!');
        return false;
    }






    var tarih1 = new Date(t1.value);
    var tarih2 = new Date(t2.value);

    //console.log(tarih1);
    //console.log(tarih2);	


    if(tarih1>tarih2){
        alert('Tarih aralığı hatalı!');
    return false;		
    }




    document.body.style.cursor  = 'wait';

    //console.log("Debug Objects: Wait");

    return true;

}




//******************************* */



function ConfirmIslem(gelenMesaj){
	
	//console.log(gelenMesaj);

    if (confirm(gelenMesaj + " Onaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}





//Tüm kolonlarda arama

function searchTableColumns(gelenTablo, gelenAlan) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
      }      
}




function modalGoster(mod, gelenBlKod) {    

        
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {		
		
		document.getElementById("icerik").innerHTML = this.responseText;
		document.body.style.cursor  = 'default';
		
    }
    };

    switch (mod) {
        case 0:
            //Başlık yazar
			document.body.style.cursor  = 'wait';
            document.getElementById('baslik').innerHTML ="Alıştan İade Detayı";
            xhttp.open("POST", "AlisIadeDetayVer.php", true);
            xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
            xhttp.send("iadeSn="+ gelenBlKod);
            
            break;    
        case 1:
            //Başlık yazar
			document.body.style.cursor  = 'wait';
            document.getElementById('baslik').innerHTML ="Cari Hareket Detayı";
            xhttp.open("POST", "CariHareketDetayVer.php", true);
            xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
            xhttp.send("cariKod="+ gelenBlKod);
            
            break;    
        
    }

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
}



function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}


function veriTransfer(gelenMesaj){
    //document.getElementById('transferVerisi').innerHTML = gelenMesaj;

    var fields = gelenMesaj.split('-');

    //Dönen değeri yazıyor...
    
    document.getElementById("personelKod").value=fields[0];   
    document.getElementById("personelAd").value=fields[1];   


    modalKapat(); 
    //alert(gelenMesaj); 
}

function submitPostLink(){    
    document.getElementById('postlinkKonum').submit();
}


function klikle(){

    if(document.getElementById("detayGosterX").checked == true){
        document.getElementById("detayGoster1").value = 't';
        document.getElementById("detayGoster2").value = 't';
    }else{
        document.getElementById("detayGoster1").value = '';
        document.getElementById("detayGoster2").value = '';

    }
}

function tabloSatirGizle(tabloAd, gizlenecekSatir){

    var satirNo;

    var table = document.getElementById(tabloAd);

    //console.log(tabloAd);

    for (var i = 1, row2; row2 = table.rows[i]; i++){

        satirNo = row2.cells[0].innerText;

        if(satirNo==gizlenecekSatir) row2.style.display = "none";

    }

}



function tabloSatirGizle2(tabloAd, gizlenecekSatir){
	
	alert("Düzeltme sonrasında sayfayı yeniden tazelemeniz gerekir!");
	
	gizlenecekSatir = "WEB-MSP-" + gizlenecekSatir;

    var satirNo;

    var table = document.getElementById(tabloAd);

    //console.log(tabloAd);

    for (var i = 1, row2; row2 = table.rows[i]; i++){

        satirNo = row2.cells[0].innerText;

        if(satirNo==gizlenecekSatir){
			row2.style.color = "#FF0000";			
		} 

    }

}








function alisIadeSil(sender, tabloAd, gizlenecekSatir){
	
	if(!confirm(gizlenecekSatir + " numaralı Alış İade talebi silinecektir!\nOnaylıyor musunuz?")){
		alert("İşlem iptal edildi!");
		return false;
	}
	//alert(tabloAd);
	//alert(gizlenecekSatir);
	
	sender.style.display = "none";
	
	
	$.ajax({
        method: "POST",
        url: "AlisIadeSil.php",
        async: false,
        data: { gelenSn: gizlenecekSatir
            }        
        })
        .done(function( response ) {             
            response = response.trim();

            if(response == "Okkk"){                
                donenDeger = true;
                tabloSatirGizle(tabloAd, gizlenecekSatir);
				alert("Alış İade başarı ile silindi!");

                //return true;
                            
            } else{
				sender.style.display = "";
                alert("Kayıt silinemedi!\n"+response);
                //console.log("Hata");           
                donenDeger = false;                    
            }             
            
        });		
		
		
		return donenDeger;
	
	
}








</script>
</head>
<body>

<?php



function cariAdVer($gelenCariKod){

    global $aktifDB;

    //$gelenSubeAd = isoMetinYap($gelenSubeAd);

    $gelenSubeKod = isoMetinYap($gelenCariKod);

    $sorguCumle = "SELECT TICARI_UNVANI FROM CARI WHERE CARIKODU = '$gelenSubeKod'";

    $donenDeger = $sorguCumle;

    try {
        $dbh = new PDO($aktifDB , 'sysdba', 'masterkey');		        
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            $donenDeger = $row[0];            
        } 


        //VT sıfırlanıyor...
        $dbh = null;
        $ret = null;	

    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;			
        die();
    }

    return utf8MetinYap($donenDeger);

}


function stokAdVer($gelenStokKod){

    global $db;	

	$sorgu="SELECT stok_ad FROM stok_kart WHERE stok_kod='$gelenStokKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Stok Kodu bulunamadı";
        exit;
    } 

    $donenDeger="Stok Kodu bulunamadı!";   

    while($row = pg_fetch_row($ret))    {
        $donenDeger = $row[0];	   
    }   
    return $donenDeger;
}



function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isChecked($gelenGizliMi){

    if ($gelenGizliMi == 't') $donenDeger = " checked"; else $donenDeger = "";     
    

    return $donenDeger;
    
}

function tarihYaz($gelenTarih){

    if($gelenTarih=="") return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));


}

function tarihFormatla($gelenTarih){

    if($gelenTarih=="") return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}

function siparisTutarVer($gelenSiparisNo, $gelenKDV, $gelenDipIskonto){

    global $db;	

	$sorgu="SELECT SUM(satis_fiyat * miktar)
            FROM musteri_siparis_detay WHERE siparis_sn=$gelenSiparisNo;";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Siparis Kodu bulunamadı";
        exit;
    } 

    $donenDeger=-1;   

    while($row = pg_fetch_row($ret))    {
        $donenDeger = $row[0];	   
    }   

    return ($donenDeger *(1+$gelenKDV/100))- $gelenDipIskonto;



}


function faturaKesilmisMi($gelenSiparisNo){
	
	global $dbh;
	
	//$gelenSubeAd = isoMetinYap($gelenSubeAd);

    //$gelenSubeKod = isoMetinYap($gelenCariKod);	
	

    $sorguCumle = "SELECT COUNT(BLKODU) FROM FATURA_DISMODUL WHERE EVRAK_NO = '$gelenSiparisNo';";
    

    try {        
	
		//Önce Fatura ilişkilerine bakılıyor.
	
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
			
			if($row[0]>0) $donenDeger=true; else $donenDeger=false;                        
        } 
        
        
        $ret = null;	

    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;			
        die();
    }

    return $donenDeger;
	
}



function irsaliyedenFaturaKesilmisMi($gelenSiparisNo){
	
	global $dbh;
	
	//$gelenSubeAd = isoMetinYap($gelenSubeAd);

    //$gelenSubeKod = isoMetinYap($gelenCariKod);	
	

    $sorguCumle = "SELECT BLIRKODU FROM IRSALIYE_DISMODUL WHERE EVRAK_NO = '$gelenSiparisNo';";
    

    try {        
	
		//Önce Fatura ilişkilerine bakılıyor.
	
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row){ //İrsaliye döngüsü
		
			//Buradan sonra bu irsaliye numarası ilişkilenen fatura var mı diye bakılıyor?
			$sorguCumle2 = "SELECT COUNT(BLKODU) FROM FATURA_DISMODUL WHERE BLDMKODU = $row[0];";
			$ret2 = $dbh->query($sorguCumle2);
			
			foreach($ret2 as $row2){
				if($row2[0]>0) $donenDeger=true; else $donenDeger=false;				
			}
		
			
			
        } 
        
        
        $ret = null;
		$ret2 = null;		

    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;			
        die();
    }

    return $donenDeger;
	
}



function odemePlaniVarMi($gelenSiparisNo){
	
	global $db;		
	

    $sorgu = "SELECT COUNT(sn) FROM odeme_plani WHERE siparis_no = '$gelenSiparisNo';";
	
	$ret = pg_query($db, $sorgu) or die("Veritabanına bağlanamadı!\n" + pg_last_error($db));
    

    $donenDeger=false;   

    while($row = pg_fetch_row($ret)){
		
        if($row[0]>0){
			$donenDeger = true;
			
		}else{
			$donenDeger = false;
			
		}	   
    }    

    return $donenDeger;
	
}

function sevkiyatBilgiVarMi($gelenSiparisNo){
	
	global $db;		
	

    $sorgu = "SELECT COUNT(sn) FROM sevkiyat_bilgi WHERE siparis_no = '$gelenSiparisNo';";
	
	$ret = pg_query($db, $sorgu) or die("Veritabanına bağlanamadı!\n" + pg_last_error($db));
    

    $donenDeger=false;   

    while($row = pg_fetch_row($ret)){
		
        if($row[0]>0){
			$donenDeger = true;
			
		}else{
			$donenDeger = false;
			
		}	   
    }    

    return $donenDeger;
	
}




//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */





//Bağlantı Yapılıyor... 
//require_once("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");
require("AkinsoftIslemleri.php");
require("BelgesiVarMi.php");
require("SiparistenCariAdVer.php");
require("AkinsoftCariAdVer.php");




//Gelen Değerler alınıyor...
$gelenZiyaretNo = $_POST['ziyaretNo'];
$gelenIslemTip = $_POST['islemTip'];

$gelenListeTur = $_POST['listeTur'];
$gelenTarih1 = $_POST['tarih1'];
$gelenTarih2 = $_POST['tarih2'];

$gelenDetayGoster = $_POST['detayGoster'];

if($gelenListeTur=="") $gelenListeTur = "Liste";


require("saat1.inc");


//Sürekli kullanılacak olan Firebird Bağlantısı
/*
try{
	$dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
	
}catch (PDOException $e) {
						print "Akınsoft\'a Bağlanamadı!: " . $e->getMessage() . "<br/>";
						die();
					}	


*/




echo '<br><br><h1 style="text-align:center;">Çöpten Talep Kurtarma (DENEME AŞAMASINDA!)</h1>';


if($gelenTarih1=="") $gelenTarih1 = date("Y")."-01-01";
if($gelenTarih2=="") $gelenTarih2 = date("Y-m-d"); //"2021-12-31";


//******************************************* */
//Burada kayıt SİLİNİYOR.
/*
if($gelenZiyaretNo<>"" && $gelenIslemTip =="ZiyaretSil"){
    $sorguCumle ="DELETE FROM ziyaret WHERE sn = $gelenZiyaretNo;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        //exit;
    }
    
    $gelenZiyaretNo = "";

}
*/


//-----------------------------------------------

echo '<form action="#" method="post" onsubmit="return saatYap()" >';
echo '<table border ="0">';
echo '<tbody>';
echo '<tr>';
echo '<td >İade Tarihi:</td>';
echo '<td>';
echo '<input type="date" id="tarih1" name="tarih1" class= "tusBosluk2" value="'.$gelenTarih1.'" title="Bu tarih ve sonrası!"';
echo '</td>';
echo '<td>';
echo '<input type="date" id="tarih2" name="tarih2" class= "tusBosluk2" value="'.$gelenTarih2.'" title="Bu tarihe kadar!"';
echo '</td>';


echo '</tr>';
echo '<tr>';
echo '<td>';
echo '</td>';
echo '<td>';
echo '<button type="button" class= "tusBosluk2" onclick="geri(1)">Geri</button>';
echo '<button type="button" class= "tusBosluk2" onclick="ileri(1)">İleri</button>';
echo '</td>';
echo '<td >';
echo '<button type="button" class= "tusBosluk2" onclick="geri(2)">Geri</button>';
echo '<button type="button" class= "tusBosluk2" onclick="ileri(2)">İleri</button>';
echo '</td>';




echo '</tr>';
/*
echo '<tr>';
echo '<td >Liste Türü:</td>';
echo '<td colspan="2">';

echo '<select name="listeTur" id="listeTur">';
    
    echo '<option value="Liste" ' . selectedKoy($gelenListeTur, "Liste"). '>Liste</option>';	           
    echo '<option value="Detay Liste" ' . selectedKoy($gelenListeTur, "Detay Liste"). '>Detay Liste</option>';	           
    

echo "</select>";
echo '</td>';

echo '</tr>';

*/


echo '<tr>';
echo '<td colspan = "3" style="text-align:right">';
echo '<input type="submit" class= "tusBosluk2" value="Sorgula" style="width:200px;">';
echo '</td>';


echo '</form>';
echo '</tr>';
echo '</tbody>';
echo '</table>';


//-----------------------------------------------


echo "<br><br>";

//echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" onkeyup="searchTableColumns(\'tableMain\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">';

echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" onkeyup="searchTableColumns(\'tableMain\',\'myInputHepsi\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">';

echo "<br><br>";

echo "<hr><br>";

//Sorgu başlıyor.

if($gelenListeTur=="Liste"){
    
    $sorguCumle = "SELECT sn, 
						  fatura_no, 
						  iade_tarihi, 
						  kullanici, 
						  aciklama,
						  onay,						  
						  bl_kodu,
						  reddet
					FROM alis_iade
					WHERE iade_tarihi BETWEEN '$gelenTarih1' AND '$gelenTarih2'
					ORDER BY iade_tarihi DESC, sn DESC;";   


//echo $sorguCumle;
    
    $ret = pg_query($db, $sorguCumle);
    
    if(!$ret){
        echo pg_last_error($db);
        exit;
    }    
    
    
    echo '<table class= "sortable" valign="middle" id="tableMain">';
    echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
    echo '<th style="text-align:center;cursor:pointer;">S/N</th>';
	echo '<th style="text-align:center;cursor:pointer;">Fatura no</th>';
    echo '<th style="text-align:center;cursor:pointer;">İade Tarihi</th>';
    echo '<th style="text-align:center;cursor:pointer;">Cari Kodu</th>';
    echo '<th style="text-align:center;cursor:pointer;">Cari Ad</th>';    
    echo '<th style="text-align:center;cursor:pointer;">Kullanıcı</th>';
    echo '<th style="text-align:center;cursor:pointer;">Açıklama</th>';
    echo '<th style="text-align:center;cursor:pointer;">Onay</th>';
	//echo '<th style="text-align:center;cursor:pointer;">Onaylayan</th>';
    echo '<th style="text-align:center;cursor:pointer;">BL Kodu</th>';
    echo '<th style="text-align:center;cursor:pointer;">Reddet</th>';
    echo "<th style=\"text-align:center\">İşlem</th>";
    echo "</tr>";    
    
    
    while($row = pg_fetch_row($ret)){
		
		$anlikCariKod = ""; //Alttaki fonksiyon ile bulunacak.
		$anlikCariAd = ""; //Alttaki fonksiyon ile bulunacak.
		siparistenCariAdVer($row[1]); 
		
    
        echo "<tr title='$satirBilgi'>";
        echo "<td style='text-align:center' onclick='modalGoster(0, $row[0])'> ". $row[0] . "</td>"; //SN
        echo "<td style='text-align:center' onclick='modalGoster(1, \"".$anlikCariKod."\")'>". $row[1] . "</td>"; //Sipariş No
        echo "<td sorttable_customkey='". tarihFormatla($row[2]) ."' style='text-align:center'>". tarihYaz($row[2]) . "</td>"; //Sipariş Tarihi
        echo "<td style='text-align:left' onclick='modalGoster(1, \"".$anlikCariKod."\")'  >$anlikCariKod</td>";   //Cari Ad
		echo "<td style='text-align:left' onclick='modalGoster(1, \"".$anlikCariKod."\")'  >$anlikCariAd</td>";   //Cari Ad
        
        
        echo "<td style='text-align:center'>". $row[3] . "</td>"; //Kullanıcı
        echo "<td style='text-align:left'>". $row[4] . "</td>"; //Açıklama    
        
        echo "<td style='text-align:center'>".trueFalse($row[5],"Onaylı"). "</td>"; //Onay	
        


        echo "<td style='text-align:center'>".$row[6]. "</td>"; //BlKodu
		echo "<td style='text-align:center'><font style='color:red;'>".trueFalse($row[7],"RED!"). "</font></td>"; //Reddet
        
        
        
        
    
        //İŞLEMLER kısmı
        //Detay, silme ve Hikaye Tuşları
        echo "<td style='vertical-align: middle; text-align:center'>";
        echo "<div style='margin: 0 auto; width: auto'>";
       	
		/*
		
		if($duzeltYetki=='t'  ){
			echo '<form action="AlistanIade.php" target="_blank" method="post" class="inline">'; 
            echo '<input type="hidden" name="faturaNo" value="'.$row[1].'">';
			echo '<input type="hidden" name="cariAd" value="'.$anlikCariAd.'">';
			echo '<input type="hidden" name="cariKod" value="'.$anlikCariKod.'">';
            echo '<button type="submit"><img src="EditIcon.png" height="20" width="20" title="İade Düzelt==>'.$row[1].'"/></button>';
            //echo '<button type="submit">Detay</button>';
            echo '</form>';
			
		}
		
		
		
		//Sipariş silme!
		
		
		if($silYetki=='t' && $row[5]== 'f'){
			
            echo '<button type="button" onclick="alisIadeSil(this, \'tableMain\', '.$row[0].');" class="inline">
			      <img src="Delete_Icon.png" height="20" width="20" title="Alış İade sil==>'.$row[0].'"/></button>';
            
			
		}
		*/
    
         echo "</div>";
         echo "</td>";
         //Detay ve Hikaye Tuşları sonu
        
    
        echo "</tr>";
    
    }

}





//print_r($gidecekKonumlar);


//echo "</tbody>";
echo "</table>";









//En alta eklenmesi gereken kod

//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';



//VT sıfırlanıyor...
$dbh = null;
$ret = null;


echo '<br><br><br>';

require("saat2.inc");



pg_close($db);

//require_once('footer.php');


?>


</body>
</html>	
