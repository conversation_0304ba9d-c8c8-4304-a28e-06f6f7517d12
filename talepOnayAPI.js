// Talep Onay API Endpoint
// HTML arayüzü ile JavaScript modülü arasında köprü görevi görür
// MySQL veritabanı kullanır

const TalepOnayKarti = require('./talepOnayKarti');
const express = require('express');
const cors = require('cors');

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(__dirname));

// Talep Onay Kartı instance'ı
let onayKarti;

// Sunucu başlatıldığında veritabanı bağlantısını kur
async function startServer() {
    try {
        onayKarti = new TalepOnayKarti();
        await new Promise(resolve => setTimeout(resolve, 1000)); // Bağlantı için bekle
        
        console.log('🚀 Talep Onay API sunucusu başlatıldı');
        console.log(`📡 API: http://localhost:${port}/api`);
        console.log(`🌐 HTML: http://localhost:${port}/talepOnayKarti.html`);
        
    } catch (error) {
        console.error('❌ Sunucu başlatma hatası:', error);
    }
}

/**
 * Ana sayfa - HTML dosyasını serve et
 */
app.get('/', (req, res) => {
    res.sendFile(__dirname + '/talepOnayKarti.html');
});

/**
 * Onay bekleyen talepleri getir
 * GET /api/onay-bekleyen-talepler
 */
app.get('/api/onay-bekleyen-talepler', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 20;
        const talepler = await onayKarti.getOnayBekleyenTalepler(limit);
        
        // İstatistikleri hesapla
        const toplamTalep = talepler.length;
        const toplamTutar = talepler.reduce((total, talep) => total + talep.toplam_tutar, 0);
        const bugun = new Date().toDateString();
        const bugunkuTalep = talepler.filter(talep => {
            const talepTarih = new Date(talep.yaratma_zaman).toDateString();
            return talepTarih === bugun;
        }).length;

        res.json({
            success: true,
            data: {
                talepler: talepler,
                istatistikler: {
                    toplamTalep,
                    toplamTutar,
                    bugunkuTalep
                }
            },
            message: `${talepler.length} talep başarıyla getirildi`
        });

    } catch (error) {
        console.error('❌ Talepler getirme hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Talepler getirme hatası: ' + error.message
        });
    }
});

/**
 * Talep detayını getir
 * GET /api/talep-detay/:talepSn
 */
app.get('/api/talep-detay/:talepSn', async (req, res) => {
    try {
        const talepSn = parseInt(req.params.talepSn);
        
        if (!talepSn) {
            return res.status(400).json({
                success: false,
                message: 'Geçersiz talep numarası'
            });
        }

        const talepDetay = await onayKarti.getTalepDetay(talepSn);
        
        res.json({
            success: true,
            data: talepDetay,
            message: `Talep ${talepSn} detayı başarıyla getirildi`
        });

    } catch (error) {
        console.error('❌ Talep detay getirme hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Talep detay getirme hatası: ' + error.message
        });
    }
});

/**
 * Talebi onayla
 * POST /api/talep-onayla/:talepSn
 */
app.post('/api/talep-onayla/:talepSn', async (req, res) => {
    try {
        const talepSn = parseInt(req.params.talepSn);
        const { onayKullanici, selectedDetails } = req.body;
        
        if (!talepSn) {
            return res.status(400).json({
                success: false,
                message: 'Geçersiz talep numarası'
            });
        }

        const kullanici = onayKullanici || 'sistem';
        const sonuc = await onayKarti.talepOnayla(talepSn, kullanici, selectedDetails);
        
        if (sonuc) {
            res.json({
                success: true,
                data: { talepSn, onayKullanici: kullanici },
                message: `Talep ${talepSn} başarıyla onaylandı`
            });
        } else {
            res.status(400).json({
                success: false,
                message: 'Talep onaylanamadı'
            });
        }

    } catch (error) {
        console.error('❌ Talep onaylama hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Talep onaylama hatası: ' + error.message
        });
    }
});

/**
 * Talebi sil
 * DELETE /api/talep-sil/:talepSn
 */
app.delete('/api/talep-sil/:talepSn', async (req, res) => {
    try {
        const talepSn = parseInt(req.params.talepSn);
        const { silmeKullanici } = req.body;
        
        if (!talepSn) {
            return res.status(400).json({
                success: false,
                message: 'Geçersiz talep numarası'
            });
        }

        const kullanici = silmeKullanici || 'sistem';
        const sonuc = await onayKarti.talepSil(talepSn, kullanici);
        
        if (sonuc) {
            res.json({
                success: true,
                data: { talepSn, silmeKullanici: kullanici },
                message: `Talep ${talepSn} başarıyla silindi`
            });
        } else {
            res.status(400).json({
                success: false,
                message: 'Talep silinemedi'
            });
        }

    } catch (error) {
        console.error('❌ Talep silme hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Talep silme hatası: ' + error.message
        });
    }
});

/**
 * Sunucu durumu kontrolü
 * GET /api/health
 */
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        },
        message: 'API sunucusu çalışıyor'
    });
});

/**
 * 404 Handler
 */
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint bulunamadı'
    });
});

/**
 * Error Handler
 */
app.use((error, req, res, next) => {
    console.error('❌ Genel API hatası:', error);
    res.status(500).json({
        success: false,
        message: 'Sunucu hatası'
    });
});

// Sunucuyu başlat
app.listen(port, () => {
    console.log(`🌐 Talep Onay API sunucusu ${port} portunda çalışıyor`);
    startServer();
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Sunucu kapatılıyor...');
    
    if (onayKarti) {
        await onayKarti.close();
    }
    
    process.exit(0);
});

module.exports = app;