<?php
require('yetki.php');
$ekranKod = 259;
require('ekranYetkileri.php');

//require_once("PGConnect.php");
require("logYaz.php");


$gelenTalepHareketSn = $_POST['talepHareketSn'];
$gelenNakit = $_POST['nakit'];
$gelenBankadan = $_POST['bankadan'];
$gelenEvrakla = $_POST['evrakla'];
$gelenKk = $_POST['kk'];
$gelenAcik = $_POST['acik'];
$gelenKdv = $_POST['kdv'];
$gelenTevkifat = $_POST['tevkifat'];
$gelenKkKod = $_POST['kkKod'];
$gelenBankaKod = $_POST['bankaKod'];
$gelenSilinecek = $_POST['silinecek'];
$gelenAciklama = str_replace("'", "''", $_POST["aciklama"]);






if($gelenTalepHareketSn=="" ||
   $gelenNakit=="" || 
   $gelenBankadan=="" || 
   $gelenEvrakla=="" || 
   $gelenKk=="" ||
   $gelenAcik=="" ||
   $gelenKdv=="" ||   
   $gelenTevkifat=="" ||   
   $gelenAciklama=="" ){
    echo "Veri hatası!";
	pg_close($db);
    exit();
}

if($gelenBankadan>0 && $gelenBankaKod=="" ){
	echo "Veri hatası!";
	pg_close($db);
    exit();	
}

if($gelenKk>0 && $gelenKkKod=="" ){
	echo "Veri hatası!";
	pg_close($db);
    exit();	
}



//echo "$gelenAciklama";
//exit();

//Öncelikle aynı ödeme emri için onaylanmamış değişiklik talebi var mı bakılıyor?

$sorguCumle = "SELECT COUNT(sn) FROM talep_dogrudan_degisiklik
							   WHERE talep_hareket_sn = $gelenTalepHareketSn
							   AND onay = FALSE;";

//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);


while($row = pg_fetch_row($ret)){
	$adet = $row[0];	
}

if($adet>0){
	echo "Bu Doğrudan Harcama ile ilgili zaten bir değişiklik talebi bulunuyor!\nÖncelikle onun sonucu beklenmelidir!";
	pg_close($db);
	exit();
}

//Sıkıntı yok ise değişiklik talebi oluşturuluyor.


$sorguCumle = "INSERT INTO talep_dogrudan_degisiklik(talep_sn, 
													 yeni_nakit, 
													 yeni_bankadan, 
													 yeni_evrak, 
													 yeni_kk, 
													 yeni_acik, 
													 yeni_kk_kod, 
													 yeni_banka_kod, 
													 yeni_sube, 
													 kullanici, 
													 aciklama, 
													 silinsin,
													 talep_hareket_sn)
			   VALUES(0, 
			          $gelenNakit, 
					  $gelenBankadan, 
					  $gelenEvrakla, 
					  $gelenKk,
					  $gelenAcik,
					  '$gelenKkKod',
					  '$gelenBankaKod',	
					  '-',					  
					  '$kullanici', 
					  '$gelenAciklama',
					  $gelenSilinecek,
					  $gelenTalepHareketSn	
					  );";
			   
			   
//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo "Talep Harcama Değişiklik talebi oluşturulamadı!\n".pg_last_error($db);
	pg_close($db);
    exit();
}


ob_clean();

if(pg_affected_rows($ret)>0){
    logYaz("{$gelenTalepHareketSn} numaralı Talep Harcama Değişiklik talebi oluşturuldu!");
    echo "Okkk"; 

} else echo "Hata! Telep Harcama değişikliği güncellenemedi!";




pg_close($db);












?>