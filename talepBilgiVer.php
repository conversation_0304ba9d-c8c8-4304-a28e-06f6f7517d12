<?php
require('yetki.php');
$ekranKod = 84;
require('ekranYetkileri.php');

?>
<style>   
  table#tableMain2 thead { display:block; }
  table#tableMain2 tbody { height:300px; overflow-y:scroll; display:block; }
</style>
</style>

<?php



function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}

function talepHareketTutar($gelenTalepSn){
	
	global $db;
	
	$sorguCumle = "SELECT SUM(nakit_odenen + bankadan_odenen + evrakla_odenen + kk_odenen + acik_odenen ) 
				   FROM talep_hareket 
				   WHERE talep_sn = $gelenTalepSn ;"; //AND talep_detay_sn = $gelenTalepDetaySn

    $ret = pg_query($db, $sorguCumle);   

	if(!$ret){
		echo pg_last_error($db);
		exit;
	}
	
	$donenDeger = 0;
    

    while($row = pg_fetch_row($ret)){
        $donenDeger = $row[0];
    }  
	
	return $donenDeger; 	
	
}

//*********************************************************
//*********************************************************
//*********************************************************
//*********************************************************
//*********************************************************
//*********************************************************
//*********************************************************

/*
Sadece oluşturuldu ama hiçbir yerde kullanılmadı.
Gerek olmadı!
Kod eksik.
Kontrol et ama?!
2023 02 13
*/


$gelenTalepSn = $_POST['talepSn'];
$gelenNeIcinSn = $_POST['neIcinAlindiSn'];


if($gelenTalepSn!=""){
	
	$sorguCumle = "SELECT sn, 
						  konu, 
						  yaratma_zaman, 
						  talep_eden, 
						  kullanici, 
						  (nakit_odenen + bankadan_odenen + evrakla_odenen + kk_odenen + acik_odenen),
						  dogrudan_miktar,
						  dogrudan_birim,
						  tamamlandi
					FROM talep WHERE sn = $gelenTalepSn;";

}elseif($gelenNeIcinSn!=""){
	
		$sorguCumle = "SELECT sn, 
						  konu, 
						  yaratma_zaman, 
						  talep_eden, 
						  kullanici, 
						  (nakit_odenen + bankadan_odenen + evrakla_odenen + kk_odenen + acik_odenen),
						  dogrudan_miktar,
						  dogrudan_birim,
						  tamamlandi
					FROM talep WHERE dogrudan_ilgili_alim = $gelenNeIcinSn;";

	
}else{
	
	echo "Veri Hatası!";
	exit();
	
}


//echo "$sorguCumle";
//exit();

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo "Hata 122: " .  pg_last_error($db);
    exit();
}

$count = pg_num_rows($ret); //Sorgu sonucunun satır sayısını veriyor.


if($count<1){
    echo '<p style="color:red">Detay kaydı yok!</p>';
    pg_close($db);
    exit();
}

echo '<table class= "sortable" valign="middle" id="tableMain2" >';
//echo "<table >";
echo "<tr>";
echo '<th style="text-align:center;cursor:pointer;">S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Detay No</th>';
echo '<th style="text-align:center;cursor:pointer;">Açıklama</th>';
echo '<th style="text-align:center;cursor:pointer;">Miktar</th>';
echo '<th style="text-align:center;cursor:pointer;">Birim</th>';
echo '<th style="text-align:center;cursor:pointer;color:red;">Fiyat</th>';
echo '<th style="text-align:center;cursor:pointer;">Termin Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">Satınalmacı</th>';
echo '<th style="text-align:center;cursor:pointer;">Durum</th>';
//echo '<th style="text-align:center;cursor:pointer;">İptal</th>';
echo "</tr>";


$sayac = 0;

while($row = pg_fetch_row($ret)){    

    $sayac+=1;
	
    echo "<tr>";
    echo "<td style='text-align:center'>". $sayac . "</td>";   //S/N
    echo "<td style='text-align:left'>". $row[0] . "</td>";   //DetayNo
    echo "<td style='text-align:left'>". $row[1]. "</td>";   //Açıklama
    echo "<td style='text-align:right'>". number_format($row[6], 2, ',', '.') . "</td>";   //Miktar
    echo "<td style='text-align:right'>". $row[7]. "</td>";   //Birim
	echo "<td style='text-align:right;color:red;'>". number_format($row[5], 2, ',', '.') . "</td>";   //Fiyat
    echo "<td style='text-align:right'>". tarihYaz($row[2]). "</td>";   //Termin Zaman
    echo "<td style='text-align:left'>". $row[4]. "</td>";   //Satınalmacı
    echo "<td style='text-align:center'>". trueFalse($row[8], "Tamamlandı"). "</td>";   //Durum
    //echo "<td style='text-align:center'>". trueFalse($row[7], "İPTAL!"). "</td>";   //İptal
    echo "</tr>";

    
}
echo "</table>";




pg_close($db);


?>