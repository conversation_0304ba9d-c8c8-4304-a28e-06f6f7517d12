<?php
require('yetki.php');
$ekranKod = 76; //Sadece Toplu onaydan değiştirilebilsin diye kullanabiliriz.
//$kullanici = $_SESSION["user"];
require('ekranYetkileri.php');


//-----------------------------------------------
//-----------------------------------------------
//-----------------------------------------------
//-----------------------------------------------
//-----------------------------------------------
//-----------------------------------------------



//require("PGConnect.php");
require("mesajlasma.php");
require("KullanicidanBilgiVer.php");
require("logYaz.php");

//----------------------------------------------


$gelenTalepSn= $_POST['talepSn'];
$gelenTalepDetaySn= $_POST['talepDetaySn'];
$gelenYeniMiktar= $_POST['miktar'];
$gelenTalepEdenKisi= $_POST['talepEdenKisi'];

//Değişiklik yapılıyor. 

$donenDeger = false;

$sorguCumle = "UPDATE talep_detay SET ilk_miktar = miktar, miktar = $gelenYeniMiktar WHERE sn = $gelenTalepDetaySn;";

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    //exit;    
}

if(pg_affected_rows($ret)>0){ //KMS gönderilecek.
	
	$kimlere = [];
	$gelenMesaj = "Talep Sn: $gelenTalepSn, Detay Sn:$gelenTalepDetaySn Adet: $gelenYeniMiktar olarak değiştirildi!";
	array_push($kimlere, kullanicidanBilgiVer("cep_tel", $gelenTalepEdenKisi));
	//kisaMesajGonder($gelenMesaj, $kimlere, 0);
	
	logYaz($gelenMesaj);
	
	//Kayıt yazılıyor.
	$sorguCumle ="INSERT INTO kms_log(kaynak_tablo, kaynak_sn, mesaj, gonderen, alici, kaynak_detay_sn)
              VALUES('talep', $gelenTalepSn, '$gelenMesaj', '$kullanici', '$gelenTalepEdenKisi', $gelenTalepDetaySn);";


	//echo $sorguCumle;

	$ret = pg_query($db, $sorguCumle);

	if($ret){
		$donenDeger = true;
		
	}else{
		echo pg_last_error($db);		
	}	
	
	
}





if($donenDeger==true){
	ob_clean();
	echo "Okkk";
}

pg_close($db);


?>