-- MEG Talep Polling Test Script

-- Test 1: <PERSON><PERSON> talep oluştur
INSERT INTO talep (konu, detay, talep_eden, kullanici, sube) 
VALUES ('POLLING TEST: Senkronizasyon Testi', 'Bu kayıt polling senkronizasyon testi içindir', 'polling_test', 'polling_test', 'TEST_SUBE') 
RETURNING sn;

-- <PERSON><PERSON><PERSON><PERSON>i sonucu not alın (örnek: sn = 12346)

-- Test 2: 30 saniye bekleyip MySQL'de kontrol edin
-- SELECT * FROM talep WHERE konu LIKE 'POLLING TEST:%';

-- Test 3: Talep detayı ekle
-- INSERT INTO talep_detay (talep_sn, aciklama, miktar, birim, kullanici) 
-- VALUES (12346, 'Polling test malzeme', 5, 'Adet', 'polling_test');

-- Test 4: 30 saniye bekleyip MySQL'de kontrol edin  
-- SELECT * FROM talep_detay WHERE aciklama LIKE 'Polling test%';

-- Test 5: <PERSON><PERSON> güncelle
-- UPDATE talep SET konu = 'POLLING TEST: GÜNCELLEME TESİTİ' WHERE sn = 12346;

-- Test 6: 30 saniye bekleyip MySQL'de güncellemeyi kontrol edin
-- SELECT * FROM talep WHERE sn = 12346;

-- Test temizliği
-- DELETE FROM talep_detay WHERE talep_sn = 12346;
-- DELETE FROM talep WHERE konu LIKE 'POLLING TEST:%';