<?php
/*
Muhasebe ekranından doğrudan harcamalarda sıkıntı olduğunda bu rutin ile kaydı yapılır.
*/
require_once('yetki.php');
$ekranKod = 65;
require('ekranYetkileri.php');

require("logYaz.php");







function talepDetayHareketiVarMi($gelenDetaySn){
	
	global $db;
	
	
	$sorguCumle = "SELECT COUNT(sn) FROM talep_hareket WHERE talep_detay_sn = $gelenDetaySn;";

	//Sorgu çalıştır.
	$ret = pg_query($db, $sorguCumle);

	if(!$ret){
			echo "Hata 673 " . pg_last_error($db);
			pg_close($db);
			exit();		
	} 

	while($row = pg_fetch_row($ret)){
		if($row[0]>0){
			$donenDeger = true;
		}else{
			$donenDeger = false;
		}	
	}
	
	
	
	return $donenDeger;
	
}




//*********************************************
//*********************************************
//*********************************************
//*********************************************
//*********************************************
//*********************************************




$gelenTalepSn = $_POST['talepSn'];
$gelenTalepDetaySn = $_POST['talepDetaySn'];


if($gelenTalepSn==""){
	echo "Veri Hatası!";
	pg_close($db);
	exit();	
}




if($gelenTalepDetaySn==""){
	$dogrudanTalep = true;
}else{
	$dogrudanTalep = false;
}



//Taşıma işlemi başlıyor...

//Öncelikle talep altında kaç adet detay olduğuna bakılacak
//Detay sayısı birden fazla ise talep silinmeyecek!!!


$sorguCumle = "SELECT COUNT(sn) FROM talep_detay WHERE talep_sn = $gelenTalepSn;";

//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);

if(!$ret){
		echo "Hata 673 " . pg_last_error($db);
		pg_close($db);
		exit();		
} 

while($row = pg_fetch_row($ret)){
	if($row[0]>1){
		$birdenFazlaDetayVar = true;
	}else{
		$birdenFazlaDetayVar = false;
	}	
}



if($birdenFazlaDetayVar){
	goto talepTasimaPasGec;
}




//Önce talep taşınıyor...

$talepTasindi = false;
$talepDetayTasindi = false;
$talepHareketTasindi = false;

$sorguCumle = "INSERT INTO talep_silinmis(SELECT * FROM talep WHERE sn=$gelenTalepSn);";


//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);

if(!$ret){
		echo "Hata 693 " . pg_last_error($db);
		pg_close($db);
		exit();		
} 


if(pg_affected_rows($ret)<1){
	echo "Hata";
	pg_close($db);
	exit();
}else{
	$talepTasindi = true;
}




talepTasimaPasGec:


//***********************************************
//Talep Detay taşınıyor...	


$sorguCumle = "INSERT INTO talep_detay_silinmis(SELECT * FROM talep_detay WHERE talep_sn=$gelenTalepSn AND sn=$gelenTalepDetaySn);";


//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);

if(!$ret){
		echo "Hata 698 " . pg_last_error($db);
		pg_close($db);
		exit();		
} 


if(pg_affected_rows($ret)<1){
	echo "Hata";
	pg_close($db);
	exit();
}else{
	$talepDetayTasindi = true;
}





//Talep hareket kaydı var mı diye kontrol edilecek!



if(talepDetayHareketiVarMi($gelenTalepDetaySn) == false){
	
	goto talepHareketiYok;
	
}








//***********************************************
//Talep Detay Hareket taşınıyor...	


$sorguCumle = "INSERT INTO talep_hareket_silinmis(SELECT * FROM talep_hareket WHERE talep_sn=$gelenTalepSn AND talep_detay_sn=$gelenTalepDetaySn);";


//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);

if(!$ret){
	echo "Hata 690 " . pg_last_error($db);
	pg_close($db);
	exit();		
} 


if(pg_affected_rows($ret)<1){
	echo "Hata";
	pg_close($db);
	exit();
}else{
	$talepHareketTasindi = true;
}



talepHareketiYok:


//Artık silme işlemleri yapılıyor...
//Talep altında tek kayıt varsa başka hiçbir şeyi silmeye gerek yok!
//Zira talep tablosu silindiğinde ilgili detay ve hareket kayıtları da siliniyor!


$donenDeger = false;


if($birdenFazlaDetayVar==false && ($dogrudanTalep==true || $talepTasindi==true)){ //Demek ki detay sadece bir tane. Tüm talep silinir ve altındaki her şey de silinecek!
		
	$sorguCumle = "DELETE FROM talep WHERE sn = $gelenTalepSn;";
	
	
	//Sorgu çalıştır.
	$ret = pg_query($db, $sorguCumle);

	if(!$ret){
		echo "Hata 690 " . pg_last_error($db);
		pg_close($db);
		exit();		
	} 


	if(pg_affected_rows($ret)<1){
		echo "Hata..";
		pg_close($db);
		exit();
	}else{
		$donenDeger = true;
	}	
	
}


if($donenDeger){
	goto cikis;
}



//Talep altında birden fazla detay var ise detaylar ve hareketleri silinecek!
//Triger olduğu için beraberinde hareketler de siliniyor!


$sorguCumle = "DELETE FROM talep_detay WHERE sn = $gelenTalepDetaySn;";

//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);

if(!$ret){
	echo "Hata 697 " . pg_last_error($db);
	pg_close($db);
	exit();		
} 


if(pg_affected_rows($ret)<1){
	echo "Hata..";
	pg_close($db);
	exit();
}else{
	$donenDeger = true;
}	












cikis:	

logYaz("Talep Sn: " . $gelenTalepSn . " Talep Detay Sn: $gelenTalepDetaySn Numaralı Talep/Detay çöpe taşındı!");
ob_clean();
echo "Okkk";							

									
									
									
									


pg_close($db);
?>