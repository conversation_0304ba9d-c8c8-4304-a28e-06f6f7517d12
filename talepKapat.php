<?php
require('yetki.php');
$ekranKod = 59;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">
<title>MEG Grup Yönetim Si<PERSON>mi</title>

<script src="sorttable.js"></script>

<style style="text/css">
    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }


/** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}



</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">

function proceed (gelenSayfa) {
    var form = document.createElement('form');
    form.setAttribute('method', 'post');    
    form.setAttribute('action', gelenSayfa);
    form.style.display = 'hidden';
    document.body.appendChild(form)
    form.submit();
}

function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}


function searchTableColumns(gelenTablo) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById("myInputHepsi");
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length-1; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
      }      
}


function kolonArama(gelenKolon, kolonNo) {
  var input, filter, table, tr, td, i, txtValue;
  input = document.getElementById(gelenKolon);//"myInput"
  filter = input.value.toLocaleUpperCase("tr");
  table = document.getElementById("tableMain");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[kolonNo];
    if (td) {
      txtValue = td.textContent || td.innerText;
      if (txtValue.toLocaleUpperCase("tr").indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}



function veriKontrol(){

    var hataVar = false;

    var nakit = 0;
    var banka = 0;
    var evrak = 0;
    var kK = 0;
    var acik = 0;

    nakit = document.getElementById("nakit").value; 
    banka = document.getElementById("banka").value; 
    evrak = document.getElementById("evrak").value; 
    kK = document.getElementById("kk").value; 
    acik = document.getElementById("acik").value; 

    if((nakit+banka+evrak+kK+acik)==0){
        alert("En az bir harcama bilgisi girilmelidir!\nİşlem iptal edilecek.");
        return false;
    }
    	


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}




function modalGoster(gelenKod) {
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
    }
    };
    xhttp.open("POST", "talepDetayVer.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("talepSn="+gelenKod);

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    console.log(gelenKod);
}

function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}












function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>



<?php

echo '<br><h1 style="text-align:center;">Satınalma Kapatma</h1>';


function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}



function acikTalepDetayVarMi($talepSn){

    global $db;

    $sorgu="SELECT COALESCE(COUNT(*), 0) FROM talep_detay WHERE talep_sn = $talepSn
                                                    AND iptal=false
                                                    AND tamamlandi=false;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Talep detay bulunamadı";
      exit;
   }  
   
   $donenDeger=true;
   
   while($row = pg_fetch_row($ret))
   {
	  if($row[0]>0) $donenDeger=true; else $donenDeger=false;	   
   }  

   return $donenDeger;
}


function talepDetayAcikHareketVarMi($talepSn){

    global $db;

    $sorgu="SELECT COALESCE(COUNT(*), 0) FROM talep_hareket
                                         WHERE talep_sn = $talepSn
                                         AND kapali_mi=false;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Talep detay bulunamadı";
      exit;
   }  
   
   $donenDeger=true;
   
   while($row = pg_fetch_row($ret))
   {
	  if($row[0]>0) $donenDeger=true; else $donenDeger=false;	   
   }  

   return $donenDeger;
}

function iptalOlmayantalepDetayVarMi($talepSn){

    global $db;

    $sorgu="SELECT COUNT(*) FROM talep_detay WHERE talep_sn = $talepSn
                                               AND iptal=false;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Talep detay bulunamadı";
      exit;
   }  
   
   $donenDeger=true;
   
   while($row = pg_fetch_row($ret))
   {
	  if($row[0]>0) $donenDeger=true; else $donenDeger=false;	   
   }  

   return $donenDeger;
}




function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn=$gorevSn;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$row[0];	   
   }  

   return $liste;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }



function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}


function talepdenGorevSnVer($gelenTalepSn){

    global $db;  

    $sorgu="SELECT sn FROM gorevlendirme WHERE talep_sn=$gelenTalepSn;";

    $donenDeger=0;

    $ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Görev bulunamadı!";
      exit;
   }   
    
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }  

   return $donenDeger;

}

function hikayesiVarMi($gelenGorevSn){

    global $db;   
    

	$sorgu="SELECT COUNT(*) FROM gorevlendirme_hikaye WHERE gorev_sn=$gelenGorevSn;";	

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Görev hikayeleri sayılamadı!";
      exit;
   }   
    
   while($row = pg_fetch_row($ret))
   {
	   if($row[0]>0) $donenDeger=true;else $donenDeger=false;	   
   }  

   return $donenDeger;
}


function talepIleIlgililerListesi($talepSn){

    global $db; 

    $liste=[];

    $sorgu="SELECT DISTINCT(islem_kullanici) FROM talep_detay WHERE talep_sn=$talepSn
                                                                AND islem_kullanici<>'';";

    $ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Detaya ulaşılamadı!";
        exit;
    }   
    
   while($row = pg_fetch_row($ret)){
	   array_push($liste,$row[0]);
   }   
   
   return $liste;
}









//*************************************************************** */
//*************************************************************** */
//*************************************************************** */





//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");
require('blink.html');
require("BelgesiVarMi.php");
require("AkinsoftCariAdVer.php");
require("AkinsoftIslemleri.php");




$gelenTalepSn = $_POST['talepSn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];

$gelenBorclanacakCari = $_POST['borclanacakCari'];





require("saat1.inc");


$gelenTalepDetaySn = $_POST['talepDetaySn'];

if($gelenTalepDetaySn=="") $gelenTalepDetaySn = "0";



$gelenNakit = $_POST['nakit'];
$gelenBanka = $_POST['banka'];
$gelenEvrak = $_POST['evrak'];
$gelenKk = $_POST['kk'];
$gelenAcik = $_POST['acik'];
$gelenTamamla= $_POST['tamamla'];


if($gelenNakit=="") $gelenNakit=0;
if($gelenBanka=="") $gelenBanka=0;
if($gelenEvrak=="") $gelenEvrak=0;
if($gelenKk=="") $gelenKk=0;
if($gelenAcik=="") $gelenAcik=0;

//echo $gelenIslemTip;
//echo $gelenKriter;




if($gelenKriter=="")$gelenKriter = "4";



if($gelenIslemTip=='talepKapatEkran'){
    //Talep kapatma kısmı
    echo "<b>Talep No: ". $gelenTalepSn . "<br><br></b>";
    echo '<form action="#" method="post" enctype="multipart/form-data">';
    
    //echo '<p style="color:red">Nakit ödeme varsa ilgili carilere Akınsoft üzerinden doğrudan borç girişi yapılacaktır! İşlemin doğruluğuna emin olun!!</p>' ;
    
    echo "<b>Nakit Ödenen: </b><br>";
    echo '<input type="number" id="nakit" readonly name="nakit" step="0.01" value="'.$gelenNakit.'"><br><br><br>';
    

    echo "<b>Banka Ödenen: </b><br>";
    echo '<input type="number" id="banka" readonly name="banka" step="0.01" value="'.$gelenBanka.'"><br><br><br>';
    

    echo "<b>Evrakla Ödenen: </b><br>";
    echo '<input type="number" id="evrak" readonly name="evrak" step="0.01" value="'.$gelenEvrak.'"><br><br><br>';

    echo "<b>Kredi Kartı ile Ödenen: </b><br>";
    echo '<input type="number" id="kk" readonly name="kk" step="0.01" value="'.$gelenKk.'"><br><br><br>';

    echo "<b>Açık Hesap Ödenen: </b><br>";
    echo '<input type="number" id="acik" readonly name="acik" step="0.01" value="'.$gelenAcik.'"><br><br><br>';

    /*

    echo "<b>Dosya Ekle: </b><br>";
    echo '<input type="file" name="fileToUpload[]" id="fileToUpload" multiple>';
    echo "<br><br>";
    */


    echo "<b>Talep KAPANSIN!: </b>";
    echo '<input type="checkbox" id="tamamla" name="tamamla" value="1">';
    
    echo "<br><br><br>";    
    echo '<input type="hidden" name="talepSn" value="'.$gelenTalepSn.'">';
    echo '<input type="hidden" name="islemTip" value="talepKapat">';            
    echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';   
    echo '<button type="submit" style="width:200px;" onclick="return veriKontrol();">Kaydet</button>';    
    echo '</form>';
    echo "<hr>";
}

if($gelenIslemTip=='talepKapat'){
	
	
	//Cari borçlandırma varsa yapılacak. 
	
	if($gelenBorclanacakCari!=""){ //Demek ki borçlanacak cari var.			
		
		$ch = curl_init();
		curl_setopt($ch,CURLOPT_URL,"https://portal.bormegplastik.com/AkinsoftCariBorclandir.php");
		curl_setopt($ch,CURLOPT_POST,TRUE);	
		curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,FALSE);
		curl_setopt($ch,CURLOPT_HTTPHEADER,array("Content-Type"=>"application/x-www-form-urlencoded"));
		curl_setopt($ch,CURLOPT_RETURNTRANSFER,TRUE);
		curl_setopt($ch,CURLOPT_POSTFIELDS,"talepSn=$gelenTalepSn");
		//curl_setopt( $ch, CURLOPT_COOKIE, $strCookie );    
			
		//İşlem isteği gönderiliyor
		$donenDeger = curl_exec($ch);	
		curl_close($ch);
		
		if($donenDeger=="Okkk"){
			echo "Cari borçlandırma yapıldı!";			
		}else{
			echo "Cari borçlandırma HATA!";			
			
		}
		
		
		
		
	}
	
	
	
	
	
	
	

    //Burada kayıt yapılıyor.

    if($gelenTamamla=="1")
        $sorguCumle = "UPDATE talep SET tamamlandi = TRUE,
                                        tamamlanma_zaman = NOW()
                                        WHERE sn=$gelenTalepSn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    //İlişkili görevi de kapat.
    $sorguCumle = "UPDATE gorevlendirme SET tamamlandi = TRUE, bitis_zaman=NOW() 
                           WHERE talep_sn=$gelenTalepSn;"; 

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 


    //Talep kapatma başarılı ise mesaj verilecek.
    logYaz($kullanici . " Kullanıcısı, ".$gelenTalepSn." numaralı talebi kapattı.");

    //Bu kısım KMS göndermek için.
    $sallaTel = []; 
    array_push($sallaTel, kullanicidanBilgiVer('cep_tel', "abilgin") );
    array_push($sallaTel, kullanicidanBilgiVer('cep_tel', "eguler") );
    
    //kisaMesajGonder($kullanici . " Kullanıcısı, ".$gelenTalepSn." numaralı talebi kapattı.", $sallaTel, 1);
    unset($sallaTel);    



}



if( $gelenIslemTip!='talepKapatEkran'){ //Talep kapatma ekranı olduğunda arama ve kriter seçimine gerek yok!

    echo '<form action="#" method="post">';
    echo '<br>Kriter: ';
    echo '<select name="kriter"  onchange="this.form.submit()">';
    echo '<option value="1" ' .selectedKoy($gelenKriter, "1"). '>Tüm Satınalmalar</option>';
    echo '<option value="2" ' .selectedKoy($gelenKriter, "2"). '>Bana Ait Devam Eden Satınalmalar</option>';
    echo '<option value="3" ' .selectedKoy($gelenKriter, "3"). '>Bitmiş Satınalmalar</option>';
    echo '<option value="4" ' .selectedKoy($gelenKriter, "4"). '>Bana Ait Kapaması Gelmiş Satınalmalar</option>';
    echo "</select>";
    echo "<br><br>";
    
    
    echo '<input type="submit" value="Sorgula">';
    echo '</form>';
}

echo "<br><br>";


echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" onkeyup="searchTableColumns(\'tableMain\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">';

echo "<br><br>";





//Burada kullanıcılara bilgilendirme yapılacak.
$gidenKimden="<EMAIL>";
$gidenAdresler = [];
$gidenTelefonlar = [];
$mesaj = $gelenGorevSn. " Numaralı Görev için TAMAMLANDI TALEP veya TAMAMLANDI değişikliği yapıldı.\nLütfen Portaldan kontrol eder misiniz?";
$kisiler =[];



//echo $gelenIslemTip;






/*

if($gelenTalepDetaySn <> "" && $gelenIslemTip<>""){

    
    switch ($gelenIslemTip) {
        case "islemOnay":
            $sorguCumle = "UPDATE talep_detay SET isleme_alindi = TRUE, islem_zaman=NOW() 
                           WHERE sn=$gelenTalepDetaySn;"; 

            logYaz($gelenTalepDetaySn . " Numaralı satınalma detay işleme alındı.");

            //$gidenBilgi = $kullanici . " Görev kapatma talebi oluşturdu!";

            //Mesaj gidecek kişiler ve görevi oluşturan
            //$kisiler = gorevlendirilenleriArrayVer($gelenGorevSn);
            //Görevi veren de ekleniyor.
            //array_push($kisiler, gorevVereniVer($gelenGorevSn));

            
            break;
        case "tamamlandiOnay":
            $sorguCumle = "UPDATE talep_detay SET tamamlandi = TRUE, tamamlandi_zaman=NOW() 
                            WHERE sn=$gelenTalepDetaySn;"; 

            logYaz($gelenTalepDetaySn . " Numaralı satınalma detay işleme alındı.");

            //$gidenBilgi = $kullanici . " Görev kapatma talebi oluşturdu!";

            //Mesaj gidecek kişiler ve görevi oluşturan
            //$kisiler = gorevlendirilenleriArrayVer($gelenGorevSn);
            //Görevi veren de ekleniyor.
            //array_push($kisiler, gorevVereniVer($gelenGorevSn));            
            break;
        case "islemIptal":
            $sorguCumle = "UPDATE talep_detay SET isleme_alindi = FALSE, islem_zaman=NULL 
                           WHERE sn=$gelenTalepDetaySn;";            
            break; 
        case "tamamlandiIptal":
            $sorguCumle = "UPDATE talep_detay SET tamamlandi = FALSE, tamamlandi_zaman=NULL 
                            WHERE sn=$gelenTalepDetaySn;";            
            break;
        } 


    //Sorgu çalıştır.
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

}

    

    foreach ($kisiler as $kisi){
        array_push($gidenAdresler, kullanicidanBilgiVer('eposta', $kisi));

        $mesajX = $mesaj ."\n". paslamaKoduVer($kisi, 'GorevTamamlama.php', '', '');


        //Bu kısım KMS göndermek için.
        $sallaTel = []; 
        array_push($sallaTel, kullanicidanBilgiVer('cep_tel', $kisi) );
        //kisaMesajGonder($mesajX, $sallaTel, 1);
        unset($sallaTel);
        
    }

    

    if(count($gidenAdresler)>0) epostaGonder($gidenKimden, $gidenAdresler, $gidenBilgi, $mesaj);
    //if(count($gidenTelefonlar)>0) kisaMesajGonder($mesaj, $gidenTelefonlar, 1);






                    
}


*/

//  if($gelenGorevSn==""||$gelenIslemTip=="") exit;

//Gelen Değerler alınıyor...
/*
$gelenKullanici = $_POST['kullanici'];

$gelenTip = $_POST['tip'];
$gelenKriterTum = $_POST['kriterTum'];

$takas = explode(" - ", $gelenKullanici);

$gelenKullaniciKod = trim($takas[1]);

*/

//$aktifKullanici = $_SESSION["user"];


/*
CREATE TABLE public.gorevlendirme (
    sn integer NOT NULL,
    gorev_veren text NOT NULL,
    gorev_konu text NOT NULL,
    baslama_zaman timestamp without time zone DEFAULT now() NOT NULL,
    hedef_zaman timestamp without time zone DEFAULT now() NOT NULL,
    tamamlandi boolean DEFAULT false NOT NULL,
    bitis_zaman timestamp without time zone,
    uzatma_zaman timestamp without time zone,
    gorev_detay text DEFAULT ''::text,
    yaratma_zaman timestamp without time zone DEFAULT now() NOT NULL,
    duzeltme_zaman timestamp without time zone DEFAULT now() NOT NULL
);


if($_SESSION["root"] == 1){
    $sorguCumle = "SELECT gorev_veren, gorev_konu, baslama_zaman, uzatma_zaman, bitis_zaman, tamamlandi, gorevlendirme.sn, gizli, gorev_detay, hedef_zaman FROM gorevlendirme                     
                    ORDER BY hedef_zaman;";
}else{
    $sorguCumle = "SELECT gorev_veren, gorev_konu, baslama_zaman, uzatma_zaman, bitis_zaman, tamamlandi, sn, gizli, gorev_detay, hedef_zaman 
                    FROM gorevlendirme                    
                    ORDER BY baslama_zaman DESC;";

}
 */

$alanlar = "SELECT  sn,                     
                    konu, 
                    detay, 
                    tamamlandi, 
                    talep_eden, 
                    kullanici, 
                    nakit_odenen,
                    bankadan_odenen,
                    evrakla_odenen, 
                    tamamlanma_zaman,
                    kk_odenen,
                    acik_odenen,
                    dogrudan,
                    sube,
					borclanacak_cari_kod "; //14


if( $gelenIslemTip=='talepKapatEkran'){ //Talep kapatma ekranı ise sadece seçilen tapebin detayları görünecek..

    $sorguCumle = $alanlar .  "FROM talep WHERE sn=$gelenTalepSn ORDER BY yaratma_zaman, sn DESC;";


}else{//Talep kapatma ekranı değilse sorgulamaya göre liste gelecek!

    

switch ($gelenKriter) {
    case "1": //Tamamı
        $sorguCumle = $alanlar .  "FROM talep ORDER BY yaratma_zaman, sn DESC;";
        //echo $sorguCumle;
      break;
    case "2": //Onay Bekleyenler

        $sorguCumle = $alanlar .  "FROM talep
                                    WHERE tamamlandi = false
                                    ORDER BY yaratma_zaman, sn DESC;";   
                                    
      $banaAitIslemdekilerGorunsun = 1;
      break;
    case "3": //Tamamlanmışlar
        $sorguCumle = $alanlar .  "FROM talep
                                    WHERE tamamlandi = true
                                    ORDER BY yaratma_zaman, sn DESC;"; 
      break;   
    case "4": //Onay Bekleyenler

    $sorguCumle = $alanlar .  "FROM talep
                                WHERE tamamlandi = false
                                ORDER BY yaratma_zaman, sn DESC;";   
    $kisiyeAitlerGorunsun = 1;
    break;
    
  } 

}
//echo $sorguCumle;

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
} 


//echo "<br><br>";    


echo '<table class= "sortable" valign="middle" id="tableMain">';
echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
echo '<th style="text-align:center;cursor:pointer;">Talep S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Konu</th>';
echo '<th style="text-align:center;cursor:pointer;">Detay</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Eden</th>';
echo '<th style="text-align:center;cursor:pointer;">Kullanici</th>';
echo '<th style="text-align:center;cursor:pointer;">Şube</th>';
echo '<th style="text-align:center;cursor:pointer;">Nakit Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Bankadan Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Evrakla Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">K.K. Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Açık Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Tamamlandı</th>';
echo '<th style="text-align:center;cursor:pointer;">Tamamlanma Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">Borçlanacak<br>Cari</th>';
echo "<th style=\"text-align:center\">İşlem</th>";
echo "</tr>";

//$sayac = 1;

while($row = pg_fetch_row($ret)){

    if(iptalOlmayantalepDetayVarMi($row[0])==false) continue;



    if($_SESSION["root"] != 1){ //Root değilse kontroller görünüyor

        if($banaAitIslemdekilerGorunsun==1){
            if(in_array($kullanici, talepIleIlgililerListesi($row[0])) == false ||
            acikTalepDetayVarMi($row[0])==false ) continue;
        } 
    
    
    
        if($kisiyeAitlerGorunsun==1){    
            if(in_array($kullanici, talepIleIlgililerListesi($row[0])) == false ||
            acikTalepDetayVarMi($row[0])==true ) continue;
        }

    }


       
    
    
//acikTalepDetayVarMi($row[0])==false &&


    //Gizli görev ve kullanıcı listede yoksa ve root değilse pas geçiliyor.

    //$goreviGorebilir = goreviGorebilir($row[6], $kullanici, $row[0]);

    //Sadece görevi alanlar
    //$sadeceGorevli = sadeceGorevli($row[6], $kullanici);
    /*
    if($_SESSION["root"] == 0){
        
        if( $row[7]=='t' && $goreviGorebilir == false ) continue;
    }
    */









    echo "<tr class='item' >";
    echo "<td onclick='modalGoster($row[0]);' style='text-align:center'>". $row[0] . "</td>"; //S/N       
    echo "<td onclick='modalGoster($row[0]);'>". $row[1] . "</td>"; //Konu
    echo "<td onclick='modalGoster($row[0]);'>". $row[2] . "</td>"; //Detay
    
    echo "<td style='text-align:center'>". $row[4] . "</td>"; //Talep Eden
    echo "<td style='text-align:center'>". $row[5] . "</td>"; //Kullanıcı
    echo "<td style='text-align:center'>". $row[13] . "</td>"; //Şube
    echo "<td style='text-align:right'>". number_format($row[6], 2, ',', '.') . "</td>";   //Nakit ödenen
    echo "<td style='text-align:right'>". number_format($row[7], 2, ',', '.') . "</td>";   //Bankadan ödenen
    echo "<td style='text-align:right'>". number_format($row[8], 2, ',', '.') . "</td>";   //Evrakla ödenen
    echo "<td style='text-align:right'>". number_format($row[10], 2, ',', '.') . "</td>";   //K.K.  ödenen
    echo "<td style='text-align:right'>". number_format($row[11], 2, ',', '.') . "</td>";   //Açık  ödenen
    echo "<td style='text-align:center'>". trueFalse($row[3],'Tamamlandı') . "</td>"; //Tamamlandı

    echo "<td sorttable_customkey='". tarihFormatla($row[9]) ."' style='text-align:center'>". tarihSaatYaz($row[9]) . "</td>"; //Tamamlanma Zaman
	
	if($row[14]!=""){
		echo "<td style='text-align:center'>". akinsoftCariAdVer($row[14], 0) . "</td>"; //Borçlanacak Cari		
	}else{
		echo "<td style='text-align:center'>-</td>"; //Borçlanacak Cari
		
	}




    //Detay, silme ve Hikaye Tuşları
    echo "<td style='vertical-align: middle; text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";

    //echo "1 ";

    $hikayeKaynakNo= talepdenGorevSnVer($row[0]);


    if(hikayesiVarMi($hikayeKaynakNo)){

        //Detay Göster
        echo '<form action="GorevHikayesiListesi.php"  method="post"  class="inline">'; 
        echo '<input type="hidden" name="gorevSn" value="'.$hikayeKaynakNo.'">';
        echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';		
        echo '<input type="hidden" name="gonderenSayfa" value="talepKapat.php">';
        echo '<button type="submit"><img src="DetayaGit.png" height="20" width="20" title="Hikayeye Git==>'.$row[0].'"/></button>';
        //echo '<button type="submit">Detay</button>';
        echo '</form>';

    }
    //echo "2 ";



    if(belgesiVarMi("talepBelgeler", $row[0], $gelenTalepDetaySn )==true){
        
        //Belgeleri Göster
        echo '<form action="TalepBelgeleri.php" target="_blank" method="post"  class="inline">'; 
        echo '<input type="hidden" name="talepSn" value="'.$row[0].'">';    
        echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$row[0].'"/></button>';
        //echo '<button type="submit">Detay</button>';
        echo '</form>';
    }


    //echo "3 ";








    
    if($row[3]=='f' && $duzeltYetki ==  't'){ //Tamamlanmamış ve kullanıcının da DÜZELTME yekisi verilmiş ise?

        if($row[3]=='f'){            

            if(acikTalepDetayVarMi($row[0])==false){ 

                if( $gelenIslemTip!='talepKapatEkran'&& $row[12]=='f' && talepDetayAcikHareketVarMi($row[0])==false){ //Talep Kapat ekranı yani fiyat giriş ekranı değilse detaya para girişi seçimi yapılabiliyor. 

                    //Tamamlanma islem Onayla?
                    echo '<form action="#"  method="post"  class="inline">'; 
                    echo '<input type="hidden" name="talepSn" value="'.$row[0].'">';
                    echo '<input type="hidden" name="islemTip" value="talepKapatEkran">';            
                    echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">'; 
					echo '<input type="hidden" name="borclanacakCari" value="'.$row[14].'">';
                    echo '<input type="hidden" name="nakit" value="'.$row[6].'">';   
                    echo '<input type="hidden" name="banka" value="'.$row[7].'">';   
                    echo '<input type="hidden" name="evrak" value="'.$row[8].'">';   
                    echo '<input type="hidden" name="kk" value="'.$row[10].'">';   
                    echo '<input type="hidden" name="acik" value="'.$row[11].'">'; 

                    echo '<button type="submit"><img src="TL-icon.png" height="20" width="20" title="Talep Kapat==>'.$row[0].'"/></button>';   //confirm 
                    echo '</form>';
                }elseif(talepDetayAcikHareketVarMi($row[0])==true){
                    echo "Kapanmamış hareket var. 'Talep Detay Kapat' ekranını kullanınız!";
                }


            }else echo "<span class='blink'> Tamamlanmamış talepler var! </span>";

        }

        if( ($row[10]=='t' || $row[12]=='t') && $row[14]=='f' ){    
            //Tamamlanma islem Onay İptal?
            echo '<form action="#"  method="post"  class="inline">'; 
            echo '<input type="hidden" name="talepDetaySn" value="'.$row[0].'">'; 

                if($row[12]=='t') {
                    echo '<input type="hidden" name="islemTip" value="tamamlandiIptal">';
                }elseif($row[10]=='t'){
                    echo '<input type="hidden" name="islemTip" value="islemIptal">';
                }
            
            echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';        
            echo '<button type="submit"><img src="cancel.png" height="20" width="20" title="Onayla İptal==>'.$row[0].'"/></button>';    
            echo '</form>';
        }
    }

//***************** */
    




    echo "</div>";
    echo "</td>";
    //Detay ve Hikaye Tuşları sonu

    echo "</tr>";
    //$sayac+=1;
}

echo "</table>";





//En alta eklenmesi gereken kod

//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2>Satınalma Detayı</h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';





pg_close($db);
echo "<br><br>";
require("saat2.inc");
echo "<br><br>";

?>

</body>
</html>
