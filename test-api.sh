#!/bin/bash

# MEG Talep API Test Script'i

echo "=== MEG TALEP API TEST ==="

# API Server'ın çalışıp çalışmadığını kontrol et
echo "1. Health Check..."
curl -X GET http://localhost:3080/health
echo -e "\n"

# Talep onaylama testi
echo "2. Talep Onaylama Testi..."
echo "Talep Detay SN giriniz (örnek: 123): "
read TALEP_DETAY_SN

if [ ! -z "$TALEP_DETAY_SN" ]; then
    curl -X POST http://localhost:3080/talep/onay \
      -H "Content-Type: application/json" \
      -d "{\"talep_detay_sn\": $TALEP_DETAY_SN}"
    echo -e "\n"
else
    echo "Talep Detay SN girilmedi, test atlanıyor."
fi

echo "=== TEST TAMAMLANDI ==="