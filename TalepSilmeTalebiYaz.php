<?php
require('yetki.php');
$ekranKod = 58;
require('ekranYetkileri.php');

//require_once("PGConnect.php");
require("logYaz.php");


$gelenTalepSn = kullaniciGirisGuvenlikKontrol($_POST['talepSn']);
$gelenTalepDetaySn = kullaniciGirisGuvenlikKontrol($_POST['talepDetaySn']);
$gelenAciklama = kullaniciGirisGuvenlikKontrol($_POST['aciklama']);


if($gelenTalepSn=="" || $gelenAciklama==""){
    echo "Veri hatası!";
	pg_close($db);
    exit();
}




if($gelenTalepDetaySn==""){
	$gelenTalepDetaySn = 0;
}


//Bekleyen bir talep var mı diye bakılıyor.

$sorguCumle = "SELECT COUNT(sn) 
					FROM talep_silme_istegi 
					WHERE talep_sn=$gelenTalepSn
					AND talep_detay_sn=$gelenTalepDetaySn;";

$ret = pg_query($db, $sorguCumle);



while($row = pg_fetch_row($ret)){
	$donenDeger = $row[0];
}

if($donenDeger>0){
	//Demek ki önceden bir kayıt var.
	echo "$gelenTalepSn / $gelenTalepDetaySn  numaralı talep ve detayı için bekleyen bir silme isteği bulunuyor!\nYeniden oluşturulamaz!";
	pg_close($db);
    exit();	
}


//Silme isteği yapılacak.



$sorguCumle = "INSERT INTO talep_silme_istegi(talep_sn, aciklama, kullanici, talep_detay_sn)
				VALUES($gelenTalepSn, '$gelenAciklama', '$kullanici', $gelenTalepDetaySn) RETURNING sn;";

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo "Yazma Anında hata oluştu:\n". pg_last_error($db);
    pg_close($db);
    exit();	
}

$donenSn = 0;

while($row = pg_fetch_row($ret)){
	$donenSn= $row[0];
}


if($donenSn>0){
	ob_clean();
    echo "Okkk#$gelenTalepSn / $gelenTalepDetaySn numaralı talep/detay için $donenSn numaralı silme isteği oluşturuldu.";
	
}else{
	echo "$gelenTalepSn / $gelenTalepDetaySn için silme isteği oluşturulamadı!";
}


pg_close($db);
?>