<?php
require('yetki.php');
$ekranKod = 261;
require('ekranYetkileri.php');




function yeniTalepDetayOlustur($gelenTalepSn, $talepDetaySn, $talepDetayMiktar, $talepDetayCariKod){
	
	global $db;
	
	$sorguCumle = "INSERT INTO talep(konu, detay, talep_eden, kullanici, sube, gizli, hizmet_kod, stok_kod, cari_kod) 
					SELECT konu, 'Kopyalandı-$gelenTalepSn', talep_eden, kullanici, sube, gizli, hizmet_kod, stok_kod, '$talepDetayCariKod' 
					 FROM talep WHERE SN = $gelenTalepSn 
					RETURNING sn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit();
    }

    //Dönen SN alınıyor. 

    while($row = pg_fetch_row($ret)){
        $kopyalananSn = $row[0]; 
    }
	
	//Detay yazılıyor.
	
	$sorguCumle = "INSERT INTO talep_detay(talep_sn, aciklama, miktar, birim, termin_zaman, onay, onay_zaman, isleme_alindi, islem_zaman, kullanici, islem_kullanici, ilgili_alim, onay_kullanici, hizmet_kod, stok_kod, teslim_kabul)
                       SELECT $kopyalananSn, aciklama, $talepDetayMiktar, birim, termin_zaman, onay, onay_zaman, isleme_alindi, islem_zaman, kullanici, islem_kullanici, ilgili_alim, onay_kullanici, hizmet_kod, stok_kod, teslim_kabul 
					   FROM talep_detay WHERE sn = $talepDetaySn;";

	$ret = pg_query($db, $sorguCumle);

	if(!$ret){
		echo pg_last_error($db);
		exit;
	}
	
	if(pg_affected_rows($ret)>0){
		$donenDeger = true;
	}else{
		$donenDeger = false;
	}
	
	
	return $donenDeger;
	
	
	
}

function detayEskiMiktar($gelenDetaySn){
	
	global $db;
	
	$sorguCumle = "SELECT miktar FROM talep_detay WHERE sn=$gelenDetaySn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit();
    }    

    while($row = pg_fetch_row($ret)){
        $donenDeger = $row[0]; 
    }
	
	return $donenDeger;	
	
}


function eskiDetayinMiktariniAzalt($detaySn, $yeniMiktar){
	
	global $db;
	
	$sorguCumle = "UPDATE talep_detay SET miktar = $yeniMiktar WHERE sn=$detaySn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit();
    }
	
	if(pg_affected_rows($ret)>0){
		$donenDeger = true;
	}else{
		$donenDeger = false;
	}
	
	
	return $donenDeger;
	
}

function eskiDetayiSil($gelenTalepSn, $detaySn){
	
	global $db;
	
	$sorguCumle = "DELETE FROM talep_detay WHERE sn=$detaySn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit();
    }
	
	if(pg_affected_rows($ret)>0){
		
		if(talepDetayYoksaTalepSil($gelenTalepSn)==true){
			$donenDeger=true;			
		}else{
			$donenDeger=false;			
		}
		
	}else{
		$donenDeger=false;
	}	
	
	return $donenDeger;
	
	
}

function talepDetayYoksaTalepSil($gelenTalepSn){
	
	global $db;
	
	$sorguCumle = "SELECT COUNT(sn) FROM talep_detay WHERE talep_sn=$gelenTalepSn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit();
    }
	
	$donenDeger=true;
	
	while($row = pg_fetch_row($ret)){
		
		if($row[0]<1){ //Detay yok. Tüm talep silinecek!
			
			$sorguCumle2="DELETE FROM talep WHERE sn=$gelenTalepSn;";
			
			$ret2 = pg_query($db, $sorguCumle2);
			
			if(pg_affected_rows($ret2)>0){
				$donenDeger = true;
			}else{
				$donenDeger = false;
			}			
			
		}
         
    }
	
	
	
	return $donenDeger;
	
	
}


//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */


//require("PGConnect.php");


//require("AkinsoftIslemleri.php");
require("logYaz.php");
//require("KullanicidanBilgiVer.php");
require("mesajlasma.php");
//require("SiparistenCariAdVer.php");
//require("AkinsoftCariAdVer.php");
//require("SiparisTutarVerSepet.php");
//require("SiparisTutarVer.php");



    
$gelenListe   = $_POST['liste'];
$gelenTalepSn = $_POST['talepSn'];

if($gelenListe=="" || $gelenTalepSn==""){
	echo "Veri hatası!";
	pg_close($db);
	exit();
}

/*
Öncelikle kümülatif toplamlar ortaya çıkıyor.
Buna göre varolan talep detay siliecek mi ona bakılacak.
*/

$miktarlar = [];

foreach($gelenListe as $deger){
	
	$bilgiler = explode("|", $deger); //8944 | 1 | Ahmet amca # MRKCR00296
	
	if(array_key_exists($bilgiler[0], $miktarlar)){
		
		$miktarlar[$bilgiler[0]] = $miktarlar[$bilgiler[0]] + $bilgiler[1]; 
		
	}else{
		$miktarlar[$bilgiler[0]] = $bilgiler[1]; 
		
	}	
	
}

/*
Burada yeni talepler oluşturuluyor.  
Oluşturulacak talep bilgileri alınıyor. 
*/

$donenDeger = true;


foreach($gelenListe as $deger){
	
	$bilgiler = explode("|", $deger); //8944 | 1 | Ahmet amca # MRKCR00296
	$talepDetaySn = $bilgiler[0]; 
	$talepDetayMiktar = $bilgiler[1]; 
	$talepDetayCariKod = explode(" # ", $bilgiler[2])[1]; 
	
	echo "$talepDetaySn-$talepDetayMiktar-$talepDetayCariKod\n";
	
	if(yeniTalepDetayOlustur($gelenTalepSn, $talepDetaySn, $talepDetayMiktar, $talepDetayCariKod)==true){
		logYaz("TalepSn: $gelenTalepSn, TalepDetaySn:$talepDetaySn detaylı bölme yapıldı.");
	}else{
		echo "TalepSn: $gelenTalepSn, TalepDetaySn:$talepDetaySn detaylı bölme HATA!";
		$donenDeger = false;
	}	
	
}



if($donenDeger==false){
	echo "İşlem hatası!";
	pg_close($db);
	exit();	
}

//İşlem devam ediyor.
//Ana talep detaydaki sayılar güncellenecek. 


foreach($miktarlar as $detaySn => $miktar){
	
	$eskiMiktar = detayEskiMiktar($detaySn);
	
	if($eskiMiktar>$miktar){		
		
		$degisecekMiktar = $eskiMiktar-$miktar;
		
		if(eskiDetayinMiktariniAzalt($detaySn, $degisecekMiktar)==true){
			logYaz("Detay Sn:$detaySn yeni miktar $degisecekMiktar olarak güncellendi.");
		}else{
			$donenDeger = false;
			$hataMesaj = $hataMesaj . "Detay Sn:$detaySn yeni miktar $degisecekMiktar HATA 1.\n";
			
		}
		
	}elseif($eskiMiktar==$miktar){//Demek ki eşit durumu
		
		//Şayet detay kalmıyorsa komple talebi de silecek!
	
		if(eskiDetayiSil($gelenTalepSn, $detaySn)==true){
			logYaz("TalepSn: $gelenTalepSn, Detay Sn:$detaySn siindi.");			
		}else{
			$donenDeger = false;
			$hataMesaj = $hataMesaj . "TalepSn:$gelenTalepSn silinemedi! HATA 2.\n";			
		}
		
	}	
	
}


ob_clean();

if($donenDeger==true) echo "Okkk";else echo "Kayıt işleminde hata!\n$hataMesaj";
	
	
pg_close($db);


?>

