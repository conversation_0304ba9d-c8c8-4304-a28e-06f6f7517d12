<?php
require('yetki.php');
$ekranKod = 105;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">
<title>MEG Grup Yönetim Si<PERSON>mi</title>

<script src="sorttable.js"></script>

<style style="text/css">
    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }
	
	.sortable th{ 
        position:sticky;
        /*position: -webkit-sticky;*/
        top: 0;
        background: green;/*#727272;*/
        color: white; /*#c5c5c5;*/
        z-index: 0;

		/*padding:7px; border: #4e95f4 1px solid;*/
		/*#4e95f4 orjinal hali*/ 
}





    .inline {
        display: inline;
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }
</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">

function proceed (gelenSayfa) {
    var form = document.createElement('form');
    form.setAttribute('method', 'post');    
    form.setAttribute('action', gelenSayfa);
    form.style.display = 'hidden';
    document.body.appendChild(form)
    form.submit();
}

function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sayac){
    
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edildi.");
		return false;
    }    

    var kisilerListesi = "";

    var i=1;
        for (i = 1; i <= sayac; i++) {               
                
                if(document.getElementById("kisiler-" + i).checked == true){
                    kisilerListesi+=document.getElementById("kisiler-" + i).value+';';
                }
        } 

    kisilerListesi = kisilerListesi.slice(0, -1);


    //console.log(kisilerListesi);

    if(kisilerListesi==""){
        alert("Görevlendirme için en az bir kişi seçilmelidir!\nİşlem iptal edildi.");
		return false;		
		
	}

    document.getElementById("kisiBilgileri").value = kisilerListesi;
	return true;	


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function searchTableColumns(gelenTablo) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById("myInputHepsi");
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length-1; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
      }      
}





function kolonArama(gelenKolon, kolonNo) {
  var input, filter, table, tr, td, i, txtValue;
  input = document.getElementById(gelenKolon);//"myInput"
  filter = input.value.toLocaleUpperCase("tr");
  table = document.getElementById("tableMain");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[kolonNo];
    if (td) {
      txtValue = td.textContent || td.innerText;
      if (txtValue.toLocaleUpperCase("tr").indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>



<?php

echo '<br><h1 style="text-align:center;">Talep Ödemeleri Listesi</h1>';


function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}

function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn='$gorevSn';";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$row[0];	   
   }  

   return $liste;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
    return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }



function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}



//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");
require("dosyalariYukle.php");
require("BelgesiVarMi.php");



$gelenTalepSn = $_POST['talepSn'];
$gelenTalepDetaySn = $_POST['talepDetaySn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];

if($gelenTalepDetaySn=="")$gelenTalepDetaySn=0;



$gelenDosyaYukle = $_POST['dosyaYukle'];


//Burada dosya yükleme işlemleri yapılıyor.
//Dosya geliyorsa kaydedecek.
dosyalariYukle("/mnt/talepBelgeler/".$gelenTalepSn."/".$gelenTalepDetaySn."/", "Talep "); 





if($gelenKriter=="")$gelenKriter = "1";




echo '<form action="#" method="post">';
echo '<br>Kriter: ';
echo '<select name="kriter"  onchange="this.form.submit()">';
echo '<option value="1" ' .selectedKoy($gelenKriter, "1"). '>Normal Talepler</option>';
echo '<option value="2" ' .selectedKoy($gelenKriter, "2"). '>Doğrudan Harcamalar</option>';
//echo '<option value="3" ' .selectedKoy($gelenKriter, "3"). '>Onaylanmış Doğrudan Harcamalar</option>';
echo "</select>";
echo "<br><br>";


echo '<input type="submit" value="Sorgula">';
echo '</form>';
echo "<br><br>";

echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" onkeyup="searchTableColumns(\'tableMain\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">';

echo "<br><br>";






switch ($gelenKriter) {
    case "1": //Normal talep
    $sorguCumle = "SELECT  talep.sn,                     
                            talep.konu,                             
                            talep.sube,
                            talep_detay.aciklama, 
                            talep_detay.miktar, 
                            talep_detay.birim,
                            talep_hareket.nakit_odenen, 
                            talep_hareket.bankadan_odenen, 
                            talep_hareket.evrakla_odenen,
                            talep_hareket.kk_odenen,  
                            talep_hareket.acik_odenen,                     
                            talep_hareket.talep_fatura_bilgisi,
                            talep_detay.sn FROM talep 
                                        INNER JOIN talep_detay ON talep.sn = talep_detay.talep_sn
                                        INNER JOIN talep_hareket ON talep_detay.sn = talep_hareket.talep_detay_sn ORDER BY talep.yaratma_zaman DESC, talep.sn DESC;";
      break;
    case "2": //Doğrudan satınalmalar

        $sorguCumle = "SELECT sn, konu, sube, nakit_odenen, bankadan_odenen, evrakla_odenen, kk_odenen, acik_odenen
                                    FROM talep
                                    WHERE dogrudan = TRUE
                                    ORDER BY yaratma_zaman DESC, sn DESC;";              
      break;   
    
  } 
//echo $sorguCumle;

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    echo "<br>" . $sorguCumle ;
    exit;
} 


//echo "<br><br>";    

if($gelenKriter==1){ //Normal Talep
    echo '<table class= "sortable" valign="middle" id="tableMain">';
    echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
    echo '<th style="text-align:center;cursor:pointer;">Satınalma<br>S/N</th>';
    echo '<th style="text-align:center;cursor:pointer;">Detay<br>S/N</th>';
    echo '<th style="text-align:center;cursor:pointer;">Konu</th>';
    echo '<th style="text-align:center;cursor:pointer;">Şube</th>';
    echo '<th style="text-align:center;cursor:pointer;">Stok</th>';
    echo '<th style="text-align:center;cursor:pointer;">Miktar</th>';
    echo '<th style="text-align:center;cursor:pointer;">Birim</th>';
    echo '<th style="text-align:center;cursor:pointer;">Nakit Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">Bankadan Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">Evrakla Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">K.K. Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">Açık Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">Evrak Bilgisi</th>';
    echo '<th style="text-align:center;cursor:pointer;">İşlem</th>';
    echo "</tr>";
}else { //Doğrudan satınalma
    echo '<table class= "sortable" valign="middle" id="tableMain">';
    echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
    echo '<th style="text-align:center;cursor:pointer;">Satınalma<br>S/N</th>';    
    echo '<th style="text-align:center;cursor:pointer;">Konu</th>';
    echo '<th style="text-align:center;cursor:pointer;">Şube</th>';
    echo '<th style="text-align:center;cursor:pointer;">Nakit Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">Bankadan Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">Evrakla Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">K.K. Ödenen</th>';
    echo '<th style="text-align:center;cursor:pointer;">Açık Ödenen</th>';    
    echo '<th style="text-align:center;cursor:pointer;">İşlem</th>';
    echo "</tr>";
    
}

//$sayac = 1;

while($row = pg_fetch_row($ret)){   
    
    if($gelenKriter==1){
        echo "<tr class='item' >";
        echo "<td style='text-align:center'>". $row[0] . "</td>"; //S/N       
        echo "<td style='text-align:center'>". $row[12] . "</td>"; //Detay S/N       
        echo "<td>". $row[1] . "</td>"; //Konu
        echo "<td>". $row[2] . "</td>"; //Detay
        
        echo "<td>". $row[3] . "</td>"; //Şube
        echo "<td style='text-align:right'>". number_format($row[4], 2, ',', '.') . "</td>";   //Miktar
        echo "<td>". $row[5] . "</td>"; //Şube
        echo "<td style='text-align:right'>". number_format($row[6], 2, ',', '.') . "</td>";   //Nakit ödenen
        echo "<td style='text-align:right'>". number_format($row[7], 2, ',', '.') . "</td>";   //Bankadan ödenen
        echo "<td style='text-align:right'>". number_format($row[8], 2, ',', '.') . "</td>";   //Evrakla ödenen
        echo "<td style='text-align:right'>". number_format($row[9], 2, ',', '.') . "</td>";   //K.K.  ödenen
        echo "<td style='text-align:right'>". number_format($row[10], 2, ',', '.') . "</td>";   //Açık.  ödenen
        echo "<td>". $row[11] . "</td>"; //Şube

            //İŞLEMLER kısmı
            //Detay, silme ve Hikaye Tuşları
            echo "<td style='vertical-align: middle; text-align:center'>";
            echo "<div style='margin: 0 auto; width: 100px'>";

            if(belgesiVarMi("talepBelgeler", $row[0], $row[12])==true){        
                //Belgeleri Göster
                echo '<form action="TalepBelgeleri.php" target="_blank"  method="post"  class="inline">'; 
                echo '<input type="hidden" name="talepSn" value="'.$row[0].'">';    
                echo '<input type="hidden" name="talepDetaySn" value="'.$row[12].'">';
                echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';      
                //echo '<input type="hidden" name="kaynak" value="DogrudanHarcamaListesi.php">';  
                echo '<input type="hidden" name="tamamlandiMi" value="'.$row[3].'">';    
                echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$row[0].'"/></button>';
                //echo '<button type="submit">Detay</button>';
                echo '</form>';
            }



            echo "</div>";
            echo "</td>";
            //Detay ve Hikaye Tuşları sonu

            echo "</tr>";

    }else { //Doğrudan harcama
        echo "<tr class='item' >";
        echo "<td style='text-align:center'>". $row[0] . "</td>"; //S/N               
        echo "<td>". $row[1] . "</td>"; //Konu
        echo "<td>". $row[2] . "</td>"; //Şube       
        
        echo "<td style='text-align:right'>". number_format($row[3], 2, ',', '.') . "</td>";   //Nakit ödenen
        echo "<td style='text-align:right'>". number_format($row[4], 2, ',', '.') . "</td>";   //Bankadan ödenen
        echo "<td style='text-align:right'>". number_format($row[5], 2, ',', '.') . "</td>";   //Evrakla ödenen
        echo "<td style='text-align:right'>". number_format($row[6], 2, ',', '.') . "</td>";   //K.K.  ödenen
        echo "<td style='text-align:right'>". number_format($row[7], 2, ',', '.') . "</td>";   //Açık.  ödenen 

        //İŞLEMLER kısmı
            //Detay, silme ve Hikaye Tuşları
            echo "<td style='vertical-align: middle; text-align:center'>";
            echo "<div style='margin: 0 auto; width: 100px'>";

            if(belgesiVarMi("talepBelgeler", $row[0], 0)==true){        
                //Belgeleri Göster
                echo '<form action="TalepBelgeleri.php" target="_blank"  method="post"  class="inline">'; 
                echo '<input type="hidden" name="talepSn" value="'.$row[0].'">';    
                echo '<input type="hidden" name="talepDetaySn" value="'.$row[12].'">';
                echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';      
                //echo '<input type="hidden" name="kaynak" value="DogrudanHarcamaListesi.php">';  
                echo '<input type="hidden" name="tamamlandiMi" value="'.$row[3].'">';    
                echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$row[0].'"/></button>';
                //echo '<button type="submit">Detay</button>';
                echo '</form>';
            }

            echo "</div>";
            echo "</td>";
            //Detay ve Hikaye Tuşları sonu

            echo "</tr>";
        
    }



}

echo "</table>";




pg_close($db);


?>

</body>
</html>
