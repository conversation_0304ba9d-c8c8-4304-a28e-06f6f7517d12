<?php
require('yetki.php');
$ekranKod = 84;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>
<script src="jquery-3.6.0.js"></script>


<style style="text/css">






#ibandiv {
  position: absolute;
  z-index: 9;
  background-color: #f1f1f1;
  text-align: center;
  border: 1px solid #d3d3d3;
  left: 500px;
  top: 100px;
  
}

#ibandivheader {
  padding: 10px;
  cursor: move;
  z-index: 10;
  background-color: #2196F3;
  color: #fff;
}















    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
	.tusBosluk2 {
        margin-left :30px ;
        margin-right :30px ;
    }	
		
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }

        
/** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}


















</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
//include('blink.html');
?>


<script type="text/javascript">

function benzersizNumaraAl(sender, hedef){
	
	if (!confirm("Rastgele Fatura numarası verilecek!\nAlım Faturasız mı?\nEmin misiniz??")){
          return true;
    }
	
	
	
	var id = "ID" + Math.random().toString(16).slice(2)
	
	document.getElementById(hedef).value= id;
	
	
}


function eFaturaListele(sender){
	
	var tarih1 = document.getElementById("eFaturaTarih1").value;
	var tarih2 = document.getElementById("eFaturaTarih2").value;
	
	if(tarih1=="" || tarih2==""){
		alert("E-Fatura Tarih aralığı mutlaka belirlenmelidir!");
		return false;
	}
	
	//İşlem devam ediyor...
	var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
      document.body.style.cursor  = 'default';
	  
	  try{
		  document.getElementById("myInputHepsi2").focus();
		}catch{
		  //
		}
	  
    }
    };
	
	document.body.style.cursor  = 'wait';
	
	document.getElementById('baslik').innerHTML ="E-Fatura Listesi";
	xhttp.open("POST", "EFaturaListesiVer.php", true);
	xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xhttp.send("hedefTransfer=eFaturaDonus&tarih1="+tarih1+"&tarih2="+tarih2);
	
	// Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
	
	
	
	
	
	
}

function eFaturaDonus(gelenFaturaNo, gelenTutar, gelenCariAd){
	//console.log(gelenFaturaNo + " - " + gelenTutar);
	
	document.getElementById("faturaNo").value = gelenFaturaNo; 
	document.getElementById("eFaturaAd").value = gelenCariAd; 
	
	modalKapat();
}


function divAc(gelenDiv) {  
  var x = document.getElementById(gelenDiv);
  x.style.display = "block";
  console.log( gelenDiv + ' DIV açıldı');
}

function divKapat(gelenDiv) {  
  var x = document.getElementById(gelenDiv);
  x.style.display = "none";
  console.log( gelenDiv + ' DIV Kapandı');
}





//IBAN kontrol sürükleme için
function dragElement(elmnt) {
  var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
  if (document.getElementById(elmnt.id + "header")) {
    /* if present, the header is where you move the DIV from:*/
    document.getElementById(elmnt.id + "header").onmousedown = dragMouseDown;
  } else {
    /* otherwise, move the DIV from anywhere inside the DIV:*/
    elmnt.onmousedown = dragMouseDown;
  }

  function dragMouseDown(e) {
    e = e || window.event;
    e.preventDefault();
    // get the mouse cursor position at startup:
    pos3 = e.clientX;
    pos4 = e.clientY;
    document.onmouseup = closeDragElement;
    // call a function whenever the cursor moves:
    document.onmousemove = elementDrag;
  }

  function elementDrag(e) {
    e = e || window.event;
    e.preventDefault();
    // calculate the new cursor position:
    pos1 = pos3 - e.clientX;
    pos2 = pos4 - e.clientY;
    pos3 = e.clientX;
    pos4 = e.clientY;
    // set the element's new position:
    elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
    elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
  }

  function closeDragElement() {
    /* stop moving when mouse button is released:*/
    document.onmouseup = null;
    document.onmousemove = null;
  }
}












function veriTransferBanka(gelenBanka){
    //Dönen değeri yazıyor...    
    document.getElementById("bankaKod").value=gelenBanka;	
	
    modalKapat(); 
}


function veriTransfer(gelenCariKod, gelenCariAd, gelenBlKodu, gelenAdres){ //Cari liste seçili ise bu rutin çalışacak
    document.getElementById("cariKod").value = gelenCariKod;
    document.getElementById("cariAd").innerHTML = gelenCariAd;    

    modalKapat();     
}

function veriTransfer3(gelenKKNo, gelenBankaAd, gelenBakiye, gelenHesapKesimTarihi, kkBlKodu, kkTanim, kkBilgi){//Kredi kartı

document.getElementById("kkKod").value = kkBilgi;
modalKapat();     

}





function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}



function modalKapat() {
	//console.log("Modal kapanma rutini");
    // Get the modal
	document.getElementById("icerik").innerHTML = "";
	document.getElementById("baslik").innerHTML = "";
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
    document.body.style.cursor  = 'default';    
}


//Tüm kolonlarda arama

function searchTableColumns(gelenTablo, gelenAlan,gelenHedefAlan) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      var bulunanKayitAdet = 0; //Kayıt sayısı için
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
            bulunanKayitAdet++;
        } else {
            tr[i].style.display = "none";
        }
      }   

      //Kayıt adedi yazılıyor.

      if(bulunanKayitAdet > 0){
          document.getElementById(gelenHedefAlan).innerHTML = bulunanKayitAdet + " Adet kayıt listelendi"; 
      }else{
          document.getElementById(gelenHedefAlan).innerHTML = ""; 
      }





}

// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    //modal.style.display = "none";
	modalKapat();
    document.body.style.cursor  = 'default';
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    //modal.style.display = "none";
	modalKapat();
}






function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sender){

    var hataVar = false;

    var nakit = 0;
    var banka = 0;
    var evrak = 0;
    var kK = 0;
    var acik = 0;


    nakit = document.getElementById("nakit").value; 
    banka = document.getElementById("banka").value; 
    evrak = document.getElementById("evrak").value; 
    kK = document.getElementById("kk").value; 
    acik = document.getElementById("acik").value; 
	//-------------
	var kdvOran = document.getElementById("kdvOran").value; 
	var tevkifatOran = document.getElementById("tevkifatOran").value;
	//*************
	var faturaDurumu = document.getElementById("faturaDurumu").value;
		
	
	if(kdvOran==""){
		alert("KDV oranı mutlaka girilmelidir!");
		return false;
	}
	
	if(tevkifatOran==""){
		alert("Tevkifat oranı mutlaka girilmelidir!");
		return false;
	}
	
	
	if(kdvOran!= "0" && faturaDurumu=="Fiktif"){
		alert("KDV'si olan Fatura Fiktif Olamaz!\nLütfen Fatura Tipini Reel olarak düzeltin!\nİşlem iptal edilecek.");
		return false;
    }


	

    var cariKod = document.getElementById("cariKod").value;

    var faturaNo = document.getElementById("faturaNo").value;

    var kkNo = document.getElementById("kkKod").value;

    var bankaKod = document.getElementById("bankaKod").value;

    if(cariKod==""){
        alert("Cari kod SEÇİLMELİDİR!\nİşlem iptal edilecek.");
        return false;
    }

    if(banka>0 && bankaKod==""){
        alert("Banka ödemesi varsa mutlaka banka SEÇİLMELİDİR!\nİşlem iptal edilecek.");
        return false;
    }



    if(kK>0 && kkNo==""){
        alert("KK ödeme varsa mutlaka kredi kartı SEÇİLMELİDİR!\nİşlem iptal edilecek.");
        return false;
    }

    if(faturaDurumu=="---"){
		alert("Fatura Durumu girilmelidir!\nİşlem iptal edilecek.");
		return false;
    }	


    if(faturaNo=="" || faturaNo.length<4){
        alert("Fatura No en az 4 haneli olmalıdır!\nİşlem iptal edilecek.");
        return false;
    }

    if((nakit+banka+evrak+kK+acik)==0){
        alert("En az bir harcama bilgisi girilmelidir!\nİşlem iptal edilecek.");
        //sender.style.display = "block";
        return false;
    }else{    
        sender.style.display = "none";
        return true;
    }

    /*
    if(document.getElementById("sube").value=="---"){
        alert("Şube girilmelidir!\nİşlem iptal edilecek.");
        hataVar = true;
    }
    */



    /*
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edilecek.");
        hataVar = true;
    }  


    if(document.getElementById("talepEden").value==""){
        alert("Talep Eden mutlaka girilmelidir!\nİşlem iptal edilecek.");
        hataVar = true;
    } 

    if(hataVar){
        alert("Veri giriş hatası!\nİşlem iptal edildi.");
        return false;


    }else {
        //document.getElementById("kisiBilgileri").value = kisilerListesi;
        return true;
    }

    */ 



}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}

function kkKontrol(){
    if(document.getElementById("kk").value==0) document.getElementById("kkKod").value="";
    //console.log(document.getElementById("kk").value);

}


function kdvEkle(params){   

    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();

    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
        document.getElementById("icerik").innerHTML = this.responseText;
        document.body.style.cursor  = 'default';
    }
    };

           
    document.getElementById('baslik').innerHTML ="KDV Ekleme";
    xhttp.open("POST", "KdvEkle.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("hedefFonksiyon=veriTransferKdv&param="+params);

    


    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";

    
}

function geciciFonksiyon(gelenHedef){

    var sonuc = document.getElementById('sonucKdvli').value;
	var iskonto = document.getElementById('iskonto').value;
	var kdv = document.getElementById('kdv').value;
	var tevkifat = document.getElementById('tevkifat').value;
	
	if(iskonto==""){
		iskonto=0;
	}
	
	if(kdv==""){
		kdv=0;
	}
	
	if(tevkifat==""){
		tevkifat=0;
	}

    if(sonuc==0){
        alert("Veri hatası!");
        return false;
    }    

    veriTransferKdv(gelenHedef, sonuc, iskonto, kdv, tevkifat);


}

function iskontoOranDegis(){
	
	var tutar = parseFloat(document.getElementById('tutar').value);    
	//var iskonto = parseFloat(document.getElementById('iskonto').value);
	var iskontoTutar = parseFloat(document.getElementById('iskontoTutar').value);
	
	if(tutar>0){
		document.getElementById('iskonto').value = iskontoTutar  / tutar * 100;
		
	}
	
	
	console.log(iskontoTutar);
	
	
	
	
	
	
	
	
	
	kdvHesapla();
	
	
	
	
}

function kdvHesapla(){
	
    var tutar = parseFloat(document.getElementById('tutar').value);
    var kdvOran = parseFloat(document.getElementById('kdv').value);
    var tevkifat = parseFloat(document.getElementById('tevkifat').value);
	var iskonto = parseFloat(document.getElementById('iskonto').value);
	var miktar = parseFloat(document.getElementById('miktar').value);
	
	

	var iskontoTutar = tutar * iskonto/100;
    
	tutar = tutar -  iskontoTutar;

    var kdvTutar = tutar * kdvOran/100;
	
	
	

    var tevkifatTutar = 0;

    if(tevkifat>0){
        tevkifatTutar = kdvTutar * tevkifat / 100;
    }else{

        tevkifatTutar = 0;

    }
    

    var sonuc = tutar + kdvTutar - tevkifatTutar ;    

    document.getElementById('sonucKdvli').value = roundNumber((sonuc*miktar), 2);
	document.getElementById('sonucKdvsiz').value = roundNumber((sonuc*miktar/(1+((kdvOran/100)*(1-(tevkifat/100))))), 2);
	
	document.getElementById('iskontoTutar').value = iskontoTutar ;
	


}



function veriTransferKdv(gelenHedef, gelenTutar, iskonto, kdv, tevkifat){

    //console.log("xxx");
    //console.log(gelenTutar);

    switch (gelenHedef) {
        case 1:
            document.getElementById("nakit").value = gelenTutar ;            
            break;

        case 2:
            document.getElementById("banka").value = gelenTutar ;            
            break;

        case 3:
            document.getElementById("evrak").value = gelenTutar ;            
            break;

        case 4:
            document.getElementById("kk").value = gelenTutar ;            
            break;

        case 5:
            document.getElementById("acik").value = gelenTutar ;            
            break;
    
        default:
            break;
    }
	
	
	document.getElementById("iskontoOran").value = iskonto;
	document.getElementById("kdvOran").value = kdv;
	document.getElementById("tevkifatOran").value = tevkifat;
	
	

    modalKapat();
}




function modalGoster(gelenModelKod){ 

    //Nakit kısmı boş ise cari kod açılmaz!
    //if(document.getElementById("nakit").value==0 && document.getElementById("acik").value==0) return false; 

    //Kredi kartı ödemesi girilmeli!
    if(document.getElementById("kk").value==0 && gelenModelKod==2) return false; 


    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
      document.body.style.cursor  = 'default';
    }
	
	try{
	  document.getElementById("myInputHepsi2").focus();
	}catch{
	  //
	}
	
	
    };
    if(gelenModelKod==0){
        document.getElementById('baslik').innerHTML ="Cari Listesi";
        xhttp.open("POST", "CariVerAkinsoftdan.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==1){
        document.getElementById('baslik').innerHTML ="Talepler Listesi";
        xhttp.open("POST", "TalepIlgiVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        cariKod = document.getElementById("cariKod").value;
        //console.log(cariKod);
        xhttp.send("cariKod=" + cariKod);
    }else if(gelenModelKod==2){
        document.getElementById('baslik').innerHTML ="Kredi Kartları";
        xhttp.open("POST", "KrediKartlariVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==3){
        document.getElementById('baslik').innerHTML ="Kredi Ödemeleri";
        xhttp.open("POST", "KrediOdemeleriVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==4){
        document.getElementById('baslik').innerHTML ="Banka Seçim";
        xhttp.open("POST", "BankaVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("hedefFonksiyon=veriTransferBanka");
    }

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>



<?php

echo '<br><h1 style="text-align:center;">Talep Hareket Girişi</h1>';


function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}


function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}



function acikTalepDetayVarMi($talepSn){

    global $db;

    $sorgu="SELECT COUNT(*) FROM talep_detay WHERE talep_sn = $talepSn
                                                    AND iptal=false
                                                    AND tamamlandi=false;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Talep detay bulunamadı";
      exit;
   }  
   
   $donenDeger=true;
   
   while($row = pg_fetch_row($ret))
   {
	  if($row[0]>0) $donenDeger=true; else $donenDeger=false;	   
   }  

   return $donenDeger;
}


function talepBilgileriniAl($gelenTalepSn){

    global $db;
    $sorgu = "SELECT konu, detay FROM talep WHERE sn = $gelenTalepSn;";
    $ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Talep bulunamadı";
        exit;
     }   
     $donenDeger = array();
     
     while($row = pg_fetch_row($ret)){
        $donenDeger = array($row[0], $row[1]);        
     }
     
     return $donenDeger;
}


function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn=$gorevSn;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret)){
	   $liste =$row[0];	   
   }  

   return $liste;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }



function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}


function subeListesiVer(){
    
    global $aktifSirketBLKodu;
    global $aktifSirketDB;

    $donenListe=[];

    $sorguCumle = "SELECT SUBE_ADI FROM SUBE WHERE BLSRKODU=$aktifSirketBLKodu AND AKTIF = 1 ORDER BY SUBE_ADI;";

    try {	
        $dbh = new \PDO($aktifSirketDB , 'sysdba', 'masterkey');
        
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            array_push($donenListe, utf8MetinYap($row[0]));
        }
        $dbh = null;
        $ret = null;
        
        }
        catch (PDOException $e) {
            print "Hata!: " . $e->getMessage() . "<br/>";
            $dbh = null;
            $ret = null;
            die();
        }

    return $donenListe;
}

function textKilitliMi($gelenTamamlandi){
    if($gelenTamamlandi == "t") return ' readonly '; else return '';
}


function cariAdVer($gelenCariKod){

    global $aktifDB;
    $gelenCariKod = isoMetinYap($gelenCariKod);

    $sorguCumle ="SELECT TICARI_UNVANI FROM CARI 
                WHERE CARIKODU = '$gelenCariKod';";

    $donenAd ="";

    try {
        $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');

        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            $donenAd = utf8MetinYap($row[0]);
        }      	
        
        
        //VT sıfırlanıyor...
        $dbh = null;
        $ret = null;
        
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        die();
    }
    
    
    return $donenAd;

 }




//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */


//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("AkinsoftIslemleri.php");
require("mesajlasma.php");
require("logYaz.php");
require("dosyalariYukle.php");
require("sqlMetinDuzenle.php");
require('blink.html');
//require('AyarlardanCek.php');




$gelenTalepSn = $_POST['talepSn'];
$gelenTalepDetaySn = $_POST['talepDetaySn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];
$gelenKonu = $_POST['talepKonu'];
//$gelenAciklama = $_POST['talepAciklama'];

$gelenAciklama = sqlMetinDuzenle($_POST["talepAciklama"]);


$gelenKaydet = $_POST['kaydet'];
$gelenKaynak = $_POST['kaynak'];
//------------------------------
$gelenNakit =  $_POST['nakit'];
$gelenBanka =  $_POST['banka'];
$gelenEvrak =  $_POST['evrak'];
$gelenKk =  $_POST['kk'];
$gelenAcik =  $_POST['acik'];

//------------------------------
$gelenFaturaBilgisi = $_POST['faturaBilgisi'];
$gelenTalepHareketSn = $_POST['talepHareketSn'];


//------------------------------
$gelenCariKod =  $_POST['cariKod'];

$gelenFaturaDurumu = $_POST['faturaDurumu'];
$gelenFaturaNo = $_POST['faturaNo'];

//****************

$gelenFaturaTarih = $_POST['faturaTarih'];

//------------
$gelenKkKod =  $_POST['kkKod'];
$gelenBankaKod =  $_POST['bankaKod'];

//----------------

$gelenKDVOran = $_POST['kdvOran'];
$gelenTevkifatOran = $_POST['tevkifatOran'];

$gelenIskontoOran = $_POST['iskontoOran'];

/*
if($gelenKDVOran==""){
	$gelenKDVOran = "-";//ayarlardanCek('KDV');	
}


if($gelenTevkifatOran==""){
	$gelenTevkifatOran = "-";	
}
*/


//$gelenEFaturaTarih1=date('Y-m-d');

$gelenEFaturaTarih1 =  date('Y-m-d', strtotime(' - 1 months')); //"2021-12-31";

$gelenEFaturaTarih2=date('Y-m-d');


if($gelenFaturaTarih=="") $gelenFaturaTarih=date('Y-m-d');



//echo ini_get('upload_max_filesize');

/*
echo $gelenTalepSn."<br>"; 
echo $gelenTalepDetaySn."<br>"; 
echo $gelenIslemTip."<br>"; 
echo $gelenKriter."<br>"; 
*/

//$gelenTamamlandi = $_POST['tamamlandi'];


//$gelenKayitMod = $_POST['kayitMod'];

if($gelenIslemTip=="") $gelenIslemTip = "Yeni Kayıt";



if($gelenIslemTip=='Yeni Kayıt' && $gelenKaydet =='1'){ //Hareket yeni kayıt.


    $sorguCumle = "INSERT INTO talep_hareket(talep_sn, talep_detay_sn, nakit_odenen, bankadan_odenen, evrakla_odenen, kk_odenen, acik_odenen, talep_fatura_bilgisi, nakit_odenen_cari_kod, fatura_durumu, fatura_no, kk_odenen_kk_kod, odenen_banka_kod, kdv_oran, tevkifat_oran, kullanici, iskonto_oran, fatura_tarih )
                         VALUES($gelenTalepSn, $gelenTalepDetaySn, $gelenNakit, $gelenBanka, $gelenEvrak, $gelenKk, $gelenAcik, '$gelenFaturaBilgisi', '$gelenCariKod', '$gelenFaturaDurumu', '$gelenFaturaNo', '$gelenKkKod', '$gelenBankaKod', $gelenKDVOran, $gelenTevkifatOran, '$kullanici', $gelenIskontoOran, '$gelenFaturaTarih') RETURNING sn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    }

    $donenSn = 0;

    while($row = pg_fetch_row($ret)){
        $donenSn = $row[0];
    }

    //Dosya geliyorsa kaydedecek.
    dosyalariYukle("/mnt/talepBelgeler/".$gelenTalepSn."/".$gelenTalepDetaySn."/", "Talep");  

    if($donenSn>0){ //İşlem tamam yani.

        echo "İşlem Tamam. Satınalma girişi: ". $donenSn . " numarası ile kaydedilmiştir. ";

        //Geri dönüş
        echo '<br><br><br>';    
        echo '<form action="'.$gelenKaynak.'" method="post">';
        echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';    
        //echo '<input type="hidden" id="tarih1" name="tarih1" value="'.$gelenTarih1.'">';
        //echo '<input type="hidden" id="tarih2" name="tarih2" value="'.$gelenTarih2.'">';
        echo '<input type="submit" value="Geri Dön" style="width:200px;">';
        echo '</form>';

        pg_close($db);

        exit;


    }




}
elseif($gelenIslemTip=='Düzenleme' && $gelenTalepHareketSn<>"" && $gelenKaydet ==""){

    /*
    $sorguCumle = "INSERT INTO talep_hareket(talep_sn, talep_detay_sn, nakit_odenen, bankadan_odenen, evrakla_odenen, kk_odenen, acik_odenen, talep_fatura_bilgisi)
                         VALUES($gelenTalepSn, $gelenTalepDetaySn, $gelenNakit, $gelenBanka, $gelenEvrak, $gelenKk, $gelenAcik, '$gelenFaturaBilgisi') RETURNING sn;";
    */

    $sorguCumle = "SELECT sn, 
						  talep_sn, 
						  talep_detay_sn, 
						  nakit_odenen, 
						  bankadan_odenen, 
						  evrakla_odenen, 
						  kk_odenen, 
						  acik_odenen, 
						  talep_fatura_bilgisi, 
						  nakit_odenen_cari_kod, 
						  fatura_durumu, 
						  fatura_no, 
						  kk_odenen_kk_kod, 
						  odenen_banka_kod,
						  kdv_oran, /*14*/
						  tevkifat_oran,	/*15*/					  
						  iskonto_oran, 		/*16*/
						  fatura_tarih  /*17*/
                    FROM talep_hareket
                    WHERE sn=$gelenTalepHareketSn;";

                   //VALUES('$gelenKonu', '$gelenTalepEden', '$kullanici', '$gelenSube') RETURNING sn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    }

    $talepBilgiler = talepBilgileriniAl($gelenTalepSn);

    $gelenKonu = $talepBilgiler[0];
    //$gelenAciklama = $talepBilgiler[1];
    $gelenAciklama = sqlMetinDuzenle($talepBilgiler[1]);

    while($row = pg_fetch_row($ret)){
        $gelenTalepSn = $gelenTalepSn;
        $gelenTalepDetaySn = $gelenTalepDetaySn;
        //-------------------
        
        $gelenNakit = $row[3];
        $gelenBanka = $row[4];
        $gelenEvrak = $row[5];
        $gelenKk = $row[6];
        $gelenAcik = $row[7];
        $gelenFaturaBilgisi = $row[8];
        $gelenCariKod = $row[9];
        $gelenCariAd = cariAdVer($row[9]);
        $gelenFaturaDurumu = $row[10];
        $gelenFaturaNo = $row[11];
        $gelenKkKod = $row[12];
        $gelenBankaKod = $row[13];
		$gelenKDVOran = $row[14];
		$gelenTevkifatOran = $row[15];
		$gelenIskontoOran = $row[16];
		$gelenFaturaTarih = $row[17];

    }




}elseif($gelenIslemTip=='Yeni Kayıt' && $gelenKaydet ==''){

    //Sayfa çağrıldığında bu geliyor.
    //Yeni giriş. Boş sayfa yani.

    
    $gelenNakit=0;
    $gelenBanka=0;
    $gelenEvrak=0;
    $gelenKk=0;
    $gelenAcik=0;
    $gelenFaturaBilgisi = '';
    $gelenFaturaDurumu = "";
    $gelenFaturaNo = "";
    $gelenKkKod = "";
    $gelenBankaKod = "";
	$gelenKDVOran = "";
	$gelenTevkifatOran = "";
	$gelenIskontoOran = 0;
	$gelenFaturaTarih = date('Y-m-d');

}elseif($gelenIslemTip=='Düzenleme' && $gelenTalepHareketSn<>"" && $gelenKaydet =="1"){//Düzenleme yapılıyor.

    /*
    $sorguCumle = "INSERT INTO talep_hareket(talep_sn, talep_detay_sn, nakit_odenen, bankadan_odenen, evrakla_odenen, kk_odenen, acik_odenen, talep_fatura_bilgisi)
                         VALUES($gelenTalepSn, $gelenTalepDetaySn, $gelenNakit, $gelenBanka, $gelenEvrak, $gelenKk, $gelenAcik, '$gelenFaturaBilgisi') RETURNING sn;";

    */
    
    $sorguCumle = "UPDATE talep_hareket SET nakit_odenen = $gelenNakit, bankadan_odenen = $gelenBanka, evrakla_odenen = $gelenEvrak, 
                    kk_odenen = $gelenKk, acik_odenen = $gelenAcik, talep_fatura_bilgisi = '$gelenFaturaBilgisi', nakit_odenen_cari_kod = '$gelenCariKod', 
                    fatura_durumu = '$gelenFaturaDurumu', fatura_no = '$gelenFaturaNo', kk_odenen_kk_kod = '$gelenKkKod', odenen_banka_kod = '$gelenBankaKod',
					kdv_oran = $gelenKDVOran, tevkifat_oran = $gelenTevkifatOran, iskonto_oran = $gelenIskontoOran, fatura_tarih = '$gelenFaturaTarih'						
                    WHERE sn = $gelenTalepHareketSn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    }

    dosyalariYukle("/mnt/talepBelgeler/".$gelenTalepSn."/".$gelenTalepDetaySn."/", "Talep");

    echo "Kayıt güncellenmiştir.". "<br>";


    echo '<br><br><br>';    
    echo '<form action="" method="post" onclick="self.close()">';
    //echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';    
    //echo '<input type="hidden" id="tarih1" name="tarih1" value="'.$gelenTarih1.'">';
    //echo '<input type="hidden" id="tarih2" name="tarih2" value="'.$gelenTarih2.'">';
    echo '<input type="submit" value="Kapat" style="width:200px;">';
    echo '</form>';

    pg_close($db);

    exit;

}



//Talep kapatma kısmı
if($gelenTalepHareketSn<>""){
    echo "<b>Talep Hareket No: ". $gelenTalepHareketSn . "<br></b>";
    echo "<b>------------<br></b>";
}
echo "<b>Talep No: ". $gelenTalepSn . "<br></b>";
echo "<b>Talep Detay No: ". $gelenTalepDetaySn . "<br></b>";

//Cari kodun görünmesi için...

if($gelenTalepSn<>"" && $gelenTalepDetaySn<>"" && $gelenKaydet==""){
	
	$sorguCumle = "SELECT cari_kod FROM talep WHERE sn=$gelenTalepSn;";
	
	$ret = pg_query($db, $sorguCumle);
	
	while($row = pg_fetch_row($ret)){
		$gelenCariKod = $row[0];
        $gelenCariAd = cariAdVer($row[0]);		
	}
	
	
}

//Buradan sonra form başlıyor.



echo '<form action="#" method="post"  enctype="multipart/form-data">';
echo "<br>";
echo "<b>Konu:</b><br>";
echo '<input type="text" readonly  id="konu" style="font-size: 12pt; width:100%;height:100%"  name="konu" value="'.$gelenKonu.'"><br>';
echo "<br>";
echo "<b>Açıklama:</b><br>";
echo '<input type="text" readonly id="talepEden"  style="font-size: 12pt; width:100%;height:100%"  name="talepEden" value="'. sqlMetinDuzenle($gelenAciklama).'"><br>';

//echo sqlMetinDuzenle($gelenAciklama);





echo "<br>";
echo "<b>Nakit Ödenen:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Cari Kod: </b><br>";
echo '<input type="number" '.textKilitliMi($gelenTamamlandi).' id="nakit" name="nakit" value="'.$gelenNakit.'" step="0.01" min ="0.0">&nbsp; &nbsp; ';
//echo '<button type="button" onclick="kdvEkle(1);"><img src="Kdv.svg" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>&nbsp; &nbsp;';
echo '<input type="text" id="cariKod" onclick="modalGoster(0);" style="background-color:#fff333;"  readonly style="height:100%" title="Nakit girişi varsa mutlaka cari seçilmelidir!"  name="cariKod" value="'.$gelenCariKod.'">&nbsp; <label id="cariAd">'.$gelenCariAd.'</label>';

echo "&nbsp;&nbsp;<font class='blink'>Tüm tutarlar KDV DAHİL Yazılacaktır...</font><br><br>";


echo "<b>Fatura Durumu: </b><br>";
echo '<select name="faturaDurumu" id="faturaDurumu">';
    if($gelenFaturaDurumu=="") echo '<option value="---" selected >---</option>';
    echo '<option value="Fiktif"' . selectedKoy($gelenFaturaDurumu, "Fiktif"). '>Fiktif</option>';
    echo '<option value="Reel"' . selectedKoy($gelenFaturaDurumu, "Reel"). '>Reel</option>';
echo "</select><br><br>";



//echo '<br><br><br>';

echo "<b>Banka Ödenen:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Banka Kod: </b><br>";
echo '<input type="number" '.textKilitliMi($gelenTamamlandi).'  id="banka" name="banka" value="'.$gelenBanka.'" step="0.01" min ="0.0">&nbsp; &nbsp; ';
echo '<button type="button" onclick="kdvEkle(2);"><img src="Kdv.svg" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>&nbsp; &nbsp;';
echo '<input type="text" id="bankaKod" onclick="modalGoster(4);" readonly style="background-color:#fff333;"  style="height:100%" size="60" title="Banka ödemesi varsa mutlaka seçilmelidir!" name="bankaKod" value="'.$gelenBankaKod.'"><br><br>';

echo "<b>Evrakla Ödenen: </b><br>";
echo '<input type="number" '.textKilitliMi($gelenTamamlandi).'  id="evrak" name="evrak" value="'.$gelenEvrak.'" step="0.01" min ="0.0">&nbsp; &nbsp; ';
echo '<button type="button" onclick="kdvEkle(3);"><img src="Kdv.svg" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>';
echo "<br><br><br>";



echo "<b>K.K. ile Ödenen:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Kredi Kartı: </b><br>";
echo '<input type="number" '.textKilitliMi($gelenTamamlandi).'  id="kk" name="kk" oninput="kkKontrol();" value="'.$gelenKk.'" step="0.01" min ="0.0">&nbsp; &nbsp;';
echo '<button type="button" onclick="kdvEkle(4);"><img src="Kdv.svg" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>&nbsp; &nbsp;';
echo '<input type="text" id="kkKod" onclick="modalGoster(2);" readonly style="background-color:#fff333;"  style="height:100%" title="Kredi kartı girişi varsa mutlaka KK seçilmelidir!"  name="kkKod" value="'.$gelenKkKod.'">&nbsp;<br><br><br>'; // <label id="cariAd">'.$gelenCariAd.'</label>

echo "<b>Açık Hesap Ödenen: </b><br>";
echo '<input type="number" '.textKilitliMi($gelenTamamlandi).'  id="acik" name="acik" step="0.01" min ="0.0" value="'.$gelenAcik.'">&nbsp; &nbsp;';
echo '<button type="button" onclick="kdvEkle(5);"><img src="Kdv.svg" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>';
echo "<br><br><br>";

//*********************

echo "<b>KDV Oranı (%): </b><br>";
echo '<input type="number" '.textKilitliMi($gelenTamamlandi).'  id="kdvOran"  name="kdvOran" width="5" step="1" min ="0" value="'.$gelenKDVOran.'">&nbsp; &nbsp;';
//echo '<button type="button" onclick="kdvEkle(5);"><img src="Kdv.svg" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>';
echo "<br><br><br>";

//*********************

echo "<b>Tevkifat Oranı (%): </b><br>";
echo '<input type="number" '.textKilitliMi($gelenTamamlandi).'  id="tevkifatOran"  name="tevkifatOran" width="5" step="0.01" min ="0" value="'.$gelenTevkifatOran.'">&nbsp; &nbsp;';
//echo '<button type="button" onclick="kdvEkle(5);"><img src="Kdv.svg" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>';
echo "<br><br><br>";


//*********************

echo "<b>İskonto Oranı (%): </b><br>";
echo '<input type="number" '.textKilitliMi($gelenTamamlandi).'  id="iskontoOran"  name="iskontoOran" width="5" step="0.01" min ="0" value="'.$gelenIskontoOran.'">&nbsp; &nbsp;';
//echo '<button type="button" onclick="kdvEkle(5);"><img src="Kdv.svg" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>';
echo "<br><br><br>";



//*********************

echo "<b>Fatura Numarası<br>(Son Dört Hanesi veya E-Fatura No):</b><br>";
echo '<input type="text" id="faturaNo"  size="20" maxlength="20" required  name="faturaNo" value="'.$gelenFaturaNo.'">&nbsp; &nbsp;';

echo '<input type="date" id="eFaturaTarih1" class="tusBosluk"  name="eFaturaTarih1" value="'.$gelenEFaturaTarih1.'" title="Efatura Tarihi1">';
echo '<input type="date" id="eFaturaTarih2" class="tusBosluk" name="eFaturaTarih2" value="'.$gelenEFaturaTarih2.'" title="Efatura Tarihi2">';

echo '<button type="button" class="tusBosluk" onclick="eFaturaListele(this);"><img src="pdf-file.png" height="15" width="15" title="KDV İşlemi" valign="middle"/></button>';
echo '<button type="button" class="tusBosluk" onclick="benzersizNumaraAl(this, \'faturaNo\');"><img src="numbers.png" height="15" width="15" title="Benzersiz Numara Yaz" valign="middle"/></button><font style="color:red;">Sadece Faturasız alım ise bu tuşu kullanın!</font>';

echo '<br>';
echo '<textarea name="eFaturaAd" readonly id="eFaturaAd" rows="3" >-</textarea>';
echo '<br><br><br>';


echo '<b>Fatura Tarihi:</b><input type="date" id="faturaTarih" class="tusBosluk" required name="faturaTarih" value="'.$gelenFaturaTarih.'" title="Fatura Tarihi">';

echo '<br><br><br>';


echo "<b>Satır bilgisi:</b><br>";
echo '<input type="text" id="faturaBilgisi" style="width:100%;height:100%"  name="faturaBilgisi" value="'.$gelenFaturaBilgisi.'"><br>';

/*
echo "<b>İsteyen Şube: </b><br>";
echo '<select name="sube" id="sube">';

if($gelenSube=="") echo '<option value="---" selected >---</option>';

    foreach (subeListesiVer() as $key => $sube) {
        echo '<option value="' . $sube . '"' . selectedKoy($gelenSube, $sube). '>'.$sube.'</option>';	           
    }
echo "<br>";
echo "</select>";
*/

echo "<br><br>";
echo "<b>Dosya Ekle: </b><br>";
echo '<input type="file" name="fileToUpload[]" id="fileToUpload" multiple>';

echo "<br><br><br>";    
echo '<input type="hidden" name="talepHareketSn" value="'.$gelenTalepHareketSn.'">'; //Burası düzenlemede kullanılacak.   
echo '<input type="hidden" name="talepSn" value="'.$gelenTalepSn.'">';
echo '<input type="hidden" name="talepDetaySn" value="'.$gelenTalepDetaySn.'">';
echo '<input type="hidden" name="islemTip" value="'.$gelenIslemTip.'">';     
echo '<input type="hidden" name="kaydet" value="1">';
echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';         
echo '<input type="hidden" name="kaynak" value="'.$gelenKaynak.'">';      
echo '<button type="submit" style="width:200px;" onclick="return veriKontrol(this);">Kaydet</button>';    
echo '</form>';




echo '<div id="ibandiv" style="display:none">';
echo '<div id="ibandivheader">IBAN Kontrol</div>';
//echo '<iframe height="450" width="450" src="https://hesapno.com/mod_iban_coz" name="hesapno.com iban çözümleme modülü" scrolling="auto" frameborder="0"></iframe><br>';

echo '<button type="button" onclick="divKapat(\'ibandiv\');" style="width:200px;" >Kapat</button> ';

echo '</div>';

echo '<script>

//Make the DIV element draggagle:
dragElement(document.getElementById("ibandiv"));

</script>';




//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';




echo "<br><br><br><br>";

pg_close($db);


?>

</body>
</html>
