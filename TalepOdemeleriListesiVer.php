<?php
require('yetki.php');
$ekranKod = 65;
//require('ekranYetkileri.php');

?>

<style>
  table#tableMain2 thead { display:block; }
  table#tableMain2 tbody { height:300px; overflow-y:scroll; display:block; }
  table#tableMain2 { width: 100%; }
</style>


<script type="text/javascript">


    
</script>


<?php




//************************************************** */
//************************************************** */
//************************************************** */
//************************************************** */

require_once("PGConnect.php");


$gelenTalepSn = $_POST['talepSn'];

$dilimler = explode("-", $gelenTalepSn);


$gelenTalepDetaySn = $dilimler[1];



$sorguCumle = "SELECT sn, 
					  nakit_odenen, 
					  bankadan_odenen, 
					  evrakla_odenen, 
					  kk_odenen, 
					  acik_odenen, 
					  odenen_banka_kod, 
					  kk_odenen_kk_kod 
               FROM talep_hareket
               WHERE talep_detay_sn = $gelenTalepDetaySn;";


$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
}

$count = pg_num_rows($ret); //Sorgu sonucunun satır sayısını veriyor.

if($count<1){ //KAyıt yok
    
    echo '<p style="color:red">Doğrudan Talep Hareket Kaydına Ulaşılamadı!</p>';
    pg_close($db);
    exit;
}


echo '<table valign="middle" align="center" id="tableMain2">';
echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
echo '<th style="text-align:center">Detay<br>S/N</th>';

echo '<th style="text-align:center;">Nakit<br> Ödenen</th>';
echo '<th style="text-align:center;">Bankadan<br> Ödenen</th>';
echo '<th style="text-align:center;">Evrakla<br> Ödenen</th>';
echo '<th style="text-align:center;">K.K.<br> Ödenen</th>';
echo '<th style="text-align:center;">Açık<br> Ödenen</th>';
echo '<th style="text-align:center;">Banka<br>Bilgisi</th>';
echo '<th style="text-align:center;">K.K.<br>Bilgisi</th>';

echo "</tr>";

$sayac = 0;

while($row = pg_fetch_row($ret)){//Varolan Puantaj çağrılıyor.    

    $sayac+=1;
    
    echo "<tr>";
    
    echo "<td style='text-align:center'>". $row[0] . "</td>"; //S/N       
    
    echo "<td style='vertical-align: middle;text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";
    echo '<input type="number" id="nakit'. $sayac. '" name="nakit" maxlength="10" onfocusout="sifiraZorla(\'nakit'.$sayac.'\')" required size="10" min="0" value="'.$row[1].'" title="nakit'. $sayac . '" step="1">';		
    echo "</div></td>";
    
    echo "<td style='vertical-align: middle;text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";
    echo '<input type="number" id="bankadan'. $sayac. '" name="bankadan" maxlength="10" onfocusout="sifiraZorla(\'bankadan'.$sayac.'\')" required size="10" min="0" value="'.$row[2].'" title="bankadan'. $sayac . '" step="1">';		
    echo "</div></td>";
    
    echo "<td style='vertical-align: middle;text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";
    echo '<input type="number" id="evrak'. $sayac. '" name="evrak" maxlength="10" onfocusout="sifiraZorla(\'evrak'.$sayac.'\')" size="10" required min="0" value="'.$row[3].'" title="evrak'. $sayac . '" step="1">';		
    echo "</div></td>";


    echo "<td style='vertical-align: middle;text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";
    echo '<input type="number" id="kredi'. $sayac. '" name="kredi" maxlength="10" onfocusout="sifiraZorla(\'kredi'.$sayac.'\')" size="10" required min="0" value="'.$row[4].'" title="kredi'. $sayac . '" step="0.01">';		
    echo "</div></td>";    
    

    echo "<td style='vertical-align: middle;text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";
    echo '<input type="number" id="acik'. $sayac. '" name="acik" maxlength="10" onfocusout="sifiraZorla(\'acik'.$sayac.'\')" size="10" required min="0" value="'.$row[5].'" title="acik'. $sayac . '" step="1">';		
    echo "</div></td>";
		
	
	//*****
	//Banka
	echo "<td style='text-align:center;'>";
	echo "<div style='margin: 0 auto; width: 100px'>";
	echo '<input type="text" style="background-color:#fff333;" size="30" id="banka'. $sayac. '" onclick="modalBankaKkSec(1,  '.$sayac.'  );" name="banka" readonly value="'.$row[6].'" title="Banka seçimini yapın!">';
	echo "</div></td>";
	//K.K.
	echo "<td style='text-align:center;'>";
	echo "<div style='margin: 0 auto; width: 100px'>";
	echo '<input type="text" style="background-color:#fff333;" size="30"  id="kk'. $sayac. '" onclick="modalBankaKkSec(2, '.$sayac.' );" name="kk" readonly value="'.$row[7].'" title="K.K seçimini yapın!">';
	echo "</div></td>";
	
	
	

    echo "</tr>";
    

}


echo "</table>";
echo "<center>";
echo '<button type="button" onclick="veriKontrolTalep();" style="width:200px;" >Kaydet</button> ';
echo '<button type="button" class= "tusBosluk" onclick="modalKapat();" style="width:200px;" >Kapat</button> ';
echo "</center>";



pg_close($db);


?>
