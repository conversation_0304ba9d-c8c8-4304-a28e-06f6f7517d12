<?php
//Talep hareket giriş ve Doğrudan harcama
//kısmından Akınsoft içinde Ödeme yapmak için kullanılıyor.

//TalepHarekettenAkinsoftdaOdemeYap.php

require_once('yetki.php');
$ekranKod = 62;

$kullanici = $_SESSION["user"];
//require_once('ekranYetkileri.php');
require("PGConnect.php");
require("AkinsoftIslemleri.php");
require("logYaz.php");
require("KullanicidanBilgiVer.php");
require("mesajlasma.php");


function akinsoftBaglantiKod(){
    
	$anaSorgu = "http://************:3056/getdata.html?";

	$baglan ="command=wlogin&username=sysdba&password=abe6db4c9f5484fae8d79f2e868a673c&devCode=201046738&devPass=53C61&timeOut=60";

	$sifreliBaglan = base64_encode($baglan);

	$AkinsoftCevap = trim(file_get_contents($anaSorgu . $sifreliBaglan));

	$baglantiKod = substr(base64_decode($AkinsoftCevap), 2);
	
	return $baglantiKod;	
}


function subeKoduVer($gelenSubeAd){    
    
    global $aktifSirketDB;

    $donenDeger="";

    $gelenSubeAd = isoMetinYap($gelenSubeAd);

    $sorguCumle = "SELECT SUBE_KODU FROM SUBE WHERE SUBE_ADI='$gelenSubeAd';";

    try {	
        $dbh = new \PDO($aktifSirketDB , 'sysdba', 'masterkey');
        
        //$ret = $dbh->query($sorguCumle);

        foreach($dbh->query($sorguCumle) as $row) {
            $donenDeger = $row[0];
        }
        $dbh = null;
        $ret = null;
        
        }
        catch (PDOException $e) {
            print "Hata!: " . $e->getMessage() . "<br/>";
            $dbh = null;
            $ret = null;
            die();
        }

    return utf8MetinYap($donenDeger);
}


function subeHarcamaKasaVer($gelenSubeAd){

    global $dbh;

    //$gelenSubeAd = isoMetinYap($gelenSubeAd);

    $gelenSubeKod = isoMetinYap(subeKoduVer($gelenSubeAd));

    $sorguCumle = "SELECT KASA_ADI FROM KASA WHERE SUBE_KODU = '$gelenSubeKod' AND OZEL_KODU ='Harcama';";

    //$donenDeger = $sorguCumle;

    try {
        //$dbh = new PDO($aktifDB , 'sysdba', 'masterkey');		        
        //$ret = $dbh->query($sorguCumle);

        foreach($dbh->query($sorguCumle) as $row) {
            $donenDeger = $row[0];            
        } 


        //VT sıfırlanıyor...
        //$dbh = null;
        $ret = null;	

    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;			
        die();
    }

    return utf8MetinYap($donenDeger);

}


function cariBLKoduVer($gelenCariKod){

    global $dbh;

    $gelenCariKod = isoMetinYap($gelenCariKod);

    $sorguCumle ="SELECT BLKODU from CARI WHERE CARIKODU = '$gelenCariKod';";

    //echo $sorguCumle;

    try {
        //$dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
        //$ret = $dbh->query($sorguCumle);
        
        foreach($dbh->query($sorguCumle) as $row) {
            $donenDeger=$row[0];
        }
        
        //VT sıfırlanıyor...
        //$dbh = null;
        $ret = null;
        
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        die();
    }
    
    
    return $donenDeger;

}


function hizmetKartAdVer($gelenHizmetKod){

    global $dbh;

    //$gelenCariKod = isoMetinYap($gelenCariKod);

    $sorguCumle ="SELECT ADI from HIZMET WHERE BLKODU = $gelenHizmetKod;";

    //echo $sorguCumle;

    try {
        //$dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
        //$ret = $dbh->query($sorguCumle);
        
        foreach($dbh->query($sorguCumle) as $row) {
            $donenDeger=$row[0];
        }
        
        //VT sıfırlanıyor...
        //$dbh = null;
        $ret = null;
        
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        die();
    }
    
    
    return utf8MetinYap($donenDeger);

}


function stokKartBilgiVer($gelenStokKod){

    global $dbh;
    global $stokBlKodu;
    global $stokAdi;

    //$gelenCariKod = isoMetinYap($gelenCariKod);

    $sorguCumle ="SELECT BLKODU, STOK_ADI from STOK WHERE STOKKODU = '$gelenStokKod';";

    $sorguCumle = isoMetinYap($sorguCumle);

    //echo $sorguCumle;

    try {
        //$dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
        //$ret = $dbh->query($sorguCumle);
        
        foreach($dbh->query($sorguCumle) as $row) {
            $stokBlKodu = $row[0];
            $stokAdi = utf8MetinYap($row[1]);

        }
        
        //VT sıfırlanıyor...
        //$dbh = null;
        $ret = null;
        
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        die();
    }
    
    
    return true;

}


function subeDepoVer($gelenSubeKod){

    global $dbh;

    //$gelenSubeAd = isoMetinYap($gelenSubeAd);

    //$gelenSubeKod = isoMetinYap($gelenSubeKod);

    $sorguCumle = isoMetinYap("SELECT DEPO_ADI 
						  FROM DEPO 
						  WHERE SUBE_KODU = '$gelenSubeKod' 
						  AND COALESCE(YETKILISI,'') <> 'TALİDEPO'
						  AND AKTIF = 1;");

    $donenDeger = $sorguCumle;

    try {
        //$dbh = new PDO($aktifDB , 'sysdba', 'masterkey');		        
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            $donenDeger = $row[0];            
        } 


        //VT sıfırlanıyor...
        //$dbh = null;
        $ret = null;	

    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;			
        die();
    }

    return utf8MetinYap($donenDeger);

}



function tasitSnVer($gelenIlgigliAlim){
	
	global $db;
	global $tasitPlakasi;
	global $ilgiliAlimAciklama;
	
	if ($gelenIlgigliAlim==""){
		return 0;
	}
	
	$sorguCumle = "SELECT ilgili_alim.tasit_sn, 
						  tasitlar.plaka,
						  ilgili_alim.ilgili_alim_ad
									FROM ilgili_alim 
									JOIN tasitlar ON tasitlar.sn = ilgili_alim.tasit_sn
									WHERE ilgili_alim.sn = $gelenIlgigliAlim;";
	
	
	$ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo "Hata323: " .  pg_last_error($db);
        exit();
    }
	
	while($row = pg_fetch_row($ret)){
		
		$donenDeger = $row[0];
		$tasitPlakasi = $row[1];
		$ilgiliAlimAciklama = $row[2];
		
	}	
	
	return $donenDeger;
	
}




//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */



    
$gelenTalepSn = $_POST['talepSn'];


//Firebird Ana bağlantısı...
try {
	
	$dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
	
}catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        //VT sıfırlanıyor...
        //$dbh = null;
        //$ret = null;
        die();
}	


//Talep kaydına ulaşılıyor.
$sorguCumle = "SELECT dogrudan, 
                      kullanici, 
                      nakit_odenen,
                      dogrudan_odenen_cari_kod,
                      sube, 
                      fatura_durumu,
                      acik_odenen,
                      kk_odenen,
                      kk_odenen_kk_kod,
                      bankadan_odenen,
                      hizmet_kod,  /*10*/
                      konu,
                      yaratma_zaman,
                      stok_kod,
                      odenen_banka_kod,
					  dogrudan_miktar, /*15*/
					  dogrudan_birim, /*16*/
					  dogrudan_ilgili_alim, /*17*/
					  borclanacak_cari_kod, /*18*/
					  dogrudan_fatura_no /*19*/
				  FROM talep 
				  WHERE sn= $gelenTalepSn 
					AND bl_kodu=0;";

                        //9 

$ret = pg_query($db, $sorguCumle);

if(!$ret){    
    echo pg_last_error($db);
    exit;
}

if(pg_num_rows($ret)<1){
    echo "Okk";
    exit;
}



while($row = pg_fetch_row($ret)){

    // Bu kısım Doğrudan Talep için kullanıldı.
    $dogrudanMi = $row[0];
    $talebiUzerineAlan = $row[1];
    $nakitOdenenTutar = $row[2];
    $odemeYapilanCariKod = $row[3];
    $talepSube = $row[4];
    $faturaDurumu = $row[5];
     //------
    $acikOdenen = $row[6];  
    //------
    $subeHarcamaKasa = subeHarcamaKasaVer($talepSube);
    $subeKodu = subeKoduVer($talepSube);
	$depoKodu = subeDepoVer($subeKodu);
    //------
    $kkOdenenTutar = $row[7];
    $kkKod = $row[8];

    $bankadanOdenen = $row[9];
    $gelenHizmetKod  = $row[10];
    $gelenKonu  = $row[11];

    $islemTarihi = date('d.m.Y', strtotime($row[12])); //$row[12]; //date('d.m.Y') 

    $gelenStokKod  = $row[13];
    $gelenBankaKod  = $row[14];
	
	$gelenMiktar  = $row[15];
	$gelenBirim  = $row[16];
	
	
	$gelenDorudanIlgiliAlim  = $row[17];  //131 SN yani Depo seçilmiş mi kriteri
	
	$gelenBorclanacakCariKod  = $row[18];
	$gelenDogrudanFaturaNo  = $row[19];
	
	
	
	
	
	if($row[17]!="" && $row[17]!= 131 ){ //131 numara "depo transferi". Bu değilse plakası var mı diye bakılıyor. 
	
		$tasitPlakasi = ""; //Aşağıdaki fonksiyon tarafından belirleniyor.	
		$ilgiliAlimAciklama = ""; //Aşağıdaki fonksiyon tarafından belirleniyor.	
		$tasitSn = 0;
		$tasitSn = tasitSnVer($row[17]);
		
	}
	
	

}


$muhasebeciMi = kullanicidanBilgiVer('muhasebeci', $talebiUzerineAlan);

//Şayet muhesebeci değilse ve iş avansı kodu yoksa işleme devam edemez!

if($muhasebeciMi=='f'){

    $gelenIsAvansBlKodu = kullanicidanBilgiVer('is_avans_bl_kodu', $talebiUzerineAlan);

    if($gelenIsAvansBlKodu==0){
        ob_clean();
        echo "Talep eden kişi muhasebeci değildir! İş avansı kodu yoktur!";
        exit;
    }



}







    $toplamFaturaTutar = $nakitOdenenTutar + $acikOdenen + $kkOdenenTutar + $bankadanOdenen;  
	$faturaDetayBirimTutar = $toplamFaturaTutar / $gelenMiktar;

   

    //Öncelikla Akınsoft bağlantı kodu alınıyor.
    $baglantiKod = akinsoftBaglantiKod();

    

    $anaSorgu = "http://************:3056/getdata.html?";

    $hataVar = 'f';



    //echo "Muhasebeci : $muhasebeciMi";
    //exit;

    

    if($dogrudanMi=='t'){//Doğrudan satınalma ise ve Nakit Ödeme ise  2022 09 06 Banka da eklendi.    

        

            //XML oluşturuluyor...
            $HTMLSorgu = "tpwd=". $baglantiKod."&command=postxml_fatura&sirketKodu=".$aktifSirket."&calismaYili=". $aktifYil ."&fieldList=&xmlValue=";
    
            $xmlDosya = '<?xml version="1.0" encoding="UTF-8"?>';	
            $xmlDosya = $xmlDosya . "<WFT><AYAR>";
            $xmlDosya = $xmlDosya . "<TRSVER><![CDATA[ASWFT1.02.03]]></TRSVER>";
            $xmlDosya = $xmlDosya . "<DATABASETYPE><![CDATA[0]]></DATABASETYPE>";
            $xmlDosya = $xmlDosya . "<SERVERNAME><![CDATA[************]]></SERVERNAME>";
            $xmlDosya = $xmlDosya . "<DBFILENAME><![CDATA[".$aktifYolDB."]]></DBFILENAME>";
            $xmlDosya = $xmlDosya . "<PERSUSER><![CDATA[SYSDBA]]></PERSUSER>";
            $xmlDosya = $xmlDosya . "<SUBE_KODU><![CDATA[$subeKodu]]></SUBE_KODU>";
            $xmlDosya = $xmlDosya . "<STOKBILGIAKTAROZELDRM><![CDATA[0]]></STOKBILGIAKTAROZELDRM>";
            $xmlDosya = $xmlDosya . "</AYAR><FATURA>";
    
            //------------------------------------------------------------------
    
            $xmlDosya = $xmlDosya . "<FATURA_NO><![CDATA[WEB-DGRDN-$gelenTalepSn]]></FATURA_NO>";
            $xmlDosya = $xmlDosya . "<FATURA_DURUMU><![CDATA[5]]></FATURA_DURUMU>"; //Alış faturası
            $xmlDosya = $xmlDosya . "<BLCRKODU><![CDATA[".cariBLKoduVer($odemeYapilanCariKod)."]]></BLCRKODU>";
            $xmlDosya = $xmlDosya . "<CARIKODU><![CDATA[$odemeYapilanCariKod]]></CARIKODU>";
            /*
            $xmlDosya = $xmlDosya . "<TICARI_UNVANI><![CDATA[HURDA ALICILAR]]></TICARI_UNVANI>";
            $xmlDosya = $xmlDosya . "<ADI_SOYADI><![CDATA[HURDA ALICILAR]]></ADI_SOYADI>";
            $xmlDosya = $xmlDosya . "<VERGI_NO><![CDATA[]]></VERGI_NO>";
            $xmlDosya = $xmlDosya . "<VERGI_DAIRESI><![CDATA[]]></VERGI_DAIRESI>";
            $xmlDosya = $xmlDosya . "<TEL1><![CDATA[]]></TEL1>";
            $xmlDosya = $xmlDosya . "<FAKS><![CDATA[]]></FAKS>";
            $xmlDosya = $xmlDosya . "<ADRESI><![CDATA[]]></ADRESI>";
            $xmlDosya = $xmlDosya . "<ILCESI><![CDATA[]]></ILCESI>";
            $xmlDosya = $xmlDosya . "<ILI><![CDATA[ADANA]]></ILI>";
            */
            $xmlDosya = $xmlDosya . "<KUL_STOK_FIYATI><![CDATA[1]]></KUL_STOK_FIYATI>";
            $xmlDosya = $xmlDosya . "<TARIHI><![CDATA[". $islemTarihi. "]]></TARIHI>"; //Şenay hanımın önerisi ile yaratma zamanı yazıldı. önceden date (d.m.Y) idi
            $xmlDosya = $xmlDosya . "<VADESI><![CDATA[". $islemTarihi . "]]></VADESI>";
            $xmlDosya = $xmlDosya . "<KDV_DURUMU><![CDATA[0]]></KDV_DURUMU>";
    
            if($faturaDurumu =='Fiktif'){
                $xmlDosya = $xmlDosya . "<GRUBU><![CDATA[FİKTİF]]></GRUBU>";
                $xmlDosya = $xmlDosya . "<KDV_ORANI><![CDATA[0]]></KDV_ORANI>";
                $xmlDosya = $xmlDosya . "<TOPLAM_KDV_KPB><![CDATA[0]]></TOPLAM_KDV_KPB>";	
            }else {
                $xmlDosya = $xmlDosya . "<GRUBU><![CDATA[REEL]]></GRUBU>";   
                $xmlDosya = $xmlDosya . "<KDV_ORANI><![CDATA[18]]></KDV_ORANI>";
                $xmlDosya = $xmlDosya . "<TOPLAM_KDV_KPB><![CDATA[".str_replace(".", ",", $toplamFaturaTutar)."]]></TOPLAM_KDV_KPB>";         
            } 
    
            $xmlDosya = $xmlDosya . "<TOPLAM_GENEL_KPB><![CDATA[".str_replace(".", ",", $toplamFaturaTutar)."]]></TOPLAM_GENEL_KPB>";	
            $xmlDosya = $xmlDosya . "<OTV_KULLAN><![CDATA[0]]></OTV_KULLAN>";
            $xmlDosya = $xmlDosya . "<ISK_KUL_CARI><![CDATA[0]]></ISK_KUL_CARI>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_CARI><![CDATA[0]]></ISK_ORAN_CARI>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_CARI><![CDATA[0]]></ISK_TUTAR_CARI>";
            $xmlDosya = $xmlDosya . "<ISK_KUL_1><![CDATA[0]]></ISK_KUL_1>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_1><![CDATA[0]]></ISK_ORAN_1>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_1><![CDATA[0]]></ISK_TUTAR_1>";
            $xmlDosya = $xmlDosya . "<ISK_KUL_2><![CDATA[0]]></ISK_KUL_2>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_2><![CDATA[0]]></ISK_ORAN_2>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_2><![CDATA[0]]></ISK_TUTAR_2>";
            $xmlDosya = $xmlDosya . "<ISK_KUL_3><![CDATA[0]]></ISK_KUL_3>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_3><![CDATA[0]]></ISK_ORAN_3>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_3><![CDATA[0]]></ISK_TUTAR_3>";
            $xmlDosya = $xmlDosya . "<ISK_KUL_STOK><![CDATA[0]]></ISK_KUL_STOK>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_STOK><![CDATA[0]]></ISK_TUTAR_STOK>";
            $xmlDosya = $xmlDosya . "<ISK_KUL_OZEL><![CDATA[0]]></ISK_KUL_OZEL>";
            $xmlDosya = $xmlDosya . "<OZEL_KODU><![CDATA[WEB]]></OZEL_KODU>";
            $xmlDosya = $xmlDosya . "<ISK_KUL_ALT><![CDATA[0]]></ISK_KUL_ALT>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_ALT><![CDATA[0]]></ISK_ORAN_ALT>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_ALT1><![CDATA[0]]></ISK_TUTAR_ALT1>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_ALT2><![CDATA[0]]></ISK_TUTAR_ALT2>";
            $xmlDosya = $xmlDosya . "<CARIHRK_ISLE><![CDATA[1]]></CARIHRK_ISLE>";// Cari haraket kaydı girilecek.
            $xmlDosya = $xmlDosya . "<STOKHRK_ISLE><![CDATA[1]]></STOKHRK_ISLE>";// Stok haraketi olacak!!!
            $xmlDosya = $xmlDosya . "<DOVIZ_KULLAN><![CDATA[0]]></DOVIZ_KULLAN>";
            $xmlDosya = $xmlDosya . "<KPBDVZ_CARI><![CDATA[0]]></KPBDVZ_CARI>";
            $xmlDosya = $xmlDosya . "<DVZ_HSISLE_STOK><![CDATA[0]]></DVZ_HSISLE_STOK>";
            $xmlDosya = $xmlDosya . "<DVZ_HSISLE_CARI><![CDATA[0]]></DVZ_HSISLE_CARI>";            
            $xmlDosya = $xmlDosya . "<TOPLAM_ALT_KPB><![CDATA[".str_replace(".", ",", $toplamFaturaTutar)."]]></TOPLAM_ALT_KPB>";
            $xmlDosya = $xmlDosya . "<TOPLAM_ISK_KPB><![CDATA[0]]></TOPLAM_ISK_KPB>";
            $xmlDosya = $xmlDosya . "<TOPLAM_ARA_KPB><![CDATA[0]]></TOPLAM_ARA_KPB>";
            $xmlDosya = $xmlDosya . "<YUVARLAMA_KPB><![CDATA[0]]></YUVARLAMA_KPB>";
            $xmlDosya = $xmlDosya . "<FATURA_KESILDI><![CDATA[1]]></FATURA_KESILDI>";
            $xmlDosya = $xmlDosya . "<KAYDEDEN><![CDATA[SYSDBA]]></KAYDEDEN>";
            $xmlDosya = $xmlDosya . "<VADE_DURUMU><![CDATA[0]]></VADE_DURUMU>";
            $xmlDosya = $xmlDosya . "<ORTALAMA_VADE><![CDATA[0]]></ORTALAMA_VADE>";
            $xmlDosya = $xmlDosya . "<IPTAL><![CDATA[0]]></IPTAL>";
            $xmlDosya = $xmlDosya . "<SILINDI><![CDATA[0]]></SILINDI>";
            $xmlDosya = $xmlDosya . "<EFATURA_KULLAN><![CDATA[0]]></EFATURA_KULLAN>";
            $xmlDosya = $xmlDosya . "<EFATURA_DURUM><![CDATA[0]]></EFATURA_DURUM>";
			
			//Doğrudanda verilen fatura no, Ek bilgi-1 alanına yazılacak. 2025 03 18
			
			if($gelenDogrudanFaturaNo!="" && $gelenDogrudanFaturaNo!="-1"){
				
				$xmlDosya = $xmlDosya . "<EKBILGI_1><![CDATA[Kaynak Fatura No:".$gelenDogrudanFaturaNo."]]></EKBILGI_1>";
				
			}else{
				$xmlDosya = $xmlDosya . "<EKBILGI_1><![CDATA[]]></EKBILGI_1>";				
			}
			
            
			
			
			
            $xmlDosya = $xmlDosya . "<EKBILGI_2><![CDATA[$gelenTalepSn numaralı talep için yapılan Doğrudan harcama ile otomatik olarak oluşturulmuştur.]]></EKBILGI_2>";
            $xmlDosya = $xmlDosya . "<ACIKLAMA><![CDATA[$gelenKonu]]></ACIKLAMA>";            
            $xmlDosya = $xmlDosya . "<PAZ_PERS_BLKODU><![CDATA[0]]></PAZ_PERS_BLKODU>";
            //$xmlDosya = $xmlDosya . "<PAZ_PERSONEL><![CDATA[]]></PAZ_PERSONEL>";
            //$xmlDosya = $xmlDosya . "<PAZ_URUN_ORANI><![CDATA[0]]></PAZ_URUN_ORANI>";
            //$xmlDosya = $xmlDosya . "<PAZ_URUN_TUTARI><![CDATA[0]]></PAZ_URUN_TUTARI>";
            $xmlDosya = $xmlDosya . "</FATURA><FATURAHAREKET>";
    
    
            //Stok kaydı için.
            $xmlDosya = $xmlDosya . "<HAREKET>";



            if($gelenHizmetKod!=0){ // Demek ki hizmet kartı girilmiş.

                //Hizmet kartı oarak açılacak.
    
                $xmlDosya = $xmlDosya . "<BLSTKODU><![CDATA[0]]></BLSTKODU>";
                $xmlDosya = $xmlDosya . "<STOKKODU><![CDATA[ ]]></STOKKODU>";
                $xmlDosya = $xmlDosya . "<BLHZMKODU><![CDATA[$gelenHizmetKod]]></BLHZMKODU>"; //Hizmet Kartı
    
                $hizmetKartAd = hizmetKartAdVer($gelenHizmetKod);
    
                $xmlDosya = $xmlDosya . "<STOK_ADI><![CDATA[$hizmetKartAd]]></STOK_ADI>";
    
                //BLHZMKODU

            }elseif($gelenStokKod!="") { //Demek ki stok kodu girilmiş.
                //Stok kartı oarak açılacak.
                $stokBlKodu = 0;
                $stokAdi = "";

                stokKartBilgiVer($gelenStokKod);
    
                $xmlDosya = $xmlDosya . "<BLSTKODU><![CDATA[$stokBlKodu]]></BLSTKODU>";
                $xmlDosya = $xmlDosya . "<STOKKODU><![CDATA[$gelenStokKod]]></STOKKODU>";
                //$xmlDosya = $xmlDosya . "<BLHZMKODU><![CDATA[$gelenHizmetKod]]></BLHZMKODU>"; //Hizmet Kartı
    
                //$hizmetKartAd = hizmetKartAdVer($gelenHizmetKod);
    
                $xmlDosya = $xmlDosya . "<STOK_ADI><![CDATA[$stokAdi]]></STOK_ADI>";
    
                //BLHZMKODU
            }



            $xmlDosya = $xmlDosya . "<BIRIMI><![CDATA[$gelenBirim]]></BIRIMI>";        
            $xmlDosya = $xmlDosya . "<MIKTARI><![CDATA[".str_replace(".", ",",$gelenMiktar)."]]></MIKTARI>";
            $xmlDosya = $xmlDosya . "<MIKTARI_2><![CDATA[".str_replace(".", ",",$gelenMiktar)."]]></MIKTARI_2>";
			
			
			
			
			
			
            $xmlDosya = $xmlDosya . "<KPB_FIYATI><![CDATA[".str_replace(".", ",",$faturaDetayBirimTutar)."]]></KPB_FIYATI>";
            $xmlDosya = $xmlDosya . "<KPBDVZ><![CDATA[0]]></KPBDVZ>";
            $xmlDosya = $xmlDosya . "<BIRIM_CARPANI><![CDATA[1]]></BIRIM_CARPANI>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_CARI><![CDATA[0]]></ISK_ORAN_CARI>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_CARI><![CDATA[0]]></ISK_TUTAR_CARI>";
            $xmlDosya = $xmlDosya . "<ISK_SNTUTAR_CARI><![CDATA[0]]></ISK_SNTUTAR_CARI>";
    
    
            if($faturaDurumu=="Fiktif"){
                $xmlDosya = $xmlDosya . "<KDV_ORANI><![CDATA[0]]></KDV_ORANI>";
                $xmlDosya = $xmlDosya . "<KDV_DURUMU><![CDATA[0]]></KDV_DURUMU>";
            }else {
                $xmlDosya = $xmlDosya . "<KDV_ORANI><![CDATA[18]]></KDV_ORANI>";
                $xmlDosya = $xmlDosya . "<KDV_DURUMU><![CDATA[1]]></KDV_DURUMU>";
            }
    
    
            $xmlDosya = $xmlDosya . "<ISK_OZEL><![CDATA[0]]></ISK_OZEL>";
            $xmlDosya = $xmlDosya . "<DEPO_ADI><![CDATA[$depoKodu]]></DEPO_ADI>";			
            $xmlDosya = $xmlDosya . "<BARKODU><![CDATA[]]></BARKODU>";
            $xmlDosya = $xmlDosya . "<MUH_KODU_GENEL><![CDATA[]]></MUH_KODU_GENEL>";
            $xmlDosya = $xmlDosya . "<OZEL_KODU><![CDATA[WEB]]></OZEL_KODU>";
            $xmlDosya = $xmlDosya . "<EKBILGI_1><![CDATA[$gelenKonu]]></EKBILGI_1>";
            $xmlDosya = $xmlDosya . "<EKBILGI_3><![CDATA[$gelenTalepSn]]></EKBILGI_3>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_2><![CDATA[0]]></ISK_ORAN_2>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_2><![CDATA[0]]></ISK_TUTAR_2>";
            $xmlDosya = $xmlDosya . "<ISK_SNTUTAR_2><![CDATA[0]]></ISK_SNTUTAR_2>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_3><![CDATA[0]]></ISK_ORAN_3>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_3><![CDATA[0]]></ISK_TUTAR_3>";
            $xmlDosya = $xmlDosya . "<ISK_SNTUTAR_3><![CDATA[0]]></ISK_SNTUTAR_3>";
            $xmlDosya = $xmlDosya . "<ISK_ORAN_STOK><![CDATA[0]]></ISK_ORAN_STOK>";
            $xmlDosya = $xmlDosya . "<ISK_TUTAR_STOK><![CDATA[0]]></ISK_TUTAR_STOK>";
            $xmlDosya = $xmlDosya . "<PAZ_PERS_BLKODU><![CDATA[]]></PAZ_PERS_BLKODU>";
            $xmlDosya = $xmlDosya . "<PAZ_PERSONEL><![CDATA[]]></PAZ_PERSONEL>";
            $xmlDosya = $xmlDosya . "<PAZ_URUN_ORANI><![CDATA[0]]></PAZ_URUN_ORANI>";
            $xmlDosya = $xmlDosya . "<PAZ_URUN_TUTARI><![CDATA[0]]></PAZ_URUN_TUTARI>";
            $xmlDosya = $xmlDosya . "<PAZ_ISC_ORANI><![CDATA[0]]></PAZ_ISC_ORANI>";
            $xmlDosya = $xmlDosya . "<PAZ_ISC_TUTARI><![CDATA[0]]></PAZ_ISC_TUTARI>";
            $xmlDosya = $xmlDosya . "</HAREKET>";
            $xmlDosya = $xmlDosya . "</FATURAHAREKET>";
            $xmlDosya = $xmlDosya . "<KAPALIFATURA>";

                if($nakitOdenenTutar>0){ //Nakit ödeme kaydı

                    $xmlDosya = $xmlDosya . "<HAREKET>";
                    $xmlDosya = $xmlDosya . "<ISLEM_TURU><![CDATA[3]]></ISLEM_TURU>";
            
                    $xmlDosya = $xmlDosya . "<KASA_ADI><![CDATA[$subeHarcamaKasa]]></KASA_ADI>";
            
                    $xmlDosya = $xmlDosya . "<KPB_BTUT><![CDATA[".str_replace(".", ",", $nakitOdenenTutar)."]]></KPB_BTUT>";
                    $xmlDosya = $xmlDosya . "<KPBDVZ><![CDATA[0]]></KPBDVZ>";
                    $xmlDosya = $xmlDosya . "<BANKA_ADI><![CDATA[]]></BANKA_ADI>";
                    $xmlDosya = $xmlDosya . "<POS_DETAY><![CDATA[]]></POS_DETAY>";
                    $xmlDosya = $xmlDosya . "<KKARTI_DETAY><![CDATA[]]></KKARTI_DETAY>";                    
                    $xmlDosya = $xmlDosya . "</HAREKET>";
                }

                if($kkOdenenTutar>0){ //KK ödemesi olduğunda

                    $dilimler = explode("|", $kkKod);
                    $kkTanim1 = $dilimler[0];
                    $kkBlKod1 = $dilimler[1];

                    $xmlDosya = $xmlDosya . "<HAREKET>";
                    $xmlDosya = $xmlDosya . "<ISLEM_TURU><![CDATA[5]]></ISLEM_TURU>";
                    $xmlDosya = $xmlDosya . "<KASA_ADI><![CDATA[]]></KASA_ADI>";
                    $xmlDosya = $xmlDosya . "<KPB_BTUT><![CDATA[".str_replace(".", ",", $kkOdenenTutar)."]]></KPB_BTUT>";
                    $xmlDosya = $xmlDosya . "<DOVIZ_KULLAN><![CDATA[0]]></DOVIZ_KULLAN>";
                    $xmlDosya = $xmlDosya . "<KPBDVZ><![CDATA[1]]></KPBDVZ>";
                    $xmlDosya = $xmlDosya . "<BLBNHSKODU><![CDATA[$kkBlKod1]]></BLBNHSKODU>";
                    $xmlDosya = $xmlDosya . "<POS_DETAY><![CDATA[]]></POS_DETAY>";
                    $xmlDosya = $xmlDosya . "<KKARTI_DETAY><![CDATA[$kkTanim1]]></KKARTI_DETAY>";
                    $xmlDosya = $xmlDosya . "</HAREKET>";

                }

                if($bankadanOdenen>0){

                    $dilimler = explode("#", $gelenBankaKod);
                    $bankaAdi = $dilimler[0];
                    $bankaHesapKodu = $dilimler[1];

                    $xmlDosya = $xmlDosya . "<HAREKET>";
                    $xmlDosya = $xmlDosya . "<ISLEM_TURU><![CDATA[4]]></ISLEM_TURU>"; //Dekont.
                    $xmlDosya = $xmlDosya . "<KASA_ADI><![CDATA[]]></KASA_ADI>";
                    $xmlDosya = $xmlDosya . "<DOVIZ_KULLAN><![CDATA[0]]></DOVIZ_KULLAN>";
                    $xmlDosya = $xmlDosya . "<KPBDVZ><![CDATA[1]]></KPBDVZ>";
                    $xmlDosya = $xmlDosya . "<BANKA_ADI><![CDATA[$bankaAdi]]></BANKA_ADI>";
                    $xmlDosya = $xmlDosya . "<BLBNHSKODU><![CDATA[$bankaHesapKodu]]></BLBNHSKODU>";
                    $xmlDosya = $xmlDosya . "<KPB_BTUT><![CDATA[".str_replace(".", ",", $bankadanOdenen)."]]></KPB_BTUT>";
                    $xmlDosya = $xmlDosya . "<GM_ENTEGRASYON><![CDATA[1]]></GM_ENTEGRASYON>";
                    $xmlDosya = $xmlDosya . "</HAREKET>";

                }




            $xmlDosya = $xmlDosya . "</KAPALIFATURA>";
            $xmlDosya = $xmlDosya . "</WFT>";
    
    
            //******************************** */
    
            //echo $xmlDosya;
    
             //echo '<div class="container">';
             //    echo '<textarea class="text" readonly  >'.$xmlDosya.'</textarea>';
             //echo '</div>';

            //exit();
    
            // echo "XML Dosya yazmalı!";
    
            //Burada alanlar temizleniyor.
            $xmlDosya = str_replace("\n", " ", $xmlDosya);
            $xmlDosya = str_replace("\r", " ", $xmlDosya);
            $xmlDosya = trim($xmlDosya);
            
            $xml_result = str_replace("&", "|*", $xmlDosya);
            
            //SDK'ya gönderiliyor...
            $XMLSorgu = $HTMLSorgu . $xml_result;
            $sifreliSorgu = base64_encode($XMLSorgu);
            $AkinsoftCevap = trim(file_get_contents($anaSorgu . $sifreliSorgu));
            $cevap = base64_decode($AkinsoftCevap);
    
            //Gelen cevap BL Kodu: 
            // XML_POST_OK^MBLKODU=4249,WEB-DGRDN-0
    
            $konum = strpos($cevap, "XML_POST_OK");
    
            if ($konum === false){ //Demek ki Akınsoft içinde kayıt oluşmadı!
                echo "Fatura Akınsoft'a yazılamadı! Ancak portalda kayıt oluştu. Kontrolünü yapın!\n";
                echo $cevap; 
                $hataVar = 't';
                exit;
            }
    
            $dilimler = explode("=", $cevap);
    
            $bilgi = explode(",", $dilimler[1]);

            $faturaBlKodu = $bilgi[0];
    
            //echo "Fatura BL Kodu: ".$bilgi[0]. "\nFatura No: ".$bilgi[1]."\nAkınsoft'da fatura oluşturulmuştur...<br>";
            $mesaj = $gelenTalepSn . " Numaralı doğrudan harcama ile ilgili Akınsoft faturası oluşturuldu. Fatura BL Kodu: ".$faturaBlKodu. "\nFatura No: ".$bilgi[1];
    
            logYaz($mesaj);
    

            epostaGonder("<EMAIL>", ['<EMAIL>'], "Doğrudan Talep Faturası oluşturuldu!", $mesaj, 0);       
			
			//Buradan sonra stok kodu yakıt ise doğrudan araç üzerine yakıt çıkışı yapılacak.
			
			//$yakitStokKodlari = ['MGS0097', 'MGS0096', 'MGS0095', 'MGS0098']; //Dizel, benzin, lpg, Oto Elektrik
			
			//if($tasitSn<>0 && in_array($gelenStokKod, $yakitStokKodlari)) // önceki hali buydu 2023 10 23
			
			if($gelenDorudanIlgiliAlim!=131 && $gelenStokKod!=""){ // İlgili alım kodu Depo değilde ve stok kodu boş değilse yani stok kodu ile yapılan bir alım ise.
				
				
				
				
				//Araca yakıt çıkış hareketi yapılacak.  iptal
				// Ancak sonradan depo seçilmeyen tüm stok kartlarının doğrudan çıkış yapılması daha uygun olur
				// denilerek depo seçilmeyen tüm stok kartlanına (Hizmet değil!) çıkış yapılmasına karar verildi. 2023 10 23
				//*************************************
				
				
				/*
				$stokBlKodu = 0;
                $stokAdi = "";
				*/
				
				$anaSorgu = "http://************:3056/getdata.html?";	
				
    
				$HTMLSorgu = "tpwd=". $baglantiKod."&command=postxml_stokhrk&sirketKodu=".$aktifSirket."&calismaYili=". $aktifYil ."&fieldList=&xmlValue=";
				
				//XML dosya burada başlıyor.
				$xmlDosya = '<?xml version="1.0" encoding="UTF-8"?>';
				$xmlDosya = $xmlDosya . "<WSH><AYAR>";    
				$xmlDosya = $xmlDosya . "<TRSVER><![CDATA[ASWSH1.02.03]]></TRSVER>";
				$xmlDosya = $xmlDosya . "<DATABASETYPE><![CDATA[0]]></DATABASETYPE>";
				$xmlDosya = $xmlDosya . "<SERVERNAME><![CDATA[************]]></SERVERNAME>";
				$xmlDosya = $xmlDosya . "<DBFILENAME><![CDATA[".$aktifYolDB."]]></DBFILENAME>";
				$xmlDosya = $xmlDosya . "<PERSUSER><![CDATA[".$kullanici."]]></PERSUSER>";
				$xmlDosya = $xmlDosya . "<SUBE_KODU><![CDATA[".$subeKodu."]]></SUBE_KODU>";    
				$xmlDosya = $xmlDosya . "</AYAR>";
				$xmlDosya = $xmlDosya . "<STOKHAREKET>";    
				$xmlDosya = $xmlDosya . "<HAREKET>";
				$xmlDosya = $xmlDosya . "<BLSTKODU><![CDATA[".$stokBlKodu."]]></BLSTKODU>"; //Mazot stok kodu
				$xmlDosya = $xmlDosya . "<DEPO_ADI><![CDATA[".$depoKodu."]]></DEPO_ADI>";
				$xmlDosya = $xmlDosya . "<OZEL_KODU><![CDATA[".$tasitPlakasi."]]></OZEL_KODU>";
				$xmlDosya = $xmlDosya . "<TARIHI><![CDATA[".$islemTarihi."]]></TARIHI>";
				
				
				$xmlDosya = $xmlDosya . "<ACIKLAMA><![CDATA[$ilgiliAlimAciklama çıkış hareketi.]]></ACIKLAMA>";					
				
				
				$xmlDosya = $xmlDosya . "<EVRAK_NO><![CDATA[WEBYSHR-".$gelenTalepSn."]]></EVRAK_NO>";
				$xmlDosya = $xmlDosya . "<KPB_FIYATI><![CDATA[".str_replace(".", ",",$faturaDetayBirimTutar)."]]></KPB_FIYATI>";				
				$xmlDosya = $xmlDosya . "<MIKTAR_2><![CDATA[". str_replace('.', ',', $gelenMiktar) ."]]></MIKTAR_2>";
				$xmlDosya = $xmlDosya . "<TUTAR_TURU><![CDATA[0]]></TUTAR_TURU>"; //Stok çıkışı:0 Stok Girişi:1
				$xmlDosya = $xmlDosya . "</HAREKET>";
				$xmlDosya = $xmlDosya . "</STOKHAREKET>";    
				$xmlDosya = $xmlDosya . "</WSH>";
				
				
				//XML Akınsoft'a gönderiliyor.
				$xmlDosya = str_replace("\n", " ", $xmlDosya);
				$xmlDosya = str_replace("\r", " ", $xmlDosya);
				$xmlDosya = trim($xmlDosya);
				$xml_result = str_replace("&", "|*", $xmlDosya);

				$XMLSorgu = $HTMLSorgu . $xml_result;
				$sifreliSorgu = base64_encode($XMLSorgu);
				$AkinsoftCevap = trim(file_get_contents($anaSorgu . $sifreliSorgu));
				$cevap = base64_decode($AkinsoftCevap);
				
				
				//Hata kontrolü 

				if(strpos($cevap, "XML_POST_OK") === false){

					//echo "Word Not Found!";

					echo "Hata! $cevap $gelenTarih";
					pg_close($db);
					exit();
					
				}else{
					
					$dilimler = explode("=", $cevap);
					$blKodu = $dilimler[1]; //AkınsoftBL Kodu alındı.
					logYaz("WEBYHR-{$gelenTalepSn} numaralı doğrudan yakıt alımı, $blKodu ile $tasitPlakasi plakalı araca Akınsoft çıkışı yapılmıştır.");
					
				}
				
				
				
				
				
				
				
				
			} //Stok çıkışı sonu...
			
			

			
			

    }


    //**************************************** */
    //**************************************** */

    
    //Şimdi ise kasa hareketi oluşturuluyor.
    //Muhasebeci ise Merkez demir kasadan ==> Şube harcama kasaya transfer oluyor.
    //Normal kişi ise yani muhasebeci değil ise kişinin iş avansından şube harcama kasaya transfer oluyor.

    if($muhasebeciMi=='t' && $nakitOdenenTutar>0 ){ //Burada kişi muhasebeci. Yani Merkez demir kasadan şube harcama kasaya transfer olacak!
        //echo "Uğradıc $muhasebeciMi  x$hataVar x $nakitOdenenTutar";
        //exit;

        $HTMLSorgu = "tpwd=". $baglantiKod."&command=postxml_kasatransfer&sirketKodu=".$aktifSirket."&calismaYili=". $aktifYil ."&fieldList=&xmlValue=";
    
        $xmlDosya_3 = '<?xml version="1.0" encoding="UTF-8"?>';	
        $xmlDosya_3 = $xmlDosya_3 . "<WKH><AYAR>";
        $xmlDosya_3 = $xmlDosya_3 . "<TRSVER><![CDATA[ASWKSTRS1.02.01]]></TRSVER>";
        $xmlDosya_3 = $xmlDosya_3 . "<DATABASETYPE><![CDATA[0]]></DATABASETYPE>";
        $xmlDosya_3 = $xmlDosya_3 . "<SERVERNAME><![CDATA[************]]></SERVERNAME>";
        $xmlDosya_3 = $xmlDosya_3 . "<DBFILENAME><![CDATA[".$aktifYolDB."]]></DBFILENAME>";
        $xmlDosya_3 = $xmlDosya_3 . "<PERSUSER><![CDATA[SYSDBA]]></PERSUSER>";
        $xmlDosya_3 = $xmlDosya_3 . "<SUBE_KODU><![CDATA[$subeKodu]]></SUBE_KODU>";    
        $xmlDosya_3 = $xmlDosya_3 . "</AYAR>";
        //---------------------------------------------
        $xmlDosya_3 = $xmlDosya_3 . "<TRANSFER>";
        $xmlDosya_3 = $xmlDosya_3 . "<KAYNAK_KASA><![CDATA[MERKEZ_DMR_KASA]]></KAYNAK_KASA>";
        $xmlDosya_3 = $xmlDosya_3 . "<HEDEF_KASA><![CDATA[$subeHarcamaKasa]]></HEDEF_KASA>";
        $xmlDosya_3 = $xmlDosya_3 . "<ISLEM_TARIHI><![CDATA[". $islemTarihi . "]]></ISLEM_TARIHI>";
        $xmlDosya_3 = $xmlDosya_3 . "<OZEL_KODU><![CDATA[Transfer]]></OZEL_KODU>";
        $xmlDosya_3 = $xmlDosya_3 . "<GM_ENTEGRASYON><![CDATA[1]]></GM_ENTEGRASYON>";
        $xmlDosya_3 = $xmlDosya_3 . "<EVRAK_NO><![CDATA[WEBKSTR-$gelenTalepSn]]></EVRAK_NO>";
        $xmlDosya_3 = $xmlDosya_3 . "<ACIKLAMA><![CDATA[$gelenTalepSn Numaralı talep için Kasa Transferi]]></ACIKLAMA>";
        $xmlDosya_3 = $xmlDosya_3 . "<DOVIZ_KULLAN><![CDATA[0]]></DOVIZ_KULLAN>";
        $xmlDosya_3 = $xmlDosya_3 . "<KPB_TUTARI><![CDATA[".str_replace(".", ",", $nakitOdenenTutar)."]]></KPB_TUTARI>";
        //---------------------------------------------

        $xmlDosya_3 = $xmlDosya_3 . "</TRANSFER>";
        $xmlDosya_3 = $xmlDosya_3 . "</WKH>";

        //İkinci bölüm için XML oluşturuluyor.
        //XML Akınsoft'a gönderiliyor.
        $xmlDosya_3 = str_replace("\n", " ", $xmlDosya_3);
        $xmlDosya_3 = str_replace("\r", " ", $xmlDosya_3);
        $xmlDosya_3 = trim($xmlDosya_3);
        $xml_result_3 = str_replace("&", "|*", $xmlDosya_3);

        $XMLSorgu_3 = $HTMLSorgu . $xml_result_3;
        $sifreliSorgu_3 = base64_encode($XMLSorgu_3);
        $AkinsoftCevap_3 = trim(file_get_contents($anaSorgu . $sifreliSorgu_3));
        $cevap_3 = base64_decode($AkinsoftCevap_3);

        if(strpos($cevap_3, "XML_POST_OK") === false){

            
            $hataVar = 't';
        
            echo "Hata!  ". $cevap_3;
            pg_close($db);
            exit;
        
        }

        $mesaj = "Merkez Demir Kasadan ==> $subeHarcamaKasa Kasa transferi yapıldı.";

        logYaz($mesaj);

        epostaGonder("<EMAIL>", ['<EMAIL>'], "Doğrudan Talep Kasa Transferi oluşturuldu!", $mesaj, 0);



    }else if($muhasebeciMi=='f' && $nakitOdenenTutar>0 ) {//Burada normal satınalmacı işlemi yapılacak. YAni İş avansı hesabından Şube Harcama kasaya cari transfer yapılacak.

        //echo "Muhasebeci değil... ";

        $gelenIsAvansBlKodu = kullanicidanBilgiVer('is_avans_bl_kodu', $talebiUzerineAlan);


        if($gelenIsAvansBlKodu==0){
            echo "Satınalmacı için İş avans Kodu belirtilmemiş!";
            pg_close($db);
            exit;
        }

        $HTMLSorgu = "tpwd=". $baglantiKod."&command=postxml_carihrk&sirketKodu=".$aktifSirket."&calismaYili=". $aktifYil ."&fieldList=&xmlValue=";
    
        //XML dosya burada başlıyor.  
        $xmlDosya = "";     


        $xmlDosya = '<?xml version="1.0" encoding="UTF-8"?>';	
        $xmlDosya = $xmlDosya . "<WCH><AYAR>";
        $xmlDosya = $xmlDosya . "<TRSVER><![CDATA[ASWCH1.02.03]]></TRSVER>";
        $xmlDosya = $xmlDosya . "<DATABASETYPE><![CDATA[0]]></DATABASETYPE>";
        $xmlDosya = $xmlDosya . "<SERVERNAME><![CDATA[************]]></SERVERNAME>";
        $xmlDosya = $xmlDosya . "<DBFILENAME><![CDATA[".$aktifYolDB."]]></DBFILENAME>";
        $xmlDosya = $xmlDosya . "<PERSUSER><![CDATA[SYSDBA]]></PERSUSER>";
        $xmlDosya = $xmlDosya . "<SUBE_KODU><![CDATA[$subeKodu]]></SUBE_KODU>";    
        $xmlDosya = $xmlDosya . "</AYAR>";
        //-----------------------------------------------------------------------------

        $xmlDosya = $xmlDosya . "<CARIHAREKET>";


        $xmlDosya = $xmlDosya . "<HAREKET>";
        $xmlDosya = $xmlDosya . "<EVRAK_NO><![CDATA[WEBCHR-$gelenTalepSn]]></EVRAK_NO>";
        $xmlDosya = $xmlDosya . "<ISLEM_TURU><![CDATA[3]]></ISLEM_TURU>"; //Nakit.
        $xmlDosya = $xmlDosya . "<BLCRKODU><![CDATA[$gelenIsAvansBlKodu]]></BLCRKODU>";
        $xmlDosya = $xmlDosya . "<TARIHI><![CDATA[". $islemTarihi . "]]></TARIHI>"; //date('d.m.Y')
        $xmlDosya = $xmlDosya . "<KPBDVZ><![CDATA[0]]></KPBDVZ>";
        $xmlDosya = $xmlDosya . "<KPB_ATUT><![CDATA[".str_replace(".", ",", $nakitOdenenTutar)."]]></KPB_ATUT>";
        $xmlDosya = $xmlDosya . "<KASA_ADI><![CDATA[$subeHarcamaKasa]]></KASA_ADI>";
        $xmlDosya = $xmlDosya . "<GM_ENTEGRASYON><![CDATA[1]]></GM_ENTEGRASYON>";
        $xmlDosya = $xmlDosya . "<OZEL_KODU><![CDATA[WEB]]></OZEL_KODU>";
        $xmlDosya = $xmlDosya . "<ACIKLAMA><![CDATA[$gelenTalepSn Numaralı talep için yapılan transfer]]></ACIKLAMA>";
        $xmlDosya = $xmlDosya . "</HAREKET>";

        $xmlDosya = $xmlDosya . "</CARIHAREKET>";
        $xmlDosya = $xmlDosya . "</WCH>";

        // echo "-------------------";
        // echo $xmlDosya;
        // echo "-------------------";


        //XML dosya oluşmadıysa hata veriliyor.
        if($xmlDosya=="") {
            echo "Hata!";
            pg_close($db);
            exit;
        }

        $xmlDosya = str_replace("\n", " ", $xmlDosya);
        $xmlDosya = str_replace("\r", " ", $xmlDosya);
        $xmlDosya = trim($xmlDosya);
        $xml_result = str_replace("&", "|*", $xmlDosya);
        
        $XMLSorgu = $HTMLSorgu . $xml_result;
        $sifreliSorgu = base64_encode($XMLSorgu);
        $AkinsoftCevap = trim(file_get_contents($anaSorgu . $sifreliSorgu));
        $cevap = base64_decode($AkinsoftCevap);
        
        
        if(strpos($cevap, "XML_POST_OK") === false){
        
            //echo "Word Not Found!";
            $hataVar='t';        
            echo "Hata!!!-02  ". $cevap;
            pg_close($db);
            exit;
        
        }

        $mesaj = "Cari iş avansı hesabından ==> $subeHarcamaKasa kasasına Cari Hareket yapıldı.";

        logYaz($mesaj);

        epostaGonder("<EMAIL>", ['<EMAIL>'], "Doğrudan Talep Faturası oluşturuldu!", $mesaj, 0);
        
    }
	
	
	//Borçandırılacak vari varsa borç kaydı girilecek. Yoksa pas geçilecek.
	
	if($gelenBorclanacakCariKod=="") goto cikis;
	
	//Buradan sonra borçandırılacak cariye borç kaydı giriliyor. 
	
	$HTMLSorgu = "tpwd=". $baglantiKod."&command=postxml_carihrk&sirketKodu=".$aktifSirket."&calismaYili=". $aktifYil ."&fieldList=&xmlValue=";
    
	//XML dosya burada başlıyor.  
	$xmlDosya = "";     


	$xmlDosya = '<?xml version="1.0" encoding="UTF-8"?>';	
	$xmlDosya = $xmlDosya . "<WCH><AYAR>";
	$xmlDosya = $xmlDosya . "<TRSVER><![CDATA[ASWCH1.02.03]]></TRSVER>";
	$xmlDosya = $xmlDosya . "<DATABASETYPE><![CDATA[0]]></DATABASETYPE>";
	$xmlDosya = $xmlDosya . "<SERVERNAME><![CDATA[************]]></SERVERNAME>";
	$xmlDosya = $xmlDosya . "<DBFILENAME><![CDATA[".$aktifYolDB."]]></DBFILENAME>";
	$xmlDosya = $xmlDosya . "<PERSUSER><![CDATA[SYSDBA]]></PERSUSER>";
	$xmlDosya = $xmlDosya . "<SUBE_KODU><![CDATA[$subeKodu]]></SUBE_KODU>";    
	$xmlDosya = $xmlDosya . "</AYAR>";
	//-----------------------------------------------------------------------------

	$xmlDosya = $xmlDosya . "<CARIHAREKET>";


	$xmlDosya = $xmlDosya . "<HAREKET>";
	$xmlDosya = $xmlDosya . "<EVRAK_NO><![CDATA[WEBCHR-$gelenTalepSn-BORÇ]]></EVRAK_NO>";
	$xmlDosya = $xmlDosya . "<ISLEM_TURU><![CDATA[2]]></ISLEM_TURU>"; //Evrak.
	$xmlDosya = $xmlDosya . "<BLCRKODU><![CDATA[".cariBLKoduVer($gelenBorclanacakCariKod)."]]></BLCRKODU>";
	$xmlDosya = $xmlDosya . "<TARIHI><![CDATA[". $islemTarihi . "]]></TARIHI>"; //date('d.m.Y')
	$xmlDosya = $xmlDosya . "<KPBDVZ><![CDATA[0]]></KPBDVZ>";
	$xmlDosya = $xmlDosya . "<KPB_BTUT><![CDATA[".str_replace(".", ",", $toplamFaturaTutar)."]]></KPB_BTUT>";	//Toplam ödeme tutarı adama boç yazılacak. Sadece NAKİT değil! 2023 11 08
	$xmlDosya = $xmlDosya . "<GM_ENTEGRASYON><![CDATA[0]]></GM_ENTEGRASYON>";
	
	//Nakitmiş. Dekont olması gerekiyor. Dekont yapıldı KAsa kaldırıldı. 2024 01 29
	//$xmlDosya = $xmlDosya . "<KASA_ADI><![CDATA[$subeHarcamaKasa]]></KASA_ADI>";
	
	$xmlDosya = $xmlDosya . "<OZEL_KODU><![CDATA[WEB]]></OZEL_KODU>";
	$xmlDosya = $xmlDosya . "<ACIKLAMA><![CDATA[$gelenTalepSn Numaralı talep ile ilgili borçlandırılmıştır.]]></ACIKLAMA>";
	$xmlDosya = $xmlDosya . "</HAREKET>";

	$xmlDosya = $xmlDosya . "</CARIHAREKET>";
	$xmlDosya = $xmlDosya . "</WCH>";

	// echo "-------------------";
	// echo $xmlDosya;
	// echo "-------------------";
	
	//XML dosya oluşmadıysa hata veriliyor. Aşağıdaki kullanılabilir 2024 01 31	
	$xmlDosyaHataVarsa = ""; //$xmlDosya;
	
	
	if($xmlDosya=="") {
		echo "Hata!";
		pg_close($db);
		exit;
	}

	$xmlDosya = str_replace("\n", " ", $xmlDosya);
	$xmlDosya = str_replace("\r", " ", $xmlDosya);
	$xmlDosya = trim($xmlDosya);
	$xml_result = str_replace("&", "|*", $xmlDosya);
	
	$XMLSorgu = $HTMLSorgu . $xml_result;
	$sifreliSorgu = base64_encode($XMLSorgu);
	$AkinsoftCevap = trim(file_get_contents($anaSorgu . $sifreliSorgu));
	$cevap = base64_decode($AkinsoftCevap);
	
	
	if(strpos($cevap, "XML_POST_OK") === false){
	
		//echo "Word Not Found!";
		$hataVar='t';        
		echo "Hata-01-01!!!\n$xmlDosyaHataVarsa\n  ". $cevap;
		pg_close($db);
		exit;
	
	}

	$mesaj = "$gelenBorclanacakCariKod, $gelenTalepSn Numaralı talep ile ilgili borçlandırılmıştır.";

	logYaz($mesaj);	
	
	
	
	
	
	
	

    //İşlem tamam:)

cikis:    

    //Burada talep üzerinde işlem yapılıyor.
    $sorguCumle = "UPDATE talep SET tamamlandi = TRUE, 
                                    tamamlanma_zaman = NOW(), 
                                    dogrudan_onaylayan = '$kullanici',
                                    bl_kodu = $faturaBlKodu 
                           WHERE sn=$gelenTalepSn;";


    //Sorgu çalıştır.
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    logYaz($gelenTalepSn . " Numaralı doğrudan harcama talebi onaylandı.");


    
    ob_clean();

    $dbh = null;



    if($hataVar=='f') echo "Okk";else echo "Kayıt işleminde hata!";


?>

