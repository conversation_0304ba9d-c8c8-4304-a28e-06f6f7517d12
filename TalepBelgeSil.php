<?php
require('yetki.php');
$ekranKod = 50;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">
<title>MEG Grup Yönetim Sistemi</title>

<style style="text/css">
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }
</style>

<?php

?>


<script type="text/javascript">

function ConfirmDelete(){
    if (confirm("Kayıt Silinecektir! İşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}




</script>

</head>

<body>





<?php

//Bağlantı Yapılıyor... 
//require("PGConnect.php");





require('siteBaslik.html');
require('menuKaynak.php');
//include('blink.html');
require("mesajlasma.php");
require("logYaz.php");








//Öncelikle Bayi bilgisine ulaşılacak!


//echo $_SESSION["user"];
//echo $_SESSION["unique_ID"];
/*
$ip = $_SERVER["REMOTE_ADDR"];
$kullanici = $_SESSION["user"];
$id = $_SESSION["unique_ID"];
*/

//GelenBilgiler
$gelenDosyaAd = $_POST["dosyaAd"];
$gelenDosya = $_POST["dosya"];
$gelenTalepSn = $_POST["talepSn"];
$gelenTalepDetaySn = $_POST["talepDetaySn"];


/*
echo '<input type="hidden" name="dosyaAd" value="'.$dosyaAd.'">';
            echo '<input type="hidden" name="dosya" value="'.$dosya.'">';
*/



if($gelenDosyaAd == "") {

    exit;
}

echo "<br>";
echo '<h1 style="text-align:center;">Talep Belge Silme</h1>';


if (!unlink($gelenDosyaAd)) {  
    echo ("$gelenDosya silinemedi!");  
    echo "<br><br><br>";
    echo '<h1 style="text-align:center;">Dosya silme hatası!.</h1>';
}  
else {  
    echo ("$gelenDosya silindi."); 
    //İşlem başarılı ise mesaj verilecek.
    logYaz($kullanici . ", " .$gelenDosya . " belgesini sildi.");

    echo "<br><br><br>";
    echo '<h1 style="text-align:center;">Talep Belgesi Başarı ile Silinmiştir.</h1>'; 
}  



//İşlem başarılı ise mesaj verilecek.
logYaz($kullanici . ", " .$gelenDosya . " belgesini sildi.");






echo "<br><br><br>";

echo "</b><br><br><br>";

echo "<center>";
echo '<form action="talepListesi.php" method="post">';
echo '<input type="submit" value="Talep Listesine Dön" style="width:200px;">';
echo '</form>';

echo "</b><br><br><br>";

echo "<center>";
echo '<form action="TalepBelgeleri.php" method="post">';
echo '<input type="hidden" name="talepSn" value="'.$gelenTalepSn.'">';    
echo '<input type="hidden" name="talepDetaySn" value="'.$gelenTalepDetaySn.'">';    
echo '<input type="submit" value="Talep Belgelerine Dön" style="width:200px;">';
echo '</form>';







/*
//Detay Göster
    echo '<form action="GorevBelgeleri.php"  method="post"  class="inline">'; 
    echo '<input type="hidden" name="gorevSn" value="'.$row[6].'">';    
    echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$row[6].'"/></button>';
    //echo '<button type="submit">Detay</button>';
    echo '</form>';

*/



//En son yer...
pg_close($db);   



?>

</body>
</html>
