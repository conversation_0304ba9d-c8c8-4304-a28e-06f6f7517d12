import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Modal,
  Alert,
  ActivityIndicator,
  ScrollView,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

/**
 * Talep Onay Kartı Ana Bileşeni
 * MEG Talep Tracking System için React Native Expo uyumlu component
 */
const TalepOnayKarti = () => {
  // State tanımlamaları
  const [talepListesi, setTalepListesi] = useState([]);
  const [yukleyici, setYukleyici] = useState(true);
  const [yenileniyor, setYenileniyor] = useState(false);
  const [modalGorunur, setModalGorunur] = useState(false);
  const [secilenTalep, setSecilenTalep] = useState(null);

  // Component mount olduğunda talepleri yükle
  useEffect(() => {
    talepleriYukle();
  }, []);

  /**
   * Talepleri API'dan yükle
   */
  const talepleriYukle = async () => {
    try {
      setYukleyici(true);
      
      // TODO: Gerçek API çağrısı burada yapılacak
      // const TalepOnayKarti = require('./talepOnayKarti');
      // const onayKarti = new TalepOnayKarti();
      // const talepler = await onayKarti.getOnayBekleyenTalepler();
      
      // Simülasyon verisi
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const simulasyonData = [
        {
          sn: 1001,
          konu: "Ofis Malzemeleri Tedariki",
          detay: "Yazıcı kartuşu, A4 kağıt ve kırtasiye malzemeleri satın alınması gerekiyor.",
          talep_eden: "Ahmet Yılmaz",
          sube: "İdari İşler",
          yaratma_zaman_formatted: "26-08-2025 14:30",
          toplam_tutar_formatted: "2.500,00 ₺",
          detaylar: [
            { aciklama: "HP LaserJet Kartuş", miktar: 3, birim: "Adet" },
            { aciklama: "A4 Fotokopi Kağıdı", miktar: 10, birim: "Paket" }
          ]
        },
        {
          sn: 1002,
          konu: "Araç Bakım ve Onarım",
          detay: "Şirket aracının periyodik bakımı ve lastik değişimi yapılacak.",
          talep_eden: "Fatma Demir",
          sube: "Lojistik",
          yaratma_zaman_formatted: "26-08-2025 10:15",
          toplam_tutar_formatted: "4.200,00 ₺",
          detaylar: [
            { aciklama: "Motor Yağı Değişimi", miktar: 1, birim: "Set" },
            { aciklama: "Lastik Takımı", miktar: 4, birim: "Adet" }
          ]
        }
      ];

      setTalepListesi(simulasyonData);
      
    } catch (error) {
      console.error('Talepler yüklenirken hata:', error);
      Alert.alert('Hata', 'Talepler yüklenirken bir hata oluştu.');
    } finally {
      setYukleyici(false);
      setYenileniyor(false);
    }
  };

  /**
   * Talep onaylama işlemi
   */
  const talepOnayla = async (talepSn) => {
    Alert.alert(
      'Talep Onayı',
      `T-${talepSn} numaralı talebi onaylamak istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Onayla',
          onPress: async () => {
            try {
              // TODO: Gerçek API çağrısı
              // await onayKarti.talepOnayla(talepSn, 'kullanici_adi');
              
              const yeniListe = talepListesi.filter(t => t.sn !== talepSn);
              setTalepListesi(yeniListe);
              
              if (modalGorunur && secilenTalep?.sn === talepSn) {
                setModalGorunur(false);
              }
              
              Alert.alert('Başarılı', `T-${talepSn} numaralı talep onaylandı.`);
              
            } catch (error) {
              Alert.alert('Hata', 'Talep onaylanırken hata oluştu.');
            }
          }
        }
      ]
    );
  };

  /**
   * Talep silme işlemi
   */
  const talepSil = async (talepSn) => {
    Alert.alert(
      'Talep Silme',
      `T-${talepSn} numaralı talebi silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Gerçek API çağrısı
              // await onayKarti.talepSil(talepSn, 'kullanici_adi');
              
              const yeniListe = talepListesi.filter(t => t.sn !== talepSn);
              setTalepListesi(yeniListe);
              
              if (modalGorunur && secilenTalep?.sn === talepSn) {
                setModalGorunur(false);
              }
              
              Alert.alert('Başarılı', `T-${talepSn} numaralı talep silindi.`);
              
            } catch (error) {
              Alert.alert('Hata', 'Talep silinirken hata oluştu.');
            }
          }
        }
      ]
    );
  };

  /**
   * Talep kartı render fonksiyonu
   */
  const TalepKarti = ({ item }) => (
    <View style={styles.talepKarti}>
      <View style={styles.kartBaslik}>
        <View style={styles.talepSn}>
          <Text style={styles.talepSnText}>T-{item.sn}</Text>
        </View>
        <Text style={styles.talepTarih}>{item.yaratma_zaman_formatted}</Text>
      </View>

      <Text style={styles.talepKonu}>{item.konu}</Text>
      <Text style={styles.talepDetay} numberOfLines={2}>{item.detay}</Text>

      <View style={styles.bilgiSatirlari}>
        <View style={styles.bilgiItem}>
          <Text style={styles.bilgiLabel}>Talep Eden</Text>
          <Text style={styles.bilgiDeger}>{item.talep_eden}</Text>
        </View>
        <View style={styles.bilgiItem}>
          <Text style={styles.bilgiLabel}>Şube</Text>
          <Text style={styles.bilgiDeger}>{item.sube}</Text>
        </View>
      </View>

      <View style={styles.tutarBolumu}>
        <Text style={styles.tutarLabel}>Toplam Tutar</Text>
        <Text style={styles.tutarDeger}>{item.toplam_tutar_formatted}</Text>
      </View>

      <TouchableOpacity
        style={styles.detayButon}
        onPress={() => {
          setSecilenTalep(item);
          setModalGorunur(true);
        }}
      >
        <Text style={styles.detayButonText}>📋 Detayları Görüntüle</Text>
      </TouchableOpacity>

      <View style={styles.aksiyonButonlari}>
        <TouchableOpacity
          style={[styles.aksiyonButon, styles.onayButon]}
          onPress={() => talepOnayla(item.sn)}
        >
          <Text style={styles.aksiyonButonText}>✅ Onayla</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.aksiyonButon, styles.silButon]}
          onPress={() => talepSil(item.sn)}
        >
          <Text style={styles.aksiyonButonText}>🗑️ Sil</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Ana component render
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      
      <LinearGradient colors={['#667eea', '#764ba2']} style={styles.baslik}>
        <Text style={styles.baslikText}>🎯 MEG Talep Onay Kartları</Text>
        <Text style={styles.altBaslik}>Onay bekleyen talepleri yönetin</Text>
      </LinearGradient>

      {yukleyici ? (
        <View style={styles.yukleyiciContainer}>
          <ActivityIndicator size="large" color="#667eea" />
          <Text style={styles.yukleyiciText}>🔄 Talepler yükleniyor...</Text>
        </View>
      ) : talepListesi.length === 0 ? (
        <View style={styles.bosDurumContainer}>
          <Text style={styles.bosDurumBaslik}>📭 Onay bekleyen talep yok</Text>
          <Text style={styles.bosDurumAciklama}>Tüm talepler işlenmiş durumda.</Text>
        </View>
      ) : (
        <FlatList
          data={talepListesi}
          keyExtractor={(item) => item.sn.toString()}
          renderItem={({ item }) => <TalepKarti item={item} />}
          contentContainerStyle={styles.listeContainer}
          refreshControl={
            <RefreshControl
              refreshing={yenileniyor}
              onRefresh={() => {
                setYenileniyor(true);
                talepleriYukle();
              }}
              colors={['#667eea']}
            />
          }
        />
      )}

      {/* Detay Modal */}
      <Modal
        visible={modalGorunur}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalGorunur(false)}
      >
        <View style={styles.modalContainer}>
          <LinearGradient colors={['#667eea', '#764ba2']} style={styles.modalBaslik}>
            <Text style={styles.modalBaslikText}>📋 Talep Detayları</Text>
            <TouchableOpacity onPress={() => setModalGorunur(false)}>
              <Text style={styles.kapatButon}>✕</Text>
            </TouchableOpacity>
          </LinearGradient>

          <ScrollView style={styles.modalIcerik}>
            {secilenTalep && (
              <>
                <Text style={styles.modalKonu}>{secilenTalep.konu}</Text>
                <Text style={styles.modalDetay}>{secilenTalep.detay}</Text>
                
                <View style={styles.modalBilgi}>
                  <Text>👤 {secilenTalep.talep_eden}</Text>
                  <Text>🏢 {secilenTalep.sube}</Text>
                  <Text>💰 {secilenTalep.toplam_tutar_formatted}</Text>
                </View>

                <Text style={styles.detayBaslik}>📝 Detaylar:</Text>
                {secilenTalep.detaylar.map((detay, index) => (
                  <View key={index} style={styles.detayItem}>
                    <Text>{detay.aciklama} - {detay.miktar} {detay.birim}</Text>
                  </View>
                ))}

                <View style={styles.modalAksiyonlari}>
                  <TouchableOpacity
                    style={[styles.aksiyonButon, styles.onayButon]}
                    onPress={() => talepOnayla(secilenTalep.sn)}
                  >
                    <Text style={styles.aksiyonButonText}>✅ Onayla</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.aksiyonButon, styles.silButon]}
                    onPress={() => talepSil(secilenTalep.sn)}
                  >
                    <Text style={styles.aksiyonButonText}>🗑️ Sil</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

// Stil tanımlamaları
const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f5f5f5' },
  baslik: { paddingHorizontal: 20, paddingVertical: 30, paddingTop: 50, alignItems: 'center' },
  baslikText: { fontSize: 24, fontWeight: 'bold', color: '#FFFFFF', marginBottom: 8 },
  altBaslik: { fontSize: 16, color: 'rgba(255, 255, 255, 0.9)' },
  listeContainer: { padding: 20 },
  talepKarti: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    borderLeftWidth: 5,
    borderLeftColor: '#667eea',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  kartBaslik: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 15 },
  talepSn: { backgroundColor: '#667eea', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 20 },
  talepSnText: { color: '#FFFFFF', fontSize: 12, fontWeight: 'bold' },
  talepTarih: { fontSize: 12, color: '#666' },
  talepKonu: { fontSize: 18, fontWeight: '600', color: '#333', marginBottom: 10 },
  talepDetay: { fontSize: 14, color: '#666', marginBottom: 15 },
  bilgiSatirlari: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 15 },
  bilgiItem: { flex: 1, marginHorizontal: 5 },
  bilgiLabel: { fontSize: 12, color: '#666', marginBottom: 3 },
  bilgiDeger: { fontSize: 14, fontWeight: '500', color: '#333' },
  tutarBolumu: { backgroundColor: '#f8f9fa', padding: 15, borderRadius: 10, alignItems: 'center', marginBottom: 15 },
  tutarLabel: { fontSize: 12, color: '#666', marginBottom: 5 },
  tutarDeger: { fontSize: 20, fontWeight: 'bold', color: '#28a745' },
  detayButon: { backgroundColor: '#17a2b8', padding: 12, borderRadius: 8, alignItems: 'center', marginBottom: 10 },
  detayButonText: { color: '#FFFFFF', fontSize: 14, fontWeight: '600' },
  aksiyonButonlari: { flexDirection: 'row', gap: 10 },
  aksiyonButon: { flex: 1, padding: 12, borderRadius: 8, alignItems: 'center' },
  onayButon: { backgroundColor: '#28a745' },
  silButon: { backgroundColor: '#dc3545' },
  aksiyonButonText: { color: '#FFFFFF', fontSize: 14, fontWeight: '600' },
  yukleyiciContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 40 },
  yukleyiciText: { marginTop: 16, fontSize: 16, color: '#666' },
  bosDurumContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 40 },
  bosDurumBaslik: { fontSize: 18, fontWeight: '600', color: '#333', marginBottom: 8 },
  bosDurumAciklama: { fontSize: 14, color: '#666', textAlign: 'center' },
  // Modal Stilleri
  modalContainer: { flex: 1, backgroundColor: '#FFFFFF' },
  modalBaslik: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 20, paddingTop: 50 },
  modalBaslikText: { fontSize: 20, fontWeight: '600', color: '#FFFFFF' },
  kapatButon: { fontSize: 24, color: '#FFFFFF', fontWeight: 'bold' },
  modalIcerik: { flex: 1, padding: 20 },
  modalKonu: { fontSize: 18, fontWeight: 'bold', marginBottom: 10 },
  modalDetay: { fontSize: 14, color: '#666', marginBottom: 20 },
  modalBilgi: { marginBottom: 20 },
  detayBaslik: { fontSize: 16, fontWeight: 'bold', marginBottom: 10 },
  detayItem: { padding: 10, backgroundColor: '#f8f9fa', marginBottom: 5, borderRadius: 5 },
  modalAksiyonlari: { flexDirection: 'row', gap: 10, marginTop: 20, marginBottom: 30 },
});

export default TalepOnayKarti;