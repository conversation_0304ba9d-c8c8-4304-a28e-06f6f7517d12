<?php
require('yetki.php');
$ekranKod = 61;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<style style="text/css">
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }
</style>

<?php

?>


<script type="text/javascript">

function ConfirmDelete(){
    if (confirm("Kayıt Silinecektir! İşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}




function sayfaYuklendi(){
	
	document.getElementById("makbuzYazdir").submit();
	
}




</script>

</head>

<body onload="sayfaYuklendi()">





<?php

//Bağlantı Yapılıyor... 
//require("PGConnect.php");




function sepetNoVer(){	
	
	global $db;
	
	//debug_to_console("Kademe 1");

	if($_SESSION["sepetNo"]!=0) return $_SESSION["sepetNo"];
	
	$sorgu="SELECT sn FROM sepet WHERE kullanici='". $_SESSION["user"] . "' AND aktif = true;";
	
	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Sepete ekleme yapılamadı!<br>Önceki Sepet tam olarak silinmemiş de olabilir?!";
      exit;
   }
   $donenDeger=0;
   
   while($row = pg_fetch_row($ret)){
	   $donenDeger = $row[0];
	   //debug_to_console("Varolan sn: $row[0]");
	   
   }
   if($donenDeger!=0)
   {
	   $_SESSION["sepetNo"]=$donenDeger;
	   return $donenDeger; //Demek ki önceden bir sepet var halen!
	   
   }else{
	   //Yeni sepet oluşturuluyor...
	   $sorgu="INSERT INTO sepet(kullanici) VALUES('" . $_SESSION["user"] . "') RETURNING sn;";
	   $ret = pg_query($db, $sorgu);
	   
	   if(!$ret){
		   echo pg_last_error($db);
		   exit;
	   }
	   
	   $donenDeger = 0;
	   
	   while($row = pg_fetch_row($ret)){
		   $donenDeger = $row[0];	   
		   $_SESSION["sepetNo"] = $row[0];	
		}
		
		return $donenDeger;
	   
   }
	
}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}

function oncekiGorevlileriSil($gorevSn){
    
    global $db;

    $donenDeger = true;

    $sorgu="DELETE FROM gorev_alan WHERE gorev_sn='$gorevSn';";

    $ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Önceki Kullanıcılar silinemedi!";
        $donenDeger = false;
     } 

    return $donenDeger;
}



function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }




 
//Doğrudan harcamad da kullanılacak.
//O yüzden include yapmak daha mantıklı.

//require_once("TalepHarekettenAkinsoftdaOdemeYap.php");



 

//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */


include('siteBaslik.html');
include('menuKaynak.php');
//include('blink.html');
require("mesajlasma.php");
require("logYaz.php");
require("dosyalariYukle.php");
require("AkinsoftIslemleri.php");
require("AkinsoftCariAdVer.php");




require('reloadKontrol2.php');



//Öncelikle Bayi bilgisine ulaşılacak!


//echo $_SESSION["user"];
//echo $_SESSION["unique_ID"];
/*
$ip = $_SERVER["REMOTE_ADDR"];
$kullanici = $_SESSION["user"];
$id = $_SESSION["unique_ID"];
*/

//tabloBilgileri
//$kullanici = $_SESSION["user"];
$gelenTalepSn = $_POST['talepSn'];
$gelenKonu = str_replace("'", "''", $_POST["konu"]);
$gelenDetay = "Doğrudan Satınalma Girişi";
$gelenIslemTip = $_POST["islemTip"];
$gelenTamamlandi = $_POST['tamamlandi'];

//$gelenDetaySn = $_POST['talepDetaySn'];

$gelenDetaySn ="0";




$gelenNakit = $_POST['nakit'];
$gelenBanka = $_POST['banka'];
$gelenEvrak = $_POST['evrak'];
$gelenKk = $_POST['kk'];
$gelenAcik = $_POST['acik'];
$gelenSube = $_POST['sube'];
$gelenTalepEden = $_POST['talepEden'];


if($gelenNakit=="") $gelenNakit=0;
if($gelenBanka=="") $gelenBanka=0;
if($gelenEvrak=="") $gelenEvrak=0;
if($gelenKk=="") $gelenKk=0;
if($gelenAcik=="") $gelenAcik=0;


//------------------------------
$gelenCariKod =  $_POST['cariKod'];
$gelenFaturaDurumu = $_POST['faturaDurumu'];
$gelenKkKod =  $_POST['kkKod'];
$gelenBankaKod =  $_POST['bankaKod'];

//-----------------------------------
$gelenBorclanacakCariKod = $_POST['borclanacakCariKod'];
$gelenDogrudanFaturaNo = $_POST['faturaNo'];

$gelenDogrudanFaturaTarihi = $_POST['faturaTarihi'];


//-------------------------------
if(isset($_POST['gizliTalep'])) { // checkbox seçilmişse "on" değeri gönderiliyor
    //echo 'Onayladınız!';
    $gelenGizli = 'TRUE';
} else { // seçilmemişse bu değer sayfaya hiç gönderilmiyor
    //echo 'Onaylamadınız.';
    $gelenGizli = 'FALSE';
}


//-------------------------------
if(isset($_POST['acilTalep'])) { // checkbox seçilmişse "on" değeri gönderiliyor
    //echo 'Onayladınız!';
    $gelenAcil = 'TRUE';
} else { // seçilmemişse bu değer sayfaya hiç gönderilmiyor
    //echo 'Onaylamadınız.';
    $gelenAcil = 'FALSE';
}




//------------------------
$gelenHizmetKod =  $_POST['hizmetKod'];
$gelenStokKod =  $_POST['stokKod'];

$gelenMiktar = $_POST['miktar'];
$gelenBirim = $_POST['birim'];
$gelenIlgiliAlim = $_POST['ilgiliAlim'];

$gelenKilometre = $_POST['kilometre'];




if($gelenHizmetKod==""){ //Gelen stok kodu 

	$gelenHizmetKod = 0;	
	
}else{ //Gelen Hizmet

	$gelenMiktar = 1;
	$gelenBirim = "ADET";		
	
}

if($gelenKilometre==""){
	$gelenKilometre = 0;
}





	


if($gelenIlgiliAlim==""){
	$gelenIlgiliAlim = 0;
}





//Nakit ödeme ve Açık ödeme SIFIR ise cari kod aktarılmaz!
//if($gelenNakit==0 && $gelenAcik==0 ) $gelenCariKod="";


echo "<br>";
echo '<h1 style="text-align:center;">Doğrudan Harcama Kaydet ('.$gelenIslemTip.')</h1>';




if($gelenDogrudanFaturaTarihi==""){
	echo "<br><br><p>Fatura/Fiş Tarihi mutlaka girilmiş olmalıdır! İşlem devam edilemez!</p>";
	goto cikis;
}

if($gelenCariKod==""){
	echo "<br><br><p>Cari kodu mutlaka seçilmiş olmalıdır! İşlem devam edilemez!</p>";
	goto cikis;
}

/*
echo "---";
echo $gelenTablo."<br>";
echo $gelenKonu."<br>";
echo $gelenDetay."<br>";
echo $gelenGorevTarih."<br>";
echo $gelenHedefTarih."<br>";
echo $gelenUzamisTarih."<br>";
echo $gelenKayitMod."<br>";
echo "---";
*/

//dogrudan_fatura_no



if($gelenIslemTip=='Yeni Kayıt'){

    $sorguCumle="INSERT INTO talep(konu, detay, talep_eden, kullanici, nakit_odenen, bankadan_odenen, evrakla_odenen, 
                                    kk_odenen, sube, dogrudan, acik_odenen, dogrudan_odenen_cari_kod, fatura_durumu, kk_odenen_kk_kod, gizli, hizmet_kod, odenen_banka_kod, stok_kod, dogrudan_miktar, dogrudan_birim, dogrudan_ilgili_alim, borclanacak_cari_kod, kilometre, dogrudan_fatura_no, dogrudan_fatura_tarihi, acil) 
                    VALUES('$gelenKonu', '$gelenDetay', '$gelenTalepEden', '$kullanici', $gelenNakit, $gelenBanka, $gelenEvrak, 
                                    $gelenKk, '$gelenSube', TRUE, $gelenAcik, '$gelenCariKod', '$gelenFaturaDurumu', '$gelenKkKod', $gelenGizli, $gelenHizmetKod, '$gelenBankaKod', '$gelenStokKod', $gelenMiktar, '$gelenBirim', $gelenIlgiliAlim, '$gelenBorclanacakCariKod', $gelenKilometre, '$gelenDogrudanFaturaNo', '$gelenDogrudanFaturaTarihi', $gelenAcil) RETURNING sn;";


}elseif($gelenIslemTip=='Düzenleme'){

    if($gelenTamamlandi=="t"){

        $sorguCumle="UPDATE talep SET sube='$gelenSube' WHERE sn=$gelenTalepSn";

    }else {
        $sorguCumle="UPDATE talep SET konu='$gelenKonu', detay='$gelenDetay', 
                        nakit_odenen = $gelenNakit,
                        bankadan_odenen = $gelenBanka,
                        evrakla_odenen = $gelenEvrak,
                        kk_odenen = $gelenKk,
                        acik_odenen = $gelenAcik,
                        sube='$gelenSube',
                        talep_eden='$gelenTalepEden',
                        dogrudan_odenen_cari_kod = '$gelenCariKod',
                        fatura_durumu = '$gelenFaturaDurumu',
                        kk_odenen_kk_kod = '$gelenKkKod',
                        gizli = $gelenGizli,
                        hizmet_kod = $gelenHizmetKod, 
                        odenen_banka_kod = '$gelenBankaKod',
						stok_kod = '$gelenStokKod',
						dogrudan_miktar = $gelenMiktar,
						dogrudan_birim = '$gelenBirim',
						dogrudan_ilgili_alim = $gelenIlgiliAlim,
						borclanacak_cari_kod = '$gelenBorclanacakCariKod',
						kilometre = $gelenKilometre,
						dogrudan_fatura_no = '$gelenDogrudanFaturaNo',
						dogrudan_fatura_tarihi = '$gelenDogrudanFaturaTarihi',
						acil = $gelenAcil						
					WHERE sn=$gelenTalepSn;";        
    }

    
}


//echo '<br>'. $sorguCumle;
//exit;

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
      echo pg_last_error($db);
      echo "Görevlendirilme kaydedilemedi!";
      exit;
    }

    if($gelenIslemTip=='Yeni Kayıt'){

       while($row = pg_fetch_row($ret)){
            $gelenTalepSn = $row[0];
        }



    

    //********************************************************* */

        //İşlem başarılı ise mesaj verilecek.
        logYaz($kullanici . " Kullanıcısı Yeni Doğrudan Harcama oluşturdu!");
		
		//Ödeme NAKİT ise tediye makbuzu oluşturulacak.
		if($gelenNakit>0){
			
			$cariAd = akinsoftCariAdVer($gelenCariKod, 0);
			
			//Tahsilat makbuzu oluşturuluyor.
			$sorguCumle2 = "INSERT INTO tahsil_tediye_makbuz(tip, cari_kod, cari_ad, tutar, sube_kod, konu, kullanici) 
							  VALUES('Tediye', '$gelenCariKod', '$cariAd', $gelenNakit, '$gelenSube', '$gelenKonu', '$kullanici') RETURNING sn;";
			
			
			//echo $sorguCumle2;
			
			$ret2 = pg_query($db, $sorguCumle2);
			
			while($row2 = pg_fetch_row($ret2)){
				$makbuzNo = $row2[0];
			}
			
			
			
			
			
			
		}
		
	
	

        //Bu kısım KMS göndermek için.
        //Edip beyin isteği ile iptal edildi. 2025 02 26
		/*
        $sallaTel = []; 
        array_push($sallaTel, kullanicidanBilgiVer('cep_tel', "fcan") );
        array_push($sallaTel, kullanicidanBilgiVer('cep_tel', "eguler") );
        //kisaMesajGonder("$kullanici kullanıcısı $gelenTalepSn numaralı yeni doğrudan harcama girildi.", $sallaTel, 1);

        unset($sallaTel);
		*/

    } else if($gelenKayitMod=='Düzenleme'){

            // if($gelenNakit>0){

            //     echo "XXX111";
                
                
            //     talepHarekettenAkinsoftdaOdemeYap('t',  /*Doğrudan Mı?*/
            //                                     $kullanici,  /*Talebi üzerine alan kullanıcı*/
            //                                     $gelenTalepSn,  /*Tale No*/
            //                                     $gelenCariKod,  /*Ödeme Yapılan Cari Kod*/
            //                                     $gelenNakit,  /*Nakit Ödenen Tutar*/
            //                                     0,  /*Talep Hareket No*/
            //                                     $gelenSube,  /*Talep Şubesi*/
            //                                     $gelenFaturaDurumu  /*Fatura Durumu*/
            //                                     );
                
            //     echo "YYY222";
            // }






    logYaz($kullanici . " Kullanıcısı Yeni Doğrudan Harcamada Düzenleme yaptı!");    



    } 




   //Doyaları kaydediyor...
    //Buradan sonra varsa belgeler kaydolacak.


    //Dosya geliyorsa kaydedecek.
    dosyalariYukle("/mnt/talepBelgeler/".$gelenTalepSn."/".$gelenDetaySn."/", "Talep");

    

    





//Burada kullanıcılara bilgilendirme yapılacak.
$gidenKimden="<EMAIL>";
$gidenAdresler = ["<EMAIL>"];
$gidenTelefonlar = [];
$gidenBilgi="Yeni Doğrudan Harcama!";
$mesaj = "Doğrudan satınalma oluşturuldu! Konu: ".$gelenKonu." \nLütfen Portaldan kontrol eder misiniz?";

epostaGonder($gidenKimden, $gidenAdresler, $gidenBilgi, $mesaj);

//$gidenAdresler = array("<EMAIL>", "<EMAIL>"); //, "<EMAIL>", "<EMAIL>");
/*
foreach ($kisiler as $kisi){
    array_push($gidenAdresler, kullanicidanBilgiVer('eposta', $kisi));
    //array_push($gidenTelefonlar, kullanicidanBilgiVer('cep_tel', $kisi));

    $mesajX = $mesaj ."\n". paslamaKoduVer($kisi, 'Gorevlendirme.php', 'gorevSn;kayitMod', $anaKayitNo .';Görüntüleme');

    //Bu kısım KMS göndermek için.
    $sallaTel = []; 
    array_push($sallaTel, kullanicidanBilgiVer('cep_tel', $kisi) );
    //kisaMesajGonder($mesajX, $sallaTel, 1);
    unset($sallaTel);

    
}


epostaGonder($gidenKimden, $gidenAdresler, $gidenBilgi, $mesaj);

*/

echo "<br><br><br>";
echo '<h1 style="text-align:center;">'.$gelenTalepSn.' numaralı Doğrudan Harcama Başarı ile Kaydedilmiştir.</h1>';

echo "<br><br><br>";



cikis:

echo '<form action="talepDogrudanHarcama.php" method="post"  class="inline">';    
echo '<button type="submit" style="width:200px;"> Yeni Giriş </button>';
//echo '<button type="submit">Detay</button>';
echo '</form>';






//Makbuzu yazdırmak için 
//Yazdır
echo '<form action="MakbuzYazdir.php" id="makbuzYazdir" target="_blank"  method="post"  class="inline">'; 
echo '<input type="hidden" name="makbuzNo" value="'.$makbuzNo.'">';        
//echo '<button type="submit"><img src="Print.jpg" height="20" width="20" title="Pdf Yap==>'.$row[0].'"/></button>';
echo '</form>';






//echo '<button type="button" onclick="self.close();" style="width:200px;" >Kapat</button> ';

//En son yer...
pg_close($db);   



?>

</body>
</html>

