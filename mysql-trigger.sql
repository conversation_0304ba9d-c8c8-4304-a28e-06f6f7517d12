-- MySQL Trigger: talep tablosunda islemYapildi = 1 olduğunda talepOnayla.js'i çalıştır
-- Bu trigger MySQL server'ında (173.249.0.120) çalışacak

DELIMITER $$

-- Mevcut trigger varsa sil
DROP TRIGGER IF EXISTS talep_onay_trigger$$

-- <PERSON><PERSON> trigger oluştur
CREATE TRIGGER talep_onay_trigger
    AFTER UPDATE ON talep
    FOR EACH ROW
BEGIN
    -- Sadece islemYapildi alanı 0'dan 1'e değiştiğinde çalış
    IF OLD.islemYapildi = 0 AND NEW.islemYapildi = 1 THEN
        
        -- talepOnayla.js dosyasını çalıştır
        -- NOT: Bu komut MySQL'in sys_exec fonksiyonu veya UDF kullanır
        -- Alternatif olarak, bir event scheduler kullanılabilir
        
        -- Log tablosuna kayıt ekle (debug için)
        INSERT INTO talep_onay_log (talep_sn, islem_zaman, durum, aciklama)
        VALUES (NEW.sn, NOW(), 'TRIGGER_FIRED', CONCAT('Talep SN=', NEW.sn, ' için onay trigger tetiklendi'));
        
        -- Burada external script çağrısı yapılacak
        -- MySQL'de external script çalıştırmak için UDF veya sys_exec gerekli
        -- Alternatif: Bir flag tablosu kullanıp, cron job ile kontrol etmek
        
    END IF;
END$$

DELIMITER ;

-- Log tablosu oluştur (yoksa)
CREATE TABLE IF NOT EXISTS talep_onay_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    talep_sn INT NOT NULL,
    islem_zaman DATETIME NOT NULL,
    durum ENUM('TRIGGER_FIRED', 'PROCESSING', 'SUCCESS', 'ERROR') NOT NULL,
    aciklama TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_talep_sn (talep_sn),
    INDEX idx_islem_zaman (islem_zaman),
    INDEX idx_durum (durum)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_turkish_ci;

-- Trigger durumunu kontrol et
SHOW TRIGGERS LIKE 'talep';