{"name": "meg-talep-tracker", "version": "1.0.0", "description": "MEG Talep Tracking System - Monitor and log talep processes and approval workflows", "main": "process.js", "scripts": {"start": "node process.js", "dev": "nodemon process.js", "test": "node test-talep-onay.js", "api": "node talepOnayAPI.js", "install-deps": "npm install"}, "keywords": ["talep", "tracking", "postgresql", "monitoring", "approval"], "author": "MEG Development Team", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "mysql2": "^3.14.3", "pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}