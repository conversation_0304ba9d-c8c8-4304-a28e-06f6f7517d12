#!/usr/bin/env node

/**
 * MEG Talep Polling Sync System
 * Belirli aralıklarla PostgreSQL'i kontrol eder ve değişiklikleri MySQL'e senkronize eder
 * 
 * Usage: node polling-sync.js [interval_seconds]
 * Default interval: 30 saniye
 */

const { Pool } = require('pg');
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const http = require('http');
const url = require('url');
const dbConfig = require('./dbconfig');

// Log dosyası yolu
const LOG_FILE = path.join(__dirname, 'log.txt');

// Konfigürasyon
const POLL_INTERVAL = (process.argv[2] ? parseInt(process.argv[2]) : 30) * 1000; // milliseconds
const LAST_SYNC_FILE = path.join(__dirname, 'last-sync.json');
const HTTP_PORT = 3080;

/**
 * Log dosyasına yazma fonksiyonu
 */
function writeLog(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    
    // Console'a da yazdır
    console.log(logMessage.trim());
    
    // Dosyaya append et
    try {
        fs.appendFileSync(LOG_FILE, logMessage);
    } catch (error) {
        console.error('Log yazma hatası:', error);
    }
}

// PostgreSQL connection pool
const pool = new Pool(dbConfig);

// MySQL connection configuration
const mysqlConfig = {
    host: '**************',
    user: 'mehmet',
    password: 'Bormeg.mS_07112024s.tech',
    database: 'bormeg_msg',
    charset: 'utf8mb4',
    timezone: 'Europe/Istanbul'
};

let mysqlConnection;
let isRunning = true;
let httpServer;

/**
 * Son senkronizasyon zamanını oku
 */
function getLastSyncTime() {
    try {
        if (fs.existsSync(LAST_SYNC_FILE)) {
            const data = JSON.parse(fs.readFileSync(LAST_SYNC_FILE, 'utf8'));
            return new Date(data.last_sync);
        }
    } catch (error) {
        console.error('Son senkronizasyon zamanı okunamadı:', error);
    }
    
    // İlk çalışma: 25.08.2025 başından itibaren
    return new Date('2025-08-25 00:00:00');
}

/**
 * Son senkronizasyon zamanını kaydet
 */
function saveLastSyncTime(time) {
    try {
        fs.writeFileSync(LAST_SYNC_FILE, JSON.stringify({
            last_sync: time.toISOString(),
            updated_at: new Date().toISOString()
        }));
    } catch (error) {
        console.error('Son senkronizasyon zamanı kaydedilemedi:', error);
    }
}

/**
 * MySQL bağlantısını başlat
 */
async function initMysqlConnection() {
    try {
        if (mysqlConnection) {
            await mysqlConnection.end();
        }
        mysqlConnection = await mysql.createConnection(mysqlConfig);
        writeLog('MySQL bağlantısı kuruldu.');
    } catch (error) {
        writeLog('MySQL bağlantı hatası: ' + error.message);
        throw error;
    }
}

/**
 * Değişiklikleri kontrol et ve senkronize et
 */
async function checkAndSyncChanges() {
    const lastSync = getLastSyncTime();
    const now = new Date();
    
    writeLog(`Değişiklikler kontrol ediliyor (son senkronizasyon: ${lastSync.toLocaleString('tr-TR')})`);
    
    try {
        // Talep tablosu değişiklikleri
        await syncTableChanges('talep', lastSync);
        
        // Talep detay tablosu değişiklikleri
        await syncTableChanges('talep_detay', lastSync);
        
        // Talep detay silinmiş tablosu değişiklikleri
        await syncTableChanges('talep_detay_silinmis', lastSync);
        
        // Talep doğrudan değişiklik tablosu değişiklikleri
        await syncTableChanges('talep_dogrudan_degisiklik', lastSync);
        
        // Talep hareket tablosu değişiklikleri
        await syncTableChanges('talep_hareket', lastSync);
        
        // Talep hareket silinmiş tablosu değişiklikleri
        await syncTableChanges('talep_hareket_silinmis', lastSync);
        
        // Talep silinmiş tablosu değişiklikleri
        await syncTableChanges('talep_silinmis', lastSync);
        
        // Talep silme isteği tablosu değişiklikleri
        await syncTableChanges('talep_silme_istegi', lastSync);
        
        // Talep üzerine alma hareket tablosu değişiklikleri
        await syncTableChanges('talep_uzerine_alma_hareket', lastSync);
        
        // Son senkronizasyon zamanını güncelle
        saveLastSyncTime(now);
        
        writeLog(`Senkronizasyon tamamlandı: ${now.toLocaleString('tr-TR')}`);
        
    } catch (error) {
        writeLog('Senkronizasyon hatası: ' + error.message);
        
        // MySQL bağlantı hatası durumunda yeniden bağlan
        if (error.code === 'PROTOCOL_CONNECTION_LOST') {
            await initMysqlConnection();
        }
    }
}

/**
 * Belirli bir tablo için değişiklikleri senkronize et
 */
async function syncTableChanges(tableName, lastSync) {
    // İlk çalışma kontrolü - eğer last-sync.json yoksa tüm kayıtları al
    const isFirstRun = !fs.existsSync(LAST_SYNC_FILE);
    
    let query;
    let queryParams = [];
    
    if (isFirstRun) {
        // İlk çalışma: 25.08.2025 ve sonrası TÜM kayıtları al
        query = `
            SELECT * FROM ${tableName} 
            WHERE duzeltme_zaman >= '2025-08-25 00:00:00' 
            ORDER BY duzeltme_zaman ASC
        `;
        writeLog(`İLK ÇALIŞMA: ${tableName} tablosu için 25.08.2025 ve sonrası TÜM kayıtlar alınıyor...`);
    } else {
        // Normal çalışma: Son senkronizasyondan sonraki kayıtları al
        query = `
            SELECT * FROM ${tableName} 
            WHERE duzeltme_zaman > $1 
            ORDER BY duzeltme_zaman ASC
        `;
        queryParams = [lastSync];
    }
    
    const result = await pool.query(query, queryParams);
    
    if (result.rows.length === 0) {
        writeLog(`${tableName} tablosunda yeni değişiklik bulunamadı.`);
        return;
    }
    
    writeLog(`${tableName} tablosunda ${result.rows.length} kayıt bulundu ve senkronize ediliyor...`);
    
    for (const row of result.rows) {
        await syncSingleRecord(tableName, row);
    }
    
    if (isFirstRun) {
        writeLog(`İLK ÇALIŞMA TAMAMLANDI: ${tableName} - ${result.rows.length} kayıt senkronize edildi.`);
    }
}

/**
 * Tek bir kaydı senkronize et
 */
async function syncSingleRecord(tableName, record) {
    if (!mysqlConnection) {
        await initMysqlConnection();
    }
    
    switch (tableName) {
        case 'talep':
            await syncTalepRecord(record);
            break;
        case 'talep_detay':
            await syncTalepDetayRecord(record);
            break;
        case 'talep_detay_silinmis':
            await syncTalepDetaySilinmisRecord(record);
            break;
        case 'talep_dogrudan_degisiklik':
            await syncTalepDogrudan_DegisiklikRecord(record);
            break;
        case 'talep_hareket':
            await syncTalepHareketRecord(record);
            break;
        case 'talep_hareket_silinmis':
            await syncTalepHareketSilinmisRecord(record);
            break;
        case 'talep_silinmis':
            await syncTalepSilinmisRecord(record);
            break;
        case 'talep_silme_istegi':
            await syncTalepSilmeIstegiRecord(record);
            break;
        case 'talep_uzerine_alma_hareket':
            await syncTalepUzerineAlmaHareketRecord(record);
            break;
    }
}

/**
 * Talep kaydını senkronize et
 */
async function syncTalepRecord(record) {
    const query = `
        INSERT IGNORE INTO talep (
            sn, konu, detay, talep_eden, kullanici, sube, cari_kod, borclanacak_cari_kod,
            hizmet_kod, stok_kod, gizli, tamamlandi, tamamlanma_zaman, bekle, gorev_sn,
            dogrudan, nakit_odenen, bankadan_odenen, evrakla_odenen, kk_odenen, acik_odenen,
            dogrudan_odenen_cari_kod, fatura_durumu, kk_odenen_kk_kod, odenen_banka_kod,
            dogrudan_miktar, dogrudan_birim, dogrudan_ilgili_alim, kilometre, dogrudan_fatura_no,
            dogrudan_onaylayan, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            konu = VALUES(konu),
            detay = VALUES(detay),
            talep_eden = VALUES(talep_eden),
            kullanici = VALUES(kullanici),
            sube = VALUES(sube),
            cari_kod = VALUES(cari_kod),
            borclanacak_cari_kod = VALUES(borclanacak_cari_kod),
            hizmet_kod = VALUES(hizmet_kod),
            stok_kod = VALUES(stok_kod),
            gizli = VALUES(gizli),
            tamamlandi = VALUES(tamamlandi),
            tamamlanma_zaman = VALUES(tamamlanma_zaman),
            bekle = VALUES(bekle),
            gorev_sn = VALUES(gorev_sn),
            dogrudan = VALUES(dogrudan),
            nakit_odenen = VALUES(nakit_odenen),
            bankadan_odenen = VALUES(bankadan_odenen),
            evrakla_odenen = VALUES(evrakla_odenen),
            kk_odenen = VALUES(kk_odenen),
            acik_odenen = VALUES(acik_odenen),
            dogrudan_odenen_cari_kod = VALUES(dogrudan_odenen_cari_kod),
            fatura_durumu = VALUES(fatura_durumu),
            kk_odenen_kk_kod = VALUES(kk_odenen_kk_kod),
            odenen_banka_kod = VALUES(odenen_banka_kod),
            dogrudan_miktar = VALUES(dogrudan_miktar),
            dogrudan_birim = VALUES(dogrudan_birim),
            dogrudan_ilgili_alim = VALUES(dogrudan_ilgili_alim),
            kilometre = VALUES(kilometre),
            dogrudan_fatura_no = VALUES(dogrudan_fatura_no),
            dogrudan_onaylayan = VALUES(dogrudan_onaylayan),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.konu, record.detay, record.talep_eden, record.kullanici,
        record.sube, record.cari_kod, record.borclanacak_cari_kod, record.hizmet_kod,
        record.stok_kod, record.gizli, record.tamamlandi, record.tamamlanma_zaman,
        record.bekle, record.gorev_sn, record.dogrudan, record.nakit_odenen,
        record.bankadan_odenen, record.evrakla_odenen, record.kk_odenen, record.acik_odenen,
        record.dogrudan_odenen_cari_kod, record.fatura_durumu, record.kk_odenen_kk_kod,
        record.odenen_banka_kod, record.dogrudan_miktar, record.dogrudan_birim,
        record.dogrudan_ilgili_alim, record.kilometre, record.dogrudan_fatura_no,
        record.dogrudan_onaylayan, record.yaratma_zaman, record.duzeltme_zaman
    ]);
    
    console.log(`Talep senkronize edildi: ${record.sn} - ${record.konu?.substring(0, 50)}...`);
}

/**
 * Talep detay kaydını senkronize et
 */
async function syncTalepDetayRecord(record) {
    const query = `
        INSERT INTO talep_detay (
            sn, talep_sn, aciklama, miktar, ilk_miktar, birim, termin_zaman,
            onay, onay_zaman, onay_kullanici, isleme_alindi, islem_zaman, islem_kullanici,
            tamamlandi, tamamlandi_zaman, iptal, iptal_kullanici, beklemede, kullanici,
            ilgili_alim, ilgili_alim_2, hizmet_kod, stok_kod, kilometre, teslim_kabul,
            teslim_kabul_adet, teslim_kabul_tarih, teslim_kabul_kullanici,
            yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            talep_sn = VALUES(talep_sn),
            aciklama = VALUES(aciklama),
            miktar = VALUES(miktar),
            ilk_miktar = VALUES(ilk_miktar),
            birim = VALUES(birim),
            termin_zaman = VALUES(termin_zaman),
            onay = VALUES(onay),
            onay_zaman = VALUES(onay_zaman),
            onay_kullanici = VALUES(onay_kullanici),
            isleme_alindi = VALUES(isleme_alindi),
            islem_zaman = VALUES(islem_zaman),
            islem_kullanici = VALUES(islem_kullanici),
            tamamlandi = VALUES(tamamlandi),
            tamamlandi_zaman = VALUES(tamamlandi_zaman),
            iptal = VALUES(iptal),
            iptal_kullanici = VALUES(iptal_kullanici),
            beklemede = VALUES(beklemede),
            kullanici = VALUES(kullanici),
            ilgili_alim = VALUES(ilgili_alim),
            ilgili_alim_2 = VALUES(ilgili_alim_2),
            hizmet_kod = VALUES(hizmet_kod),
            stok_kod = VALUES(stok_kod),
            kilometre = VALUES(kilometre),
            teslim_kabul = VALUES(teslim_kabul),
            teslim_kabul_adet = VALUES(teslim_kabul_adet),
            teslim_kabul_tarih = VALUES(teslim_kabul_tarih),
            teslim_kabul_kullanici = VALUES(teslim_kabul_kullanici),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.talep_sn, record.aciklama, record.miktar, record.ilk_miktar,
        record.birim, record.termin_zaman, record.onay, record.onay_zaman, record.onay_kullanici,
        record.isleme_alindi, record.islem_zaman, record.islem_kullanici, record.tamamlandi,
        record.tamamlandi_zaman, record.iptal, record.iptal_kullanici, record.beklemede,
        record.kullanici, record.ilgili_alim, record.ilgili_alim_2, record.hizmet_kod,
        record.stok_kod, record.kilometre, record.teslim_kabul, record.teslim_kabul_adet,
        record.teslim_kabul_tarih, record.teslim_kabul_kullanici, record.yaratma_zaman,
        record.duzeltme_zaman
    ]);
    
    console.log(`Talep detay senkronize edildi: ${record.sn} - Talep: ${record.talep_sn}`);
}

/**
 * Talep hareket kaydını senkronize et
 */
async function syncTalepHareketRecord(record) {
    const query = `
        INSERT INTO talep_hareket (
            sn, talep_sn, talep_detay_sn, hareket_tipi, nakit_odenen, bankadan_odenen,
            evrakla_odenen, kk_odenen, acik_odenen, kapali_mi, kullanici,
            yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            talep_sn = VALUES(talep_sn),
            talep_detay_sn = VALUES(talep_detay_sn),
            hareket_tipi = VALUES(hareket_tipi),
            nakit_odenen = VALUES(nakit_odenen),
            bankadan_odenen = VALUES(bankadan_odenen),
            evrakla_odenen = VALUES(evrakla_odenen),
            kk_odenen = VALUES(kk_odenen),
            acik_odenen = VALUES(acik_odenen),
            kapali_mi = VALUES(kapali_mi),
            kullanici = VALUES(kullanici),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.talep_sn, record.talep_detay_sn, record.hareket_tipi,
        record.nakit_odenen, record.bankadan_odenen, record.evrakla_odenen,
        record.kk_odenen, record.acik_odenen, record.kapali_mi, record.kullanici,
        record.yaratma_zaman, record.duzeltme_zaman
    ]);
    
    console.log(`Talep hareket senkronize edildi: ${record.sn} - Talep: ${record.talep_sn}`);
}

/**
 * Talep detay silinmiş kaydını senkronize et
 */
async function syncTalepDetaySilinmisRecord(record) {
    const query = `
        INSERT INTO talep_detay_silinmis (
            sn, talep_sn, aciklama, miktar, ilk_miktar, birim, termin_zaman,
            onay, onay_zaman, onay_kullanici, isleme_alindi, islem_zaman, islem_kullanici,
            tamamlandi, tamamlandi_zaman, iptal, iptal_kullanici, beklemede, kullanici,
            ilgili_alim, ilgili_alim_2, hizmet_kod, stok_kod, kilometre, teslim_kabul,
            teslim_kabul_adet, teslim_kabul_tarih, teslim_kabul_kullanici,
            yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            talep_sn = VALUES(talep_sn),
            aciklama = VALUES(aciklama),
            miktar = VALUES(miktar),
            ilk_miktar = VALUES(ilk_miktar),
            birim = VALUES(birim),
            termin_zaman = VALUES(termin_zaman),
            onay = VALUES(onay),
            onay_zaman = VALUES(onay_zaman),
            onay_kullanici = VALUES(onay_kullanici),
            isleme_alindi = VALUES(isleme_alindi),
            islem_zaman = VALUES(islem_zaman),
            islem_kullanici = VALUES(islem_kullanici),
            tamamlandi = VALUES(tamamlandi),
            tamamlandi_zaman = VALUES(tamamlandi_zaman),
            iptal = VALUES(iptal),
            iptal_kullanici = VALUES(iptal_kullanici),
            beklemede = VALUES(beklemede),
            kullanici = VALUES(kullanici),
            ilgili_alim = VALUES(ilgili_alim),
            ilgili_alim_2 = VALUES(ilgili_alim_2),
            hizmet_kod = VALUES(hizmet_kod),
            stok_kod = VALUES(stok_kod),
            kilometre = VALUES(kilometre),
            teslim_kabul = VALUES(teslim_kabul),
            teslim_kabul_adet = VALUES(teslim_kabul_adet),
            teslim_kabul_tarih = VALUES(teslim_kabul_tarih),
            teslim_kabul_kullanici = VALUES(teslim_kabul_kullanici),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.talep_sn, record.aciklama, record.miktar, record.ilk_miktar,
        record.birim, record.termin_zaman, record.onay, record.onay_zaman, record.onay_kullanici,
        record.isleme_alindi, record.islem_zaman, record.islem_kullanici, record.tamamlandi,
        record.tamamlandi_zaman, record.iptal, record.iptal_kullanici, record.beklemede,
        record.kullanici, record.ilgili_alim, record.ilgili_alim_2, record.hizmet_kod,
        record.stok_kod, record.kilometre, record.teslim_kabul, record.teslim_kabul_adet,
        record.teslim_kabul_tarih, record.teslim_kabul_kullanici, record.yaratma_zaman,
        record.duzeltme_zaman
    ]);
    
    console.log(`Talep detay silinmiş senkronize edildi: ${record.sn} - Talep: ${record.talep_sn}`);
}

/**
 * Talep doğrudan değişiklik kaydını senkronize et
 */
async function syncTalepDogrudan_DegisiklikRecord(record) {
    const query = `
        INSERT INTO talep_dogrudan_degisiklik (
            sn, talep_sn, eski_miktar, yeni_miktar, eski_birim, yeni_birim,
            kullanici, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            talep_sn = VALUES(talep_sn),
            eski_miktar = VALUES(eski_miktar),
            yeni_miktar = VALUES(yeni_miktar),
            eski_birim = VALUES(eski_birim),
            yeni_birim = VALUES(yeni_birim),
            kullanici = VALUES(kullanici),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.talep_sn, record.eski_miktar, record.yeni_miktar,
        record.eski_birim, record.yeni_birim, record.kullanici,
        record.yaratma_zaman, record.duzeltme_zaman
    ]);
    
    console.log(`Talep doğrudan değişiklik senkronize edildi: ${record.sn} - Talep: ${record.talep_sn}`);
}

/**
 * Talep hareket silinmiş kaydını senkronize et
 */
async function syncTalepHareketSilinmisRecord(record) {
    const query = `
        INSERT INTO talep_hareket_silinmis (
            sn, talep_sn, talep_detay_sn, hareket_tipi, nakit_odenen, bankadan_odenen,
            evrakla_odenen, kk_odenen, acik_odenen, kapali_mi, kullanici,
            yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            talep_sn = VALUES(talep_sn),
            talep_detay_sn = VALUES(talep_detay_sn),
            hareket_tipi = VALUES(hareket_tipi),
            nakit_odenen = VALUES(nakit_odenen),
            bankadan_odenen = VALUES(bankadan_odenen),
            evrakla_odenen = VALUES(evrakla_odenen),
            kk_odenen = VALUES(kk_odenen),
            acik_odenen = VALUES(acik_odenen),
            kapali_mi = VALUES(kapali_mi),
            kullanici = VALUES(kullanici),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.talep_sn, record.talep_detay_sn, record.hareket_tipi,
        record.nakit_odenen, record.bankadan_odenen, record.evrakla_odenen,
        record.kk_odenen, record.acik_odenen, record.kapali_mi, record.kullanici,
        record.yaratma_zaman, record.duzeltme_zaman
    ]);
    
    console.log(`Talep hareket silinmiş senkronize edildi: ${record.sn} - Talep: ${record.talep_sn}`);
}

/**
 * Talep silinmiş kaydını senkronize et
 */
async function syncTalepSilinmisRecord(record) {
    const query = `
        INSERT INTO talep_silinmis (
            sn, konu, detay, talep_eden, kullanici, sube, cari_kod, borclanacak_cari_kod,
            hizmet_kod, stok_kod, gizli, tamamlandi, tamamlanma_zaman, bekle, gorev_sn,
            dogrudan, nakit_odenen, bankadan_odenen, evrakla_odenen, kk_odenen, acik_odenen,
            dogrudan_odenen_cari_kod, fatura_durumu, kk_odenen_kk_kod, odenen_banka_kod,
            dogrudan_miktar, dogrudan_birim, dogrudan_ilgili_alim, kilometre, dogrudan_fatura_no,
            dogrudan_onaylayan, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            konu = VALUES(konu),
            detay = VALUES(detay),
            talep_eden = VALUES(talep_eden),
            kullanici = VALUES(kullanici),
            sube = VALUES(sube),
            cari_kod = VALUES(cari_kod),
            borclanacak_cari_kod = VALUES(borclanacak_cari_kod),
            hizmet_kod = VALUES(hizmet_kod),
            stok_kod = VALUES(stok_kod),
            gizli = VALUES(gizli),
            tamamlandi = VALUES(tamamlandi),
            tamamlanma_zaman = VALUES(tamamlanma_zaman),
            bekle = VALUES(bekle),
            gorev_sn = VALUES(gorev_sn),
            dogrudan = VALUES(dogrudan),
            nakit_odenen = VALUES(nakit_odenen),
            bankadan_odenen = VALUES(bankadan_odenen),
            evrakla_odenen = VALUES(evrakla_odenen),
            kk_odenen = VALUES(kk_odenen),
            acik_odenen = VALUES(acik_odenen),
            dogrudan_odenen_cari_kod = VALUES(dogrudan_odenen_cari_kod),
            fatura_durumu = VALUES(fatura_durumu),
            kk_odenen_kk_kod = VALUES(kk_odenen_kk_kod),
            odenen_banka_kod = VALUES(odenen_banka_kod),
            dogrudan_miktar = VALUES(dogrudan_miktar),
            dogrudan_birim = VALUES(dogrudan_birim),
            dogrudan_ilgili_alim = VALUES(dogrudan_ilgili_alim),
            kilometre = VALUES(kilometre),
            dogrudan_fatura_no = VALUES(dogrudan_fatura_no),
            dogrudan_onaylayan = VALUES(dogrudan_onaylayan),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.konu, record.detay, record.talep_eden, record.kullanici,
        record.sube, record.cari_kod, record.borclanacak_cari_kod, record.hizmet_kod,
        record.stok_kod, record.gizli, record.tamamlandi, record.tamamlanma_zaman,
        record.bekle, record.gorev_sn, record.dogrudan, record.nakit_odenen,
        record.bankadan_odenen, record.evrakla_odenen, record.kk_odenen, record.acik_odenen,
        record.dogrudan_odenen_cari_kod, record.fatura_durumu, record.kk_odenen_kk_kod,
        record.odenen_banka_kod, record.dogrudan_miktar, record.dogrudan_birim,
        record.dogrudan_ilgili_alim, record.kilometre, record.dogrudan_fatura_no,
        record.dogrudan_onaylayan, record.yaratma_zaman, record.duzeltme_zaman
    ]);
    
    console.log(`Talep silinmiş senkronize edildi: ${record.sn} - ${record.konu?.substring(0, 50)}...`);
}

/**
 * Talep silme isteği kaydını senkronize et
 */
async function syncTalepSilmeIstegiRecord(record) {
    const query = `
        INSERT INTO talep_silme_istegi (
            sn, talep_sn, istegi_yapan, neden, durum, yanitlayan, yanit,
            yanitlama_zaman, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            talep_sn = VALUES(talep_sn),
            istegi_yapan = VALUES(istegi_yapan),
            neden = VALUES(neden),
            durum = VALUES(durum),
            yanitlayan = VALUES(yanitlayan),
            yanit = VALUES(yanit),
            yanitlama_zaman = VALUES(yanitlama_zaman),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.talep_sn, record.istegi_yapan, record.neden,
        record.durum, record.yanitlayan, record.yanit, record.yanitlama_zaman,
        record.yaratma_zaman, record.duzeltme_zaman
    ]);
    
    console.log(`Talep silme isteği senkronize edildi: ${record.sn} - Talep: ${record.talep_sn}`);
}

/**
 * Talep üzerine alma hareket kaydını senkronize et
 */
async function syncTalepUzerineAlmaHareketRecord(record) {
    const query = `
        INSERT INTO talep_uzerine_alma_hareket (
            sn, talep_sn, eski_kullanici, yeni_kullanici, degistiren,
            neden, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            talep_sn = VALUES(talep_sn),
            eski_kullanici = VALUES(eski_kullanici),
            yeni_kullanici = VALUES(yeni_kullanici),
            degistiren = VALUES(degistiren),
            neden = VALUES(neden),
            duzeltme_zaman = NOW()
    `;
    
    await mysqlConnection.execute(query, [
        record.sn, record.talep_sn, record.eski_kullanici, record.yeni_kullanici,
        record.degistiren, record.neden, record.yaratma_zaman, record.duzeltme_zaman
    ]);
    
    console.log(`Talep üzerine alma hareket senkronize edildi: ${record.sn} - Talep: ${record.talep_sn}`);
}

/**
 * HTTP Request Body'yi oku
 */
function getRequestBody(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(JSON.parse(body));
            } catch (error) {
                resolve({});
            }
        });
        req.on('error', reject);
    });
}

/**
 * Talep onay endpoint'i
 */
async function handleTalepOnay(req, res) {
    try {
        const body = await getRequestBody(req);
        const { talep_detay_sn } = body;
        
        if (!talep_detay_sn) {
            res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({ 
                success: false, 
                error: 'talep_detay_sn gerekli' 
            }));
            return;
        }

        // PostgreSQL bağlantısı ile onay işlemi
        const client = await pool.connect();
        
        try {
            await client.query('BEGIN');
            
            // 1. talep_detay tablosunda onay = true yap
            const updateDetayQuery = `
                UPDATE talep_detay 
                SET onay = TRUE, 
                    onay_zaman = NOW(), 
                    onay_kullanici = 'eguler'
                WHERE sn = $1
                RETURNING talep_sn
            `;
            const detayResult = await client.query(updateDetayQuery, [talep_detay_sn]);
            
            if (detayResult.rows.length === 0) {
                throw new Error('Talep detayı bulunamadı');
            }
            
            const talep_sn = detayResult.rows[0].talep_sn;
            
            // 2. talep tablosunda dogrudan_onaylayan = 'eguler' yap
            const updateTalepQuery = `
                UPDATE talep 
                SET dogrudan_onaylayan = 'eguler'
                WHERE sn = $1
            `;
            await client.query(updateTalepQuery, [talep_sn]);
            
            await client.query('COMMIT');
            
            console.log(`Talep onaylandı: Detay SN=${talep_detay_sn}, Talep SN=${talep_sn}, Onaylayan=eguler`);
            
            res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({
                success: true,
                message: 'Talep başarıyla onaylandı',
                data: {
                    talep_detay_sn: talep_detay_sn,
                    talep_sn: talep_sn,
                    onaylayan: 'eguler'
                }
            }));
            
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
        
    } catch (error) {
        console.error('Talep onay hatası:', error);
        res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            success: false,
            error: error.message
        }));
    }
}

/**
 * HTTP Server oluştur
 */
function createHttpServer() {
    httpServer = http.createServer(async (req, res) => {
        // CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        
        if (req.method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }
        
        const parsedUrl = url.parse(req.url, true);
        const path = parsedUrl.pathname;
        
        // Routes
        if (path === '/talep/onay' && req.method === 'POST') {
            await handleTalepOnay(req, res);
        } else if (path === '/health' && req.method === 'GET') {
            // Health check endpoint
            res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({
                status: 'ok',
                service: 'MEG Talep Sync',
                timestamp: new Date().toISOString()
            }));
        } else if (path === '/' && req.method === 'GET') {
            // Ana sayfa
            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
                <html>
                    <head><title>MEG Talep Sync API</title></head>
                    <body>
                        <h1>MEG Talep Sync API</h1>
                        <h2>Endpoints:</h2>
                        <ul>
                            <li><strong>POST /talep/onay</strong> - Talep onaylama</li>
                            <li><strong>GET /health</strong> - Sistem durumu</li>
                        </ul>
                        <h3>Örnek kullanım:</h3>
                        <pre>
curl -X POST http://localhost:3080/talep/onay \\
  -H "Content-Type: application/json" \\
  -d '{"talep_detay_sn": 123}'
                        </pre>
                    </body>
                </html>
            `);
        } else {
            // 404
            res.writeHead(404, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({
                success: false,
                error: 'Endpoint bulunamadı'
            }));
        }
    });
    
    httpServer.listen(HTTP_PORT, () => {
        console.log(`HTTP API Server başlatıldı: http://localhost:${HTTP_PORT}`);
        console.log('Kullanılabilir endpoints:');
        console.log(`  POST http://localhost:${HTTP_PORT}/talep/onay`);
        console.log(`  GET  http://localhost:${HTTP_PORT}/health`);
    });
}

/**
 * Ana döngü
 */
async function startPolling() {
    console.log(`Polling başlatıldı. Kontrol aralığı: ${POLL_INTERVAL / 1000} saniye`);
    
    while (isRunning) {
        try {
            await checkAndSyncChanges();
        } catch (error) {
            console.error('Polling döngüsü hatası:', error);
        }
        
        // Belirtilen süre kadar bekle
        await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL));
    }
}

/**
 * Graceful shutdown
 */
async function shutdown() {
    console.log('Sistem kapatılıyor...');
    isRunning = false;
    
    try {
        if (httpServer) {
            httpServer.close();
            console.log('HTTP Server kapatıldı.');
        }
        
        if (mysqlConnection) {
            await mysqlConnection.end();
            console.log('MySQL bağlantısı kapatıldı.');
        }
        
        await pool.end();
        console.log('PostgreSQL bağlantı havuzu kapatıldı.');
        
    } catch (error) {
        console.error('Kapatma sırasında hata:', error);
    }
    
    process.exit(0);
}

/**
 * Ana fonksiyon
 */
async function main() {
    try {
        const isFirstRun = !fs.existsSync(LAST_SYNC_FILE);
        
        console.log('MEG Talep Polling Senkronizasyon Servisi başlatılıyor...');
        
        if (isFirstRun) {
            console.log('==========================================');
            console.log('İLK ÇALIŞMA: 25.08.2025 ve sonrası TÜM kayıtlar senkronize edilecek');
            console.log('Bu işlem biraz zaman alabilir...');
            console.log('==========================================');
        }
        
        // MySQL bağlantısını başlat
        await initMysqlConnection();
        
        // HTTP Server'ı başlat
        createHttpServer();
        
        // İlk senkronizasyonu çalıştır
        await checkAndSyncChanges();
        
        if (isFirstRun) {
            console.log('==========================================');
            console.log('İLK ÇALIŞMA TAMAMLANDI!');
            console.log('Bundan sonra 30 saniyede bir yeni değişiklikler kontrol edilecek.');
            console.log('==========================================');
        }
        
        // Polling döngüsünü başlat
        await startPolling();
        
    } catch (error) {
        console.error('Başlatma hatası:', error);
        process.exit(1);
    }
}

// Signal handlers
process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

// Çalıştır
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    main,
    checkAndSyncChanges,
    getLastSyncTime,
    saveLastSyncTime
};