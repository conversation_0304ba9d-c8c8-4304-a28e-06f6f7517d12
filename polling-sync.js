#!/usr/bin/env node

/**
 * MEG Talep Polling Sync System
 * Belirli aralıklarla PostgreSQL'i kontrol eder ve değişiklikleri MySQL'e senkronize eder
 * 
 * Usage: node polling-sync.js [interval_seconds]
 * Default interval: 30 saniye
 */

const { Pool } = require('pg');
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const http = require('http');
const url = require('url');
const dbConfig = require('./dbconfig');

// Log dosyası yolu
const LOG_FILE = path.join(__dirname, 'log.txt');

// Konfigürasyon
const POLL_INTERVAL = (process.argv[2] ? parseInt(process.argv[2]) : 30) * 1000; // milliseconds
const LAST_SYNC_FILE = path.join(__dirname, 'last-sync.json');
const HTTP_PORT = 3080;

/**
 * Log dosyasına yazma fonksiyonu
 */
function writeLog(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    
    // Console'a da yazdır
    console.log(logMessage.trim());
    
    // Dosyaya append et
    try {
        fs.appendFileSync(LOG_FILE, logMessage);
    } catch (error) {
        console.error('Log yazma hatası:', error);
    }
}

/**
 * Undefined değerleri null'a çeviren helper fonksiyon
 */
function sanitizeValue(value) {
    return value === undefined ? null : value;
}

/**
 * NOT NULL alanlar için özel sanitize fonksiyon
 */
function sanitizeNotNull(value, defaultValue = '') {
    if (value === undefined || value === null) {
        return defaultValue;
    }
    return value;
}

// PostgreSQL connection pool
const pool = new Pool(dbConfig);

// MySQL connection configuration
const mysqlConfig = {
    host: '**************',
    user: 'mehmet',
    password: 'Bormeg.mS_07112024s.tech',
    database: 'bormeg_msg',
    charset: 'utf8mb4',
    timezone: '+03:00'
};

let mysqlConnection;
let isRunning = true;
let httpServer;

/**
 * Belirli bir tablo için son senkronizasyon zamanını oku
 */
function getLastSyncTimeForTable(tableName) {
    try {
        if (fs.existsSync(LAST_SYNC_FILE)) {
            const data = JSON.parse(fs.readFileSync(LAST_SYNC_FILE, 'utf8'));
            if (data.tables && data.tables[tableName]) {
                return new Date(data.tables[tableName]);
            }
        }
    } catch (error) {
        writeLog(`${tableName} son senkronizasyon zamanı okunamadı:`, error);
    }
    
    // İlk çalışma: 25.08.2025 başından itibaren
    return new Date('2025-08-25 00:00:00');
}

/**
 * Belirli bir tablo için son senkronizasyon zamanını kaydet
 */
function saveLastSyncTimeForTable(tableName, time) {
    try {
        let data = { tables: {} };
        if (fs.existsSync(LAST_SYNC_FILE)) {
            data = JSON.parse(fs.readFileSync(LAST_SYNC_FILE, 'utf8'));
            if (!data.tables) data.tables = {};
        }
        
        data.tables[tableName] = time.toISOString();
        data.updated_at = new Date().toISOString();
        
        fs.writeFileSync(LAST_SYNC_FILE, JSON.stringify(data, null, 2));
    } catch (error) {
        writeLog(`${tableName} son senkronizasyon zamanı kaydedilemedi:`, error);
    }
}

/**
 * MySQL'den belirli bir tablonun son kayıt zamanını al
 */
async function getLastRecordTimeFromMySQL(tableName) {
    try {
        const [rows] = await mysqlConnection.execute(
            `SELECT MAX(duzeltme_zaman) as max_time FROM ${tableName} LIMIT 1`
        );
        
        if (rows.length > 0 && rows[0].max_time) {
            return new Date(rows[0].max_time);
        }
    } catch (error) {
        writeLog(`${tableName} MySQL son kayıt zamanı alınamadı: ${error.message}`);
    }
    
    // Eğer tablo boşsa veya hata varsa, başlangıç zamanını dön
    return new Date('2025-08-25 00:00:00');
}

/**
 * MySQL bağlantısını başlat
 */
async function initMysqlConnection() {
    try {
        if (mysqlConnection) {
            await mysqlConnection.end();
        }
        mysqlConnection = await mysql.createConnection(mysqlConfig);
        writeLog('MySQL bağlantısı kuruldu.');
    } catch (error) {
        writeLog('MySQL bağlantı hatası: ' + error.message);
        throw error;
    }
}

/**
 * PostgreSQL ve MySQL şemalarını karşılaştır ve NOT NULL farklarını logla
 */
async function checkTableSchemas() {
    const tableNames = [
        'talep', 'talep_detay', 'talep_hareket'
    ];
    
    writeLog('==========================================');
    writeLog('PostgreSQL VE MySQL ŞEMA KARŞILAŞTIRMASI');
    writeLog('==========================================');
    
    for (const tableName of tableNames) {
        try {
            writeLog(`\n🔍 ${tableName.toUpperCase()} tablosu analiz ediliyor...`);
            
            // PostgreSQL şeması
            let pgColumns = [];
            try {
                const pgResult = await pool.query(`
                    SELECT column_name, is_nullable, data_type, column_default
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' AND table_name = $1 
                    ORDER BY ordinal_position
                `, [tableName]);
                pgColumns = pgResult.rows;
                writeLog(`📘 PostgreSQL: ${pgColumns.length} alan bulundu`);
            } catch (error) {
                writeLog(`❌ PostgreSQL şeması okunamadı: ${error.message}`);
            }
            
            // MySQL şeması
            let mysqlColumns = [];
            try {
                const [mysqlResult] = await mysqlConnection.execute(`
                    SELECT COLUMN_NAME as column_name, IS_NULLABLE as is_nullable, 
                           DATA_TYPE as data_type, COLUMN_DEFAULT as column_default
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? 
                    ORDER BY ORDINAL_POSITION
                `, [mysqlConfig.database, tableName]);
                mysqlColumns = mysqlResult;
                writeLog(`📗 MySQL: ${mysqlColumns.length} alan bulundu`);
            } catch (error) {
                writeLog(`❌ MySQL şeması okunamadı: ${error.message}`);
            }
            
            if (pgColumns.length === 0 || mysqlColumns.length === 0) {
                writeLog(`⚠️ ${tableName} tablosu karşılaştırılamadı!`);
                continue;
            }
            
            // NOT NULL karşılaştırması
            writeLog(`\n🔒 NOT NULL ALAN ANALİZİ:`);
            
            const pgNotNullMap = new Map();
            pgColumns.forEach(col => {
                pgNotNullMap.set(col.column_name.toLowerCase(), col.is_nullable === 'NO');
            });
            
            const mysqlNotNullMap = new Map();
            mysqlColumns.forEach(col => {
                mysqlNotNullMap.set(col.column_name.toLowerCase(), col.is_nullable === 'NO');
            });
            
            // Tüm alanları kontrol et
            const allColumns = new Set([...pgNotNullMap.keys(), ...mysqlNotNullMap.keys()]);
            
            const problemColumns = [];
            const matchingColumns = [];
            
            allColumns.forEach(colName => {
                const pgNotNull = pgNotNullMap.get(colName);
                const mysqlNotNull = mysqlNotNullMap.get(colName);
                
                if (pgNotNull === undefined) {
                    writeLog(`   ⚠️  ${colName} - Sadece MySQL'de var (NOT NULL: ${mysqlNotNull})`);
                } else if (mysqlNotNull === undefined) {
                    writeLog(`   ⚠️  ${colName} - Sadece PostgreSQL'de var (NOT NULL: ${pgNotNull})`);
                } else if (pgNotNull !== mysqlNotNull) {
                    writeLog(`   🚨 ${colName} - FARK: PG(${pgNotNull ? 'NOT NULL' : 'NULL'}) vs MySQL(${mysqlNotNull ? 'NOT NULL' : 'NULL'})`);
                    if (mysqlNotNull && !pgNotNull) {
                        problemColumns.push(colName);
                    }
                } else {
                    matchingColumns.push({colName, notNull: pgNotNull});
                }
            });
            
            writeLog(`\n✅ Eşleşen alanlar: ${matchingColumns.length}`);
            writeLog(`🚨 Sorunlu alanlar: ${problemColumns.length}`);
            
            if (problemColumns.length > 0) {
                writeLog(`\n⚡ ${tableName} tablosu için YAPILMASI GEREKENLER:`);
                problemColumns.forEach(colName => {
                    writeLog(`   - ${colName} alanı için sanitizeNotNull() kullanılmalı`);
                });
            }
            
        } catch (error) {
            writeLog(`❌ ${tableName} karşılaştırması başarısız: ${error.message}`);
        }
    }
    
    writeLog('\n==========================================');
    writeLog('ŞEMA KARŞILAŞTIRMASI TAMAMLANDI');
    writeLog('==========================================\n');
}

/**
 * Değişiklikleri kontrol et ve senkronize et
 */
async function checkAndSyncChanges() {
    const now = new Date();
    
    writeLog(`Değişiklikler kontrol ediliyor...`);
    
    const tables = [
        'talep', 'talep_detay', 'talep_hareket'
    ];
    
    try {
        for (const tableName of tables) {
            await syncTableChanges(tableName);
        }
        
        writeLog(`Senkronizasyon tamamlandı: ${now.toLocaleString('tr-TR')}`);
        
    } catch (error) {
        writeLog('Senkronizasyon hatası: ' + error.message);
        
        // MySQL bağlantı hatası durumunda yeniden bağlan
        if (error.code === 'PROTOCOL_CONNECTION_LOST') {
            await initMysqlConnection();
        }
    }
}

/**
 * Belirli bir tablo için değişiklikleri senkronize et
 */
async function syncTableChanges(tableName) {
    try {
        writeLog(`\n🔄 ${tableName.toUpperCase()} tablosu senkronize ediliyor...`);
        
        // MySQL'den son kayıt zamanını al
        const mysqlLastTime = await getLastRecordTimeFromMySQL(tableName);
        writeLog(`📗 MySQL son kayıt zamanı: ${mysqlLastTime.toLocaleString('tr-TR')}`);
        
        // Bu tablo için son senkronizasyon zamanını al
        const lastSyncTime = getLastSyncTimeForTable(tableName);
        writeLog(`📝 Son senkronizasyon zamanı: ${lastSyncTime.toLocaleString('tr-TR')}`);
        
        // Hangi zamandan sonraki kayıtları alacağımızı belirle (MySQL'den daha yeni olanları)
        const syncFromTime = new Date(Math.max(mysqlLastTime.getTime(), lastSyncTime.getTime()));
        
        // PostgreSQL'den yeni kayıtları al
        const query = `
            SELECT * FROM ${tableName} 
            WHERE duzeltme_zaman > $1 
            ORDER BY duzeltme_zaman ASC
        `;
        
        writeLog(`📘 PostgreSQL'den ${syncFromTime.toLocaleString('tr-TR')} sonrası kayıtlar alınıyor...`);
        const result = await pool.query(query, [syncFromTime]);
        
        if (result.rows.length === 0) {
            writeLog(`✅ ${tableName} tablosunda yeni değişiklik bulunamadı.`);
            return;
        }
        
        writeLog(`📊 ${tableName} tablosunda ${result.rows.length} yeni kayıt bulundu, senkronize ediliyor...`);
        
        let syncedCount = 0;
        let latestTime = syncFromTime;
        
        for (const row of result.rows) {
            try {
                await syncSingleRecord(tableName, row);
                syncedCount++;
                
                // En son kayıt zamanını takip et
                const recordTime = new Date(row.duzeltme_zaman);
                if (recordTime > latestTime) {
                    latestTime = recordTime;
                }
            } catch (error) {
                writeLog(`❌ ${tableName} SN=${row.sn} senkronize edilemedi: ${error.message}`);
            }
        }
        
        // Bu tablo için son senkronizasyon zamanını güncelle
        if (syncedCount > 0) {
            saveLastSyncTimeForTable(tableName, latestTime);
            writeLog(`✅ ${tableName} tablosu tamamlandı: ${syncedCount}/${result.rows.length} kayıt senkronize edildi.`);
        } else {
            writeLog(`⚠️ ${tableName} tablosunda hiçbir kayıt senkronize edilemedi.`);
        }
        
    } catch (error) {
        writeLog(`❌ ${tableName} senkronizasyon hatası: ${error.message}`);
        throw error;
    }
}

/**
 * Tek bir kaydı senkronize et
 */
async function syncSingleRecord(tableName, record) {
    if (!mysqlConnection) {
        await initMysqlConnection();
    }
    
    // Kayıt zaten var mı kontrol et
    const exists = await checkIfRecordExists(tableName, sanitizeValue(record.sn));
    if (exists) {
        writeLog(`${tableName} tablosunda SN=${sanitizeValue(record.sn)} zaten mevcut, atlanıyor...`);
        return;
    }
    
    switch (tableName) {
        case 'talep':
            await syncTalepRecord(record);
            break;
        case 'talep_detay':
            await syncTalepDetayRecord(record);
            break;
        case 'talep_detay_silinmis':
            await syncTalepDetaySilinmisRecord(record);
            break;
        case 'talep_dogrudan_degisiklik':
            await syncTalepDogrudan_DegisiklikRecord(record);
            break;
        case 'talep_hareket':
            await syncTalepHareketRecord(record);
            break;
        case 'talep_hareket_silinmis':
            await syncTalepHareketSilinmisRecord(record);
            break;
        case 'talep_silinmis':
            await syncTalepSilinmisRecord(record);
            break;
        case 'talep_silme_istegi':
            await syncTalepSilmeIstegiRecord(record);
            break;
        case 'talep_uzerine_alma_hareket':
            await syncTalepUzerineAlmaHareketRecord(record);
            break;
    }
}

/**
 * MySQL'de kayıt var mı kontrol et
 */
async function checkIfRecordExists(tableName, sn) {
    try {
        writeLog(`Kontrol ediliyor: ${tableName} tablosunda SN=${sn}`);
        const [rows] = await mysqlConnection.execute(
            `SELECT 1 FROM ${tableName} WHERE sn = ? LIMIT 1`,
            [sn]
        );
        const exists = rows.length > 0;
        writeLog(`Kontrol sonucu: ${tableName} SN=${sn} -> ${exists ? 'MEVCUT' : 'YOK'}`);
        return exists;
    } catch (error) {
        writeLog(`Kayıt kontrol hatası (${tableName}, SN: ${sn}): ${error.message}`);
        return false; // Hata durumunda false dön, INSERT denensin
    }
}

/**
 * Talep kaydını senkronize et
 */
async function syncTalepRecord(record) {
    const query = `
        INSERT INTO talep (
            sn, konu, detay, talep_eden, kullanici, sube, cari_kod, borclanacak_cari_kod,
            hizmet_kod, stok_kod, gizli, tamamlandi, tamamlanma_zaman, bekle, gorev_sn,
            dogrudan, nakit_odenen, bankadan_odenen, evrakla_odenen, kk_odenen, acik_odenen,
            dogrudan_odenen_cari_kod, fatura_durumu, kk_odenen_kk_kod, odenen_banka_kod,
            dogrudan_miktar, dogrudan_birim, dogrudan_ilgili_alim, kilometre, dogrudan_fatura_no,
            dogrudan_onaylayan, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.konu), sanitizeNotNull(record.detay, ''), sanitizeValue(record.talep_eden), sanitizeValue(record.kullanici),
        sanitizeValue(record.sube), sanitizeValue(record.cari_kod), sanitizeValue(record.borclanacak_cari_kod), sanitizeValue(record.hizmet_kod),
        sanitizeValue(record.stok_kod), sanitizeValue(record.gizli), sanitizeValue(record.tamamlandi), sanitizeNotNull(record.tamamlanma_zaman, '1970-01-01 00:00:00'),
        sanitizeValue(record.bekle), sanitizeValue(record.gorev_sn), sanitizeValue(record.dogrudan), sanitizeValue(record.nakit_odenen),
        sanitizeValue(record.bankadan_odenen), sanitizeValue(record.evrakla_odenen), sanitizeValue(record.kk_odenen), sanitizeValue(record.acik_odenen),
        sanitizeValue(record.dogrudan_odenen_cari_kod), sanitizeValue(record.fatura_durumu), sanitizeValue(record.kk_odenen_kk_kod),
        sanitizeValue(record.odenen_banka_kod), sanitizeValue(record.dogrudan_miktar), sanitizeValue(record.dogrudan_birim),
        sanitizeValue(record.dogrudan_ilgili_alim), sanitizeValue(record.kilometre), sanitizeValue(record.dogrudan_fatura_no),
        sanitizeNotNull(record.dogrudan_onaylayan, ''), sanitizeValue(record.yaratma_zaman), sanitizeValue(record.duzeltme_zaman)
    ]);
    
    writeLog(`Talep senkronize edildi: ${sanitizeValue(record.sn)} - ${sanitizeValue(record.konu)?.substring(0, 50)}...`);
}

/**
 * Talep detay kaydını senkronize et
 */
async function syncTalepDetayRecord(record) {
    const query = `
        INSERT INTO talep_detay (
            sn, talep_sn, aciklama, miktar, ilk_miktar, birim, termin_zaman,
            onay, onay_zaman, onay_kullanici, isleme_alindi, islem_zaman, islem_kullanici,
            tamamlandi, tamamlandi_zaman, iptal, iptal_kullanici, beklemede, kullanici,
            ilgili_alim, ilgili_alim_2, hizmet_kod, stok_kod, kilometre, teslim_kabul,
            teslim_kabul_adet, teslim_kabul_tarih, teslim_kabul_kullanici,
            yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.talep_sn), sanitizeValue(record.aciklama), sanitizeNotNull(record.miktar, 0), sanitizeNotNull(record.ilk_miktar, 0),
        sanitizeValue(record.birim), sanitizeValue(record.termin_zaman), sanitizeValue(record.onay), sanitizeValue(record.onay_zaman), sanitizeNotNull(record.onay_kullanici, ''),
        sanitizeValue(record.isleme_alindi), sanitizeValue(record.islem_zaman), sanitizeNotNull(record.islem_kullanici, ''), sanitizeValue(record.tamamlandi),
        sanitizeNotNull(record.tamamlandi_zaman, '1970-01-01 00:00:00'), sanitizeValue(record.iptal), sanitizeNotNull(record.iptal_kullanici, ''), sanitizeValue(record.beklemede),
        sanitizeNotNull(record.kullanici, ''), sanitizeNotNull(record.ilgili_alim, ''), sanitizeValue(record.ilgili_alim_2), sanitizeNotNull(record.hizmet_kod, ''),
        sanitizeValue(record.stok_kod), sanitizeValue(record.kilometre), sanitizeValue(record.teslim_kabul), sanitizeValue(record.teslim_kabul_adet),
        sanitizeNotNull(record.teslim_kabul_tarih, '1970-01-01 00:00:00'), sanitizeNotNull(record.teslim_kabul_kullanici, ''), sanitizeValue(record.yaratma_zaman),
        sanitizeValue(record.duzeltme_zaman)
    ]);
    
    writeLog(`Talep detay senkronize edildi: ${sanitizeValue(record.sn)} - Talep: ${sanitizeValue(record.talep_sn)}`);
}

/**
 * Talep hareket kaydını senkronize et
 */
async function syncTalepHareketRecord(record) {
    const query = `
        INSERT INTO talep_hareket (
            sn, talep_sn, talep_detay_sn, nakit_odenen, bankadan_odenen,
            evrakla_odenen, kk_odenen, acik_odenen, kapali_mi, kullanici,
            yaratma_zaman, duzeltme_zaman, talep_fatura_bilgisi, nakit_odenen_cari_kod,
            fatura_durumu, fatura_no, kk_odenen_kk_kod, goruldu, odenen_banka_kod,
            kdv_oran, tevkifat_oran, iskonto_oran, fatura_tarih
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.talep_sn), sanitizeValue(record.talep_detay_sn),
        sanitizeValue(record.nakit_odenen), sanitizeValue(record.bankadan_odenen), sanitizeValue(record.evrakla_odenen),
        sanitizeValue(record.kk_odenen), sanitizeValue(record.acik_odenen), sanitizeValue(record.kapali_mi), 
        sanitizeValue(record.kullanici), sanitizeValue(record.yaratma_zaman), sanitizeValue(record.duzeltme_zaman),
        sanitizeNotNull(record.talep_fatura_bilgisi, ''), sanitizeValue(record.nakit_odenen_cari_kod),
        sanitizeValue(record.fatura_durumu), sanitizeValue(record.fatura_no), sanitizeValue(record.kk_odenen_kk_kod),
        sanitizeValue(record.goruldu) || 0, sanitizeValue(record.odenen_banka_kod), sanitizeValue(record.kdv_oran),
        sanitizeValue(record.tevkifat_oran), sanitizeValue(record.iskonto_oran), sanitizeValue(record.fatura_tarih)
    ]);
    
    writeLog(`Talep hareket senkronize edildi: ${sanitizeValue(record.sn)} - Talep: ${sanitizeValue(record.talep_sn)}`);
}

/**
 * Talep detay silinmiş kaydını senkronize et
 */
async function syncTalepDetaySilinmisRecord(record) {
    const query = `
        INSERT INTO talep_detay_silinmis (
            sn, talep_sn, aciklama, miktar, ilk_miktar, birim, termin_zaman,
            onay, onay_zaman, onay_kullanici, isleme_alindi, islem_zaman, islem_kullanici,
            tamamlandi, tamamlandi_zaman, iptal, iptal_kullanici, beklemede, kullanici,
            ilgili_alim, ilgili_alim_2, hizmet_kod, stok_kod, kilometre, teslim_kabul,
            teslim_kabul_adet, teslim_kabul_tarih, teslim_kabul_kullanici,
            yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.talep_sn), sanitizeValue(record.aciklama), sanitizeNotNull(record.miktar, 0), sanitizeNotNull(record.ilk_miktar, 0),
        sanitizeValue(record.birim), sanitizeValue(record.termin_zaman), sanitizeValue(record.onay), sanitizeValue(record.onay_zaman), sanitizeNotNull(record.onay_kullanici, ''),
        sanitizeValue(record.isleme_alindi), sanitizeValue(record.islem_zaman), sanitizeNotNull(record.islem_kullanici, ''), sanitizeValue(record.tamamlandi),
        sanitizeNotNull(record.tamamlandi_zaman, '1970-01-01 00:00:00'), sanitizeValue(record.iptal), sanitizeNotNull(record.iptal_kullanici, ''), sanitizeValue(record.beklemede),
        sanitizeNotNull(record.kullanici, ''), sanitizeNotNull(record.ilgili_alim, ''), sanitizeValue(record.ilgili_alim_2), sanitizeNotNull(record.hizmet_kod, ''),
        sanitizeValue(record.stok_kod), sanitizeValue(record.kilometre), sanitizeValue(record.teslim_kabul), sanitizeValue(record.teslim_kabul_adet),
        sanitizeNotNull(record.teslim_kabul_tarih, '1970-01-01 00:00:00'), sanitizeNotNull(record.teslim_kabul_kullanici, ''), sanitizeValue(record.yaratma_zaman),
        sanitizeValue(record.duzeltme_zaman)
    ]);
    
    writeLog(`Talep detay silinmiş senkronize edildi: ${sanitizeValue(record.sn)} - Talep: ${sanitizeValue(record.talep_sn)}`);
}

/**
 * Talep doğrudan değişiklik kaydını senkronize et
 */
async function syncTalepDogrudan_DegisiklikRecord(record) {
    const query = `
        INSERT INTO talep_dogrudan_degisiklik (
            sn, talep_sn, eski_miktar, yeni_miktar, eski_birim, yeni_birim,
            kullanici, yaratma_zaman, duzeltme_zaman, aciklama, onay, silinsin,
            yeni_nakit, yeni_bankadan, yeni_evrak, yeni_kk, yeni_kk_kod, 
            yeni_banka_kod, yeni_sube, yeni_acik, talep_hareket_sn, 
            yeni_kdv, yeni_tevkifat
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.talep_sn), sanitizeValue(record.eski_miktar), sanitizeValue(record.yeni_miktar),
        sanitizeValue(record.eski_birim), sanitizeValue(record.yeni_birim), sanitizeValue(record.kullanici),
        sanitizeValue(record.yaratma_zaman), sanitizeValue(record.duzeltme_zaman), sanitizeNotNull(record.aciklama, ''),
        sanitizeValue(record.onay), sanitizeValue(record.silinsin), sanitizeValue(record.yeni_nakit),
        sanitizeValue(record.yeni_bankadan), sanitizeValue(record.yeni_evrak), sanitizeValue(record.yeni_kk),
        sanitizeValue(record.yeni_kk_kod), sanitizeValue(record.yeni_banka_kod), sanitizeValue(record.yeni_sube),
        sanitizeValue(record.yeni_acik), sanitizeValue(record.talep_hareket_sn), sanitizeValue(record.yeni_kdv),
        sanitizeValue(record.yeni_tevkifat)
    ]);
    
    writeLog(`Talep doğrudan değişiklik senkronize edildi: ${sanitizeValue(record.sn)} - Talep: ${sanitizeValue(record.talep_sn)}`);
}

/**
 * Talep hareket silinmiş kaydını senkronize et
 */
async function syncTalepHareketSilinmisRecord(record) {
    const query = `
        INSERT INTO talep_hareket_silinmis (
            sn, talep_sn, talep_detay_sn, nakit_odenen, bankadan_odenen,
            evrakla_odenen, kk_odenen, acik_odenen, kapali_mi, kullanici,
            yaratma_zaman, duzeltme_zaman, talep_fatura_bilgisi, nakit_odenen_cari_kod,
            fatura_durumu, fatura_no, kk_odenen_kk_kod, goruldu, odenen_banka_kod,
            kdv_oran, tevkifat_oran, iskonto_oran, fatura_tarih
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.talep_sn), sanitizeValue(record.talep_detay_sn),
        sanitizeValue(record.nakit_odenen), sanitizeValue(record.bankadan_odenen), sanitizeValue(record.evrakla_odenen),
        sanitizeValue(record.kk_odenen), sanitizeValue(record.acik_odenen), sanitizeValue(record.kapali_mi), 
        sanitizeValue(record.kullanici), sanitizeValue(record.yaratma_zaman), sanitizeValue(record.duzeltme_zaman),
        sanitizeNotNull(record.talep_fatura_bilgisi, ''), sanitizeValue(record.nakit_odenen_cari_kod),
        sanitizeValue(record.fatura_durumu), sanitizeValue(record.fatura_no), sanitizeValue(record.kk_odenen_kk_kod),
        sanitizeValue(record.goruldu) || 0, sanitizeValue(record.odenen_banka_kod), sanitizeValue(record.kdv_oran),
        sanitizeValue(record.tevkifat_oran), sanitizeValue(record.iskonto_oran), sanitizeValue(record.fatura_tarih)
    ]);
    
    writeLog(`Talep hareket silinmiş senkronize edildi: ${sanitizeValue(record.sn)} - Talep: ${sanitizeValue(record.talep_sn)}`);
}

/**
 * Talep silinmiş kaydını senkronize et
 */
async function syncTalepSilinmisRecord(record) {
    const query = `
        INSERT INTO talep_silinmis (
            sn, konu, detay, talep_eden, kullanici, sube, cari_kod, borclanacak_cari_kod,
            hizmet_kod, stok_kod, gizli, tamamlandi, tamamlanma_zaman, bekle, gorev_sn,
            dogrudan, nakit_odenen, bankadan_odenen, evrakla_odenen, kk_odenen, acik_odenen,
            dogrudan_odenen_cari_kod, fatura_durumu, kk_odenen_kk_kod, odenen_banka_kod,
            dogrudan_miktar, dogrudan_birim, dogrudan_ilgili_alim, kilometre, dogrudan_fatura_no,
            dogrudan_onaylayan, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.konu), sanitizeNotNull(record.detay, ''), sanitizeValue(record.talep_eden), sanitizeValue(record.kullanici),
        sanitizeValue(record.sube), sanitizeValue(record.cari_kod), sanitizeValue(record.borclanacak_cari_kod), sanitizeValue(record.hizmet_kod),
        sanitizeValue(record.stok_kod), sanitizeValue(record.gizli), sanitizeValue(record.tamamlandi), sanitizeNotNull(record.tamamlanma_zaman, '1970-01-01 00:00:00'),
        sanitizeValue(record.bekle), sanitizeValue(record.gorev_sn), sanitizeValue(record.dogrudan), sanitizeValue(record.nakit_odenen),
        sanitizeValue(record.bankadan_odenen), sanitizeValue(record.evrakla_odenen), sanitizeValue(record.kk_odenen), sanitizeValue(record.acik_odenen),
        sanitizeValue(record.dogrudan_odenen_cari_kod), sanitizeValue(record.fatura_durumu), sanitizeValue(record.kk_odenen_kk_kod),
        sanitizeValue(record.odenen_banka_kod), sanitizeValue(record.dogrudan_miktar), sanitizeValue(record.dogrudan_birim),
        sanitizeValue(record.dogrudan_ilgili_alim), sanitizeValue(record.kilometre), sanitizeValue(record.dogrudan_fatura_no),
        sanitizeNotNull(record.dogrudan_onaylayan, ''), sanitizeValue(record.yaratma_zaman), sanitizeValue(record.duzeltme_zaman)
    ]);
    
    writeLog(`Talep silinmiş senkronize edildi: ${sanitizeValue(record.sn)} - ${sanitizeValue(record.konu)?.substring(0, 50)}...`);
}

/**
 * Talep silme isteği kaydını senkronize et
 */
async function syncTalepSilmeIstegiRecord(record) {
    const query = `
        INSERT INTO talep_silme_istegi (
            sn, talep_sn, istegi_yapan, neden, durum, yanitlayan, yanit,
            yanitlama_zaman, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.talep_sn), sanitizeValue(record.istegi_yapan), sanitizeValue(record.neden),
        sanitizeValue(record.durum), sanitizeValue(record.yanitlayan), sanitizeValue(record.yanit), sanitizeValue(record.yanitlama_zaman),
        sanitizeValue(record.yaratma_zaman), sanitizeValue(record.duzeltme_zaman)
    ]);
    
    writeLog(`Talep silme isteği senkronize edildi: ${sanitizeValue(record.sn)} - Talep: ${sanitizeValue(record.talep_sn)}`);
}

/**
 * Talep üzerine alma hareket kaydını senkronize et
 */
async function syncTalepUzerineAlmaHareketRecord(record) {
    const query = `
        INSERT INTO talep_uzerine_alma_hareket (
            sn, talep_sn, eski_kullanici, yeni_kullanici, degistiren,
            neden, yaratma_zaman, duzeltme_zaman
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await mysqlConnection.execute(query, [
        sanitizeValue(record.sn), sanitizeValue(record.talep_sn), sanitizeValue(record.eski_kullanici), sanitizeValue(record.yeni_kullanici),
        sanitizeValue(record.degistiren), sanitizeValue(record.neden), sanitizeValue(record.yaratma_zaman), sanitizeValue(record.duzeltme_zaman)
    ]);
    
    writeLog(`Talep üzerine alma hareket senkronize edildi: ${sanitizeValue(record.sn)} - Talep: ${sanitizeValue(record.talep_sn)}`);
}

/**
 * HTTP Request Body'yi oku
 */
function getRequestBody(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(JSON.parse(body));
            } catch (error) {
                resolve({});
            }
        });
        req.on('error', reject);
    });
}

/**
 * Talep onay endpoint'i
 */
async function handleTalepOnay(req, res) {
    try {
        const body = await getRequestBody(req);
        const { talep_detay_sn } = body;
        
        if (!talep_detay_sn) {
            res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({ 
                success: false, 
                error: 'talep_detay_sn gerekli' 
            }));
            return;
        }

        // PostgreSQL bağlantısı ile onay işlemi
        const client = await pool.connect();
        
        try {
            await client.query('BEGIN');
            
            // 1. talep_detay tablosunda onay = true yap
            const updateDetayQuery = `
                UPDATE talep_detay 
                SET onay = TRUE, 
                    onay_zaman = NOW(), 
                    onay_kullanici = 'eguler'
                WHERE sn = $1
                RETURNING talep_sn
            `;
            const detayResult = await client.query(updateDetayQuery, [talep_detay_sn]);
            
            if (detayResult.rows.length === 0) {
                throw new Error('Talep detayı bulunamadı');
            }
            
            const talep_sn = detayResult.rows[0].talep_sn;
            
            // 2. talep tablosunda dogrudan_onaylayan = 'eguler' yap
            const updateTalepQuery = `
                UPDATE talep 
                SET dogrudan_onaylayan = 'eguler'
                WHERE sn = $1
            `;
            await client.query(updateTalepQuery, [talep_sn]);
            
            await client.query('COMMIT');
            
            writeLog(`Talep onaylandı: Detay SN=${talep_detay_sn}, Talep SN=${talep_sn}, Onaylayan=eguler`);
            
            res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({
                success: true,
                message: 'Talep başarıyla onaylandı',
                data: {
                    talep_detay_sn: talep_detay_sn,
                    talep_sn: talep_sn,
                    onaylayan: 'eguler'
                }
            }));
            
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
        
    } catch (error) {
        writeLog('Talep onay hatası:', error);
        res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            success: false,
            error: error.message
        }));
    }
}

/**
 * HTTP Server oluştur
 */
function createHttpServer() {
    httpServer = http.createServer(async (req, res) => {
        // CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        
        if (req.method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }
        
        const parsedUrl = url.parse(req.url, true);
        const path = parsedUrl.pathname;
        
        // Routes
        if (path === '/talep/onay' && req.method === 'POST') {
            await handleTalepOnay(req, res);
        } else if (path === '/health' && req.method === 'GET') {
            // Health check endpoint
            res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({
                status: 'ok',
                service: 'MEG Talep Sync',
                timestamp: new Date().toISOString()
            }));
        } else if (path === '/' && req.method === 'GET') {
            // Ana sayfa
            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
                <html>
                    <head><title>MEG Talep Sync API</title></head>
                    <body>
                        <h1>MEG Talep Sync API</h1>
                        <h2>Endpoints:</h2>
                        <ul>
                            <li><strong>POST /talep/onay</strong> - Talep onaylama</li>
                            <li><strong>GET /health</strong> - Sistem durumu</li>
                        </ul>
                        <h3>Örnek kullanım:</h3>
                        <pre>
curl -X POST http://localhost:3080/talep/onay \\
  -H "Content-Type: application/json" \\
  -d '{"talep_detay_sn": 123}'
                        </pre>
                    </body>
                </html>
            `);
        } else {
            // 404
            res.writeHead(404, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({
                success: false,
                error: 'Endpoint bulunamadı'
            }));
        }
    });
    
    httpServer.listen(HTTP_PORT, () => {
        writeLog(`HTTP API Server başlatıldı: http://localhost:${HTTP_PORT}`);
        writeLog('Kullanılabilir endpoints:');
        writeLog(`  POST http://localhost:${HTTP_PORT}/talep/onay`);
        writeLog(`  GET  http://localhost:${HTTP_PORT}/health`);
    });
}

/**
 * Ana döngü
 */
async function startPolling() {
    writeLog(`Polling başlatıldı. Kontrol aralığı: ${POLL_INTERVAL / 1000} saniye`);
    
    while (isRunning) {
        try {
            await checkAndSyncChanges();
        } catch (error) {
            writeLog('Polling döngüsü hatası:', error);
        }
        
        // Belirtilen süre kadar bekle
        await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL));
    }
}

/**
 * Graceful shutdown
 */
async function shutdown() {
    writeLog('Sistem kapatılıyor...');
    isRunning = false;
    
    try {
        if (httpServer) {
            httpServer.close();
            writeLog('HTTP Server kapatıldı.');
        }
        
        if (mysqlConnection) {
            await mysqlConnection.end();
            writeLog('MySQL bağlantısı kapatıldı.');
        }
        
        await pool.end();
        writeLog('PostgreSQL bağlantı havuzu kapatıldı.');
        
    } catch (error) {
        writeLog('Kapatma sırasında hata:', error);
    }
    
    process.exit(0);
}

/**
 * Ana fonksiyon
 */
async function main() {
    try {
        const isFirstRun = !fs.existsSync(LAST_SYNC_FILE);
        
        writeLog('MEG Talep Polling Senkronizasyon Servisi başlatılıyor...');
        
        if (isFirstRun) {
            writeLog('==========================================');
            writeLog('İLK ÇALIŞMA: 25.08.2025 ve sonrası TÜM kayıtlar senkronize edilecek');
            writeLog('Bu işlem biraz zaman alabilir...');
            writeLog('==========================================');
        }
        
        // MySQL bağlantısını başlat
        await initMysqlConnection();
        
        // Şema karşılaştırması yap
        await checkTableSchemas();
        
        // HTTP Server'ı başlat
        createHttpServer();
        
        // İlk senkronizasyonu çalıştır
        await checkAndSyncChanges();
        
        if (isFirstRun) {
            writeLog('==========================================');
            writeLog('İLK ÇALIŞMA TAMAMLANDI!');
            writeLog('Bundan sonra 30 saniyede bir yeni değişiklikler kontrol edilecek.');
            writeLog('==========================================');
        }
        
        // Polling döngüsünü başlat
        await startPolling();
        
    } catch (error) {
        writeLog('Başlatma hatası:', error);
        process.exit(1);
    }
}

// Signal handlers
process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

// Çalıştır
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    main,
    checkAndSyncChanges,
    getLastSyncTimeForTable,
    saveLastSyncTimeForTable
};