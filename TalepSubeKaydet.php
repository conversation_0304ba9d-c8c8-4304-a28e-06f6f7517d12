<?php
require_once('yetki.php');
require_once("PGConnect.php");
require_once("logYaz.php");


function eskiSubeVer($gelenKayitNo){

    global $db; 
    
    $sorguCumle = "SELECT sube FROM talep WHERE sn=$gelenKayitNo;";

    //Sorgu çalıştır.
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        $donenDeger = false;
    } 

    while($row = pg_fetch_row($ret))   {
        $donenDeger = $row[0];	   
    }  

    return $donenDeger;

}


$gelenKayitNo = $_POST['talepSn'];
$gelenSube = $_POST['gelenSube']; 


$eskiSubeNedir= eskiSubeVer($gelenKayitNo);

$sorguCumle = "UPDATE talep SET sube = '$gelenSube'
                            WHERE sn=$gelenKayitNo;";




$donenDeger = true;


//Sorgu çalıştır.
$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    $donenDeger = false;
} 

if($donenDeger==true){
    logYaz($gelenKayitNo . ' Numaralı Talep üzerindeki varolan; '.$eskiSubeNedir . ' şubesi, '. $gelenSube. ' olarak değiştirildi!');
    echo "Ok";

}  else echo "Talep kaydına ulaşılamadı!";

//echo $sorguCumle;

pg_close($db);
?>