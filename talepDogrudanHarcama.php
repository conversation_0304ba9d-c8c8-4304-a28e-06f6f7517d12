<?php
require('yetki.php');
$ekranKod = 61;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>
<script src="jquery-3.6.0.js"></script>

<style style="text/css">
    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }

            
/** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 60%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}















</style>

<?php
require('siteBaslik.html');
require('menuKaynak.php');
require('blink.html');
?>


<script type="text/javascript">




function eFaturaListele(sender){
	
	//var tarih1 = document.getElementById("eFaturaTarih1").value;
	//var tarih2 = document.getElementById("eFaturaTarih2").value;
	/*
	if(tarih1=="" || tarih2==""){
		alert("E-Fatura Tarih aralığı mutlaka belirlenmelidir!");
		return false;
	}
	*/
	
	//gelenFaturaTutar = gelenFaturaTutar;
	
	//İşlem devam ediyor...
	var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
      document.body.style.cursor  = 'default';
	  
	  try{
		  document.getElementById("myInputHepsi2").focus();
		}catch{
		  //
		}
	  
    }
    };
	
	document.body.style.cursor  = 'wait';
	
	document.getElementById('baslik').innerHTML ="E-Fatura Listesi";
	xhttp.open("POST", "EFaturaListesiVerTarihsiz.php", true);
	xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xhttp.send("hedefTransfer=eFaturaDonus");
	
	// Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
	
	
}


function eFaturaDonus(eFaturaNo, faturaTutar, faturaCariAd){
	
	document.getElementById("faturaNo").value = eFaturaNo;
	
	
	
	modalKapat();
	
	
	
}



function temizle(sender){
	
	sender.style.display = "none";
	
	document.getElementById("borclanacakCariKod").value="";
	document.getElementById("borclanacakCariAd").innerHTML = "";
	
	
	
	sender.style.display = "";
	
	
}


function yeniSatirGiris(tabloAd, hedefFonksiyonAd){
	//alert(kullanici);
	
	var yeniAd = prompt("Yeni ilgili alım girin", "");
	
	if (yeniAd == null) {
		alert("İşlem iptal edildi");
		return;
	}
	//console.log(tabloAd);
	//console.log(hedefFonksiyonAd);
	//console.log('<label onclick="'+ hedefFonksiyonAd +'(\''+ yeniAd +'\');">'+ yeniAd +'</label>');
	yeniAd = yeniAd.toLocaleUpperCase("tr");
	
	//Buradan sonra yeni giriş yapılıyor. 
	//Kum saati
    document.body.style.cursor  = 'wait';

    $.ajax({
        method: "POST",
        url: "YeniIlgiAlimKaydet.php",
        async: false,
        data: { alimAd: yeniAd
            }        
    })
        .done(function( response ) { 

            
            response = response.trim();          

            

            if(response.indexOf("Okkk") !== -1){                    
                donenDeger = true; 
                //Normal Saat
                document.body.style.cursor  = 'default';  
                alert("İşlem tamam. Yeni giriş eklendi");
				const yeniKodP = response.split("-");
				yeniKod = yeniKodP[1];
                
                            
            } else{
                //Normal Saat
                document.body.style.cursor  = 'default';
                alert("Hata: " + response);
                console.log(response);
                //console.log("Hata");           
                donenDeger = false;
                //return false;                
            }   
                    
            
        });
		
	if(donenDeger == true ){ //Tabloya satır eklenecek!
	
		let tableRef = document.getElementById(tabloAd);
		let newRow = tableRef.insertRow(-1);
		//newRow.innerHTML='<tr onclick="alert(15);"/>';
		
		let newCell = newRow.insertCell(0);		
		
		newCell.style.textAlign = "left";
		newCell.style.verticalAlign = "middle";
		newCell.innerHTML='<label onclick="'+ hedefFonksiyonAd +'(\''+ yeniKod +'\', \''+ yeniAd +'\');">'+ yeniAd +'</label>';
		
		//-------------
		let newCell1 = newRow.insertCell(0);		
		
		newCell1.style.textAlign = "left";
		newCell1.style.verticalAlign = "middle";
		newCell1.innerHTML='<label onclick="'+ hedefFonksiyonAd +'(\''+ yeniKod +'\', \''+ yeniAd +'\');">'+ yeniKod +'</label>';
		
		
		//let newText = document.createTextNode(yeniAd);
		//newCell.appendChild(newText);
	
	
		
	}
	
	
	
	
	
}






function yeniStokEkle(sender){
	
	document.getElementById("stokEklemeForm").submit();
	
}





function veriTransferBanka(gelenBanka){
    //Dönen değeri yazıyor...    
    document.getElementById("bankaKod").value=gelenBanka;   
    modalKapat(); 
}


function veriTransfer(gelenCariKod, gelenCariAd, gelenBlKodu, gelenAdres){ //Cari liste seçili ise bu rutin çalışacak
    document.getElementById("cariKod").value = gelenCariKod;
    document.getElementById("cariAd").innerHTML = gelenCariAd;    

    modalKapat();     
}


function veriTransfer3(gelenKKNo, gelenBankaAd, gelenBakiye, gelenHesapKesimTarihi, kkBlKodu, kkTanim, kkBilgi){//Kredi kartı

    document.getElementById("kkKod").value = kkBilgi;
    modalKapat();     

}





function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
    document.body.style.cursor  = 'default';    
}



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
    document.body.style.cursor  = 'default';
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}




function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function bankaKodSifirla(){
	
	var banka = 0;
	banka = document.getElementById("banka").value; 
	
	if(banka==0){
		document.getElementById("bankaKod").value="";		
	}
	
}



function krediKartSifirla(){
	
	var kK = 0;
	kK = document.getElementById("kk").value; 
	
	if(kK==0){
		document.getElementById("kkKod").value="";		
	}
	
}

function eFaturaOncedenKullanilmisMi(galenFaturaNo){
	
	
	$.ajax({
        method: "POST",
        url: "EfaturaDogrudanKullanilmisMi.php",
        async: false,
        data: { eFaturaNo: galenFaturaNo
            }        
    })
        .done(function( response ) { 
            
            response = response.trim();                      

            if(response.indexOf("Okkk") !== -1){                    
                donenDeger = false;                
                
                            
            } else{               
                alert("Hata: " + response);
                console.log(response);
                //console.log("Hata");           
                donenDeger = true;
                //return false;                
            }   
                    
            
        });
	
	
	
	return donenDeger;
}


function veriKontrol(){

    var hataVar = false;

    var nakit = 0;
    var banka = 0;
    var evrak = 0;
    var kK = 0;
    var acik = 0;


    var nakit = document.getElementById("nakit").value; 
    var banka = document.getElementById("banka").value; 
    var evrak = document.getElementById("evrak").value; 
    var kK = document.getElementById("kk").value; 
    var acik = document.getElementById("acik").value; 
	var konu = document.getElementById("konu").value; 

    var cariKod = document.getElementById("cariKod").value;
    var kKKod = document.getElementById("kkKod").value;
    var bankaKod = document.getElementById("bankaKod").value;
	
	var hizmetKod = document.getElementById("hizmetKod").value;
	var stokKod = document.getElementById("stokKod").value;
	
	var miktar = document.getElementById("miktar").value;
	var birim = document.getElementById("birim").value;
	var ilgiliAlim = document.getElementById("ilgiliAlim").value;
	
	var borclanacakCariKod = document.getElementById("borclanacakCariKod").value;
	
	var kilometre = parseInt(document.getElementById("kilometre").value);
	
	
	var dogrudanFaturaNo = document.getElementById("faturaNo").value.trim();
	var dogrudanFaturaTarihi = document.getElementById("faturaTarihi").value;
	
	
	if(dogrudanFaturaNo==""){
		alert("Fatura Numarası boş geçilemez!");
		return false;
	}	
		
	
	if(dogrudanFaturaNo!="-1"){
		
		if(eFaturaOncedenKullanilmisMi(dogrudanFaturaNo)){
			return false;
		}
	}
	
	
	if(dogrudanFaturaTarihi==""){
		alert("Fatura/Fiş Tarihi boş geçilemez!");
		return false;
	}
	
	
	
	
	
	
	const yakitStokKodlari = ['MGS0097', 'MGS0096', 'MGS0095', 'MGS0098']; //Dizel, benzin, lpg, Oto Elektrik
	
	
	if(yakitStokKodlari.indexOf(stokKod) != -1){ // is in array	
	
		if(kilometre=="" || kilometre==0 || isNaN(kilometre)){
			alert("Yakıt alımında mutlaka Kilometre yazılmalıdır!");
			return false;
		}
		
	} 
	
	
	//console.log(stokKod);
	//console.log(kilometre);
	
	//return false;


	
	
	
	
	
	if(cariKod==borclanacakCariKod){
		alert("Ödeme yapılan ve borçlandırılacak olan cari aynı olamaz!!");
		return false;
	}
	
	
	if(hizmetKod=="" && stokKod=="" ){
		alert("Stok veya Hizmet kodlarından en az birisi mutlaka seçilmiş olmalıdır!");
		return false;
	}
	
	if(stokKod!="" && (miktar=="" || birim=="---") ){ //Stok kodu seçilmiş demektir. 
	
		alert("Miktar ve Birim mutlaka seçilmiş olmalıdır!");
		return false;
	}	
	
	
	if(konu==""){
		alert("Konu mutlaka girilmiş olmalıdır!");
		return false;
	}
	
	
	if(ilgiliAlim==""){
		alert("Ne için alındığı mutlaka girilmiş olmalıdır!");
		return false;
	}
	

	
	
	

    if(cariKod==""){
        alert("Cari kod SEÇİLMELİDİR!\nİşlem iptal edilecek.");
        return false;
    }

    if(banka>0 && bankaKod==""){
        alert("Banka ödemesi varsa mutlaka banka SEÇİLMELİDİR!\nİşlem iptal edilecek.");
        return false;
    }

    if(kK>0 && kKKod==""){
         alert("KK ödeme varsa mutlaka KK kod SEÇİLMELİDİR!\nİşlem iptal edilecek.");
         return false;
    }

    if((nakit+banka+evrak+kK+acik)==0){
        alert("En az bir harcama bilgisi girilmelidir!\nİşlem iptal edilecek.");
        hataVar = true;
    }
	
	//console.log(nakit+banka+evrak+kK+acik);


    if(document.getElementById("sube").value=="---"){
        alert("Şube girilmelidir!\nİşlem iptal edilecek.");
        hataVar = true;
    }

    if(document.getElementById("faturaDurumu").value=="---"){
        alert("Fatura Durumu girilmelidir!\nİşlem iptal edilecek.");
        hataVar = true;
    }

   



    if(hataVar){
        alert("Veri giriş hatası!\nİşlem iptal edildi.");
        return false;


    }else {
        //document.getElementById("kisiBilgileri").value = kisilerListesi;
        //document.getElementById("gonderTus").disabled=true;    
        //document.getElementById("gonderTus").disabled = true; 
        document.getElementById("gonderTus").style.display = "none";
        document.body.style.cursor  = 'wait';
        return true;
    }




}


function veriTransferCari(gelenKullanici, gelenCariKod, gelenCariAd){	
	
    document.getElementById("talepEden").value = gelenKullanici;
	
	//document.getElementById("cariKod").value = gelenCariKod;
    //document.getElementById("kullaniciAd").innerHTML = gelenCariAd;
    //document.getElementById("cariAd2").value = gelenCariAd;    
    
    modalKapat();     
}







function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}

//Tüm kolonlarda arama

function searchTableColumns(gelenTablo, gelenAlan,gelenHedefAlan) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      var bulunanKayitAdet = 0; //Kayıt sayısı için
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
            bulunanKayitAdet++;
        } else {
            tr[i].style.display = "none";
        }
      }   

      //Kayıt adedi yazılıyor.

      if(bulunanKayitAdet > 0){
          document.getElementById(gelenHedefAlan).innerHTML = bulunanKayitAdet + " Adet kayıt listelendi"; 
      }else{
          document.getElementById(gelenHedefAlan).innerHTML = ""; 
      }





}

function kkKontrol(){
    if(document.getElementById("kk").value==0) document.getElementById("kkKod").value="";
    //console.log(document.getElementById("kk").value);

}

function modalGoster(gelenModelKod) { 

    //Nakit kısmı boş ise cari kod açılmaz!
    //Sonradan sürekli giriş yapılsın dendi ve kontrol kalktır.
    //if(document.getElementById("nakit").value==0 && document.getElementById("acik").value==0) return false; 

    if(document.getElementById("kk").value==0 && gelenModelKod==2) return false; 


    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
    document.getElementById("icerik").innerHTML = this.responseText;
    document.body.style.cursor  = 'default';
	
	try{
	  document.getElementById("myInputHepsi2").focus();
	}catch{
	  //
	}	
	
	
    }
    };
    if(gelenModelKod==0){
        document.getElementById('baslik').innerHTML ="Cari Listesi";
        xhttp.open("POST", "CariVerAkinsoftdan.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==1){
        document.getElementById('baslik').innerHTML ="Talepler Listesi";
        xhttp.open("POST", "TalepIlgiVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        cariKod = document.getElementById("cariKod").value;
        //console.log(cariKod);
        xhttp.send("cariKod=" + cariKod);
    }else if(gelenModelKod==2){
        document.getElementById('baslik').innerHTML ="Kredi Kartları";
        xhttp.open("POST", "KrediKartlariVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==3){
        document.getElementById('baslik').innerHTML ="Kredi Ödemeleri";
        xhttp.open("POST", "KrediOdemeleriVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send();
    }else if(gelenModelKod==4){
        document.getElementById('baslik').innerHTML = "Hizmet Kartları";
        xhttp.open("POST", "HizmetKartVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("veriTransferAd=veriTransferHizmetKart");
    }else if(gelenModelKod==5){
        document.getElementById('baslik').innerHTML ="Banka Seçim";
        xhttp.open("POST", "BankaVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("hedefFonksiyon=veriTransferBanka");
    }else if(gelenModelKod==6){
        document.getElementById('baslik').innerHTML ="Stok Kartları";
        xhttp.open("POST", "StokKodVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("veriTransferAd=veriTransferStokKod");

    }else if(gelenModelKod==7){
        document.getElementById('baslik').innerHTML ="Kullanıcılar";
		xhttp.open("POST", "KullaniciVer.php", true);
		xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
		xhttp.send("veriTransferAd=veriTransferCari&sadeceBormeg=1");

    }else if(gelenModelKod==8){
        document.getElementById('baslik').innerHTML ="Ne için Alındı";
        xhttp.open("POST", "NeIcinAlindiVer.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("veriTransferAd=veriTransferNeIcinAlindi");

    }else if(gelenModelKod==9){
        document.getElementById('baslik').innerHTML ="Cari Listesi";
        xhttp.open("POST", "CariVerAkinsoftdan.php", true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("hedefTransfer=borclanacakCariTransfer");
    }

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}




function borclanacakCariTransfer(gelenCariKod, gelenCariAd, gelenBlKodu, gelenAdres){ //Cari liste seçili ise bu rutin çalışacak
    document.getElementById("borclanacakCariKod").value = gelenCariKod;
    document.getElementById("borclanacakCariAd").innerHTML = gelenCariAd;    

    modalKapat();     
}










function veriTransferNeIcinAlindi(gelenSn, gelenNeIcinAlindi){
	document.getElementById("ilgiliAlim").value = gelenSn; 
	
	document.getElementById("ilgiliAlimAd").innerHTML = gelenNeIcinAlindi; 
	document.getElementById("ilgiliAlimAd2").value = gelenNeIcinAlindi;
	
	console.log (gelenNeIcinAlindi);
	
    modalKapat(); 
	
}





function veriTransferHizmetKart(gelenHizmetKod, gelenHizmetAd){
    document.getElementById("hizmetKod").value = gelenHizmetKod;
    document.getElementById("hizmetAd").innerHTML = gelenHizmetAd;  
    document.getElementById("hizmetAd2").value = gelenHizmetAd;  
	
	//Stok bilgileri sıfırlanacak
	document.getElementById("stokKod").value = "";
    document.getElementById("stokAd").innerHTML = "";
    document.getElementById("stokAd2").value = "";
    
    modalKapat();     
}


function veriTransferStokKod(gelenStokBlKodu, gelenStokKod, gelenStokAd){
    document.getElementById("stokKod").value = gelenStokKod;
    document.getElementById("stokAd").innerHTML = gelenStokAd;
    document.getElementById("stokAd2").value = gelenStokAd;

    //Hizmet bilgilerini sıfırlıyor.
    document.getElementById("hizmetKod").value = "";
    document.getElementById("hizmetAd").innerHTML = "";
    document.getElementById("hizmetAd2").value = "";

    
    modalKapat();     
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>



<?php


function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}



function acikTalepDetayVarMi($talepSn){

    global $db;

    $sorgu="SELECT COUNT(*) FROM talep_detay WHERE talep_sn = $talepSn
                                                    AND iptal=false
                                                    AND tamamlandi=false;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Talep detay bulunamadı";
      exit;
   }  
   
   $donenDeger=true;
   
   while($row = pg_fetch_row($ret))
   {
	  if($row[0]>0) $donenDeger=true; else $donenDeger=false;	   
   }  

   return $donenDeger;
}




function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn=$gorevSn;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$row[0];	   
   }  

   return $liste;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }



function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}


function subeListesiVer(){
    
    global $aktifSirketBLKodu;
    global $aktifSirketDB;

    $donenListe=[];

    $sorguCumle = "SELECT SUBE_ADI FROM SUBE WHERE BLSRKODU=$aktifSirketBLKodu AND AKTIF = 1 ORDER BY SUBE_ADI;";

    try {	
        $dbh = new \PDO($aktifSirketDB , 'sysdba', 'masterkey');
        
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            array_push($donenListe, utf8MetinYap($row[0]));
        }
        $dbh = null;
        $ret = null;
        
        }
        catch (PDOException $e) {
            print "Hata!: " . $e->getMessage() . "<br/>";
            $dbh = null;
            $ret = null;
            die();
        }

    return $donenListe;
}

function textKilitliMi($gelenTamamlandi){
    if($gelenTamamlandi == "t") return ' readonly '; else return '';
}


function cariAdVer($gelenCariKod){

    global $aktifDB;

    $gelenCariKod = isoMetinYap($gelenCariKod);

    $sorguCumle ="SELECT TICARI_UNVANI FROM CARI 
                WHERE CARIKODU = '$gelenCariKod';";

    $donenAd ="";

    try {
        $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');

        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            $donenAd = utf8MetinYap($row[0]);
        }      	
        
        
        //VT sıfırlanıyor...
        $dbh = null;
        $ret = null;
        
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        die();
    }
    
    
    return $donenAd;

}


function hizmetAdVer($gelenHizmetKod){

    global $aktifDB;

    //$gelenHizmetKod = isoMetinYap($gelenHizmetKod);

    $sorguCumle ="SELECT ADI FROM HIZMET 
                WHERE BLKODU = $gelenHizmetKod;";

    $donenAd ="";

    try {
        $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');

        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            $donenAd = utf8MetinYap($row[0]);
        }      	
        
        
        //VT sıfırlanıyor...
        $dbh = null;
        $ret = null;
        
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        die();
    }
    
    
    return $donenAd;

}




function isChecked($gelenGizliMi){

    if ($gelenGizliMi == 't') $donenDeger = " checked"; else $donenDeger = "";     
    

    return $donenDeger;
    
}


function StokAdVer($gelenStokKod){

    global $aktifDB;

    $sorguCumle ="SELECT STOK_ADI FROM STOK 
                WHERE STOKKODU = '$gelenStokKod';";

    $sorguCumle = isoMetinYap($sorguCumle);

    try {
        $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
        $ret = $dbh->query($sorguCumle);        

        

        foreach($ret as $row) { 
            $donenDeger = utf8MetinYap($row[0]);
        }
    
    //VT sıfırlanıyor...
    $dbh = null;
    $ret = null;
    
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";        
        die();
    } 


    return $donenDeger;  
}


function ilgiliAlimAdVer($gelenIlgiliAlim){
	
	global $db;

    $sorgu="SELECT ilgili_alim_ad FROM ilgili_alim WHERE sn = $gelenIlgiliAlim;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "İlgili alım bulunamadı";
      exit();
   }  
   
   
   
   while($row = pg_fetch_row($ret)){
	  $donenDeger = $row[0];	   
   }  

   return $donenDeger;	
	
}






//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */
//************************************************************** */

//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("AkinsoftIslemleri.php");
require("mesajlasma.php");
require("logYaz.php");
require("KullanicidanBilgiVer.php");
//include('blink.html');
require("AkinsoftBirimleriVer.php");
require("BelgesiVarMi.php");




$gelenTalepSn = $_POST['talepSn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];
$gelenTamamlandi = $_POST['tamamlandi'];

$gelenFaturaDurumu = $_POST['faturaDurumu'];



$gelenMiktar = $_POST['miktar'];
$gelenBirim = $_POST['birim'];
$gelenIlgiliAlim = $_POST['ilgiliAlim'];
$gelenIlgiliAlimAd = $_POST['ilgiliAlimAd'];


$gelenBorclanacakCariKod = $_POST['borclanacakCariKod'];

$gelenStokKod = $_POST['stokKod'];
$gelenStokAd = $_POST['stokAd'];


$gelenKilometre = $_POST['kilometre'];

$gelenFaturaNo = $_POST['faturaNo'];
$gelenFaturaTarihi = $_POST['faturaTarihi'];


if(isset($_POST['gizliTalep'])) { // checkbox seçilmişse "on" değeri gönderiliyor
    //echo 'Onayladınız!';
    $gelenGizli = 't';
} else { // seçilmemişse bu değer sayfaya hiç gönderilmiyor
    //echo 'Onaylamadınız.';
    $gelenGizli = 'f';
}


//-------------------------------
if(isset($_POST['acilTalep'])) { // checkbox seçilmişse "on" değeri gönderiliyor
    //echo 'Onayladınız!';
    $gelenAcil = 'TRUE';
} else { // seçilmemişse bu değer sayfaya hiç gönderilmiyor
    //echo 'Onaylamadınız.';
    $gelenAcil = 'FALSE';
}





//$gelenKayitMod = $_POST['kayitMod'];

if($gelenIslemTip=="") $gelenIslemTip = "Yeni Kayıt";



if($gelenFaturaTarihi=="") $gelenFaturaTarihi = date("Y-m-d");



echo '<br><h1 style="text-align:center;">Doğrudan Harcama Girişi ('.$gelenIslemTip.')</h1>';



$muhasebeciMi = kullanicidanBilgiVer('muhasebeci', $kullanici);

if($muhasebeciMi=='f'){

    $gelenIsAvansBlKodu = kullanicidanBilgiVer('is_avans_bl_kodu', $kullanici);

    if($gelenIsAvansBlKodu==0){       
        echo "***Talep eden kişi muhasebeci değildir! İş avansı kodu yoktur!";
        goto cikis;
    }

}




if($gelenIslemTip=='Düzenleme' || $gelenIslemTip=='Görüntüleme'){

    $sorguCumle = "SELECT konu, 
					      talep_eden, 
						  kullanici, 
						  sube, 
						  nakit_odenen, 
						  bankadan_odenen, 
						  evrakla_odenen, 
						  kk_odenen, 
						  acik_odenen, 
						  dogrudan_odenen_cari_kod, 
						  fatura_durumu, 
						  gizli, 
						  kk_odenen_kk_kod, 
						  odenen_banka_kod, 
						  hizmet_kod, 
						  borclanacak_cari_kod,
						  stok_kod, /* 16 */
						  dogrudan_miktar,
						  dogrudan_birim,
						  dogrudan_ilgili_alim,		/*19*/				  
						  kilometre,
						  dogrudan_fatura_no, /*21*/
						  dogrudan_fatura_tarihi, /*22*/
						  acil /*23*/
					FROM talep
                    WHERE sn=$gelenTalepSn;";

                   //VALUES('$gelenKonu', '$gelenTalepEden', '$kullanici', '$gelenSube') RETURNING sn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    }

    while($row = pg_fetch_row($ret)){
        //$gelenTalepSn = 
        $gelenKonu = $row[0];
        $gelenTalepEden = $row[1];
        $gelenKullanici = $row[2];
        $gelenSube = $row[3];
        $gelenNakit = $row[4];
        $gelenBanka = $row[5];
        $gelenEvrak = $row[6];
        $gelenKk = $row[7];
        $gelenAcik = $row[8];
        $gelenCariKod = $row[9];
        $gelenCariAd = cariAdVer($row[9]);
        $gelenFaturaDurumu = $row[10];
        $gelenGizli = $row[11];
        $gelenKkKodd = $row[12];
        $gelenBankaKod = $row[13];
		$gelenHizmetKod = $row[14];
		$gelenHizmetAd = hizmetAdVer($row[14]);
		
		//----------
		$gelenBorclanacakCariKod = $row[15];
		
		if($row[15]==""){
			$gelenBorclanacakCariAd = "";			
		}else{
			$gelenBorclanacakCariAd = cariAdVer($row[15]);			
		}
		
		$gelenStokKod = $row[16];
		$gelenStokAd = stokAdVer($row[16]);
		$gelenMiktar = $row[17];
		$gelenBirim = $row[18];
		$gelenIlgiliAlim = $row[19];	
		$gelenIlgiliAlimAd = ilgiliAlimAdVer($gelenIlgiliAlim);
		$gelenKilometre = $row[20];	
		$gelenFaturaNo = $row[21];	
		$gelenFaturaTarihi = $row[22];	
		$gelenAcil = $row[23];	
		

    }




}elseif($gelenIslemTip=='Yeni Kayıt'){

    $gelenTalepSn = "";
    $gelenNakit=0;
    $gelenBanka=0;
    $gelenEvrak=0;
    $gelenKk=0;
    $gelenAcik=0;
    $gelenCariKod = "";
    $gelenFaturaDurumu = "";
	$gelenBorclanacakCariKod = "";
	$gelenKilometre = "";
	$gelenFaturaNo="";
	$gelenFaturaTarihi = date("Y-m-d");
	$gelenAcil = "";

}



//Talep kapatma kısmı
echo "<b>Talep No: ". $gelenTalepSn . "<br></b>";

echo '<form ';
if($gelenIslemTip!='Görüntüleme'){
    echo 'action="DogrudanHarcamaKaydet.php"';

}

echo ' method="post" enctype="multipart/form-data">';
echo "<br>";
echo "<b>Konu:</b><br>";
echo '<input type="text" '.textKilitliMi($gelenTamamlandi).'  id="konu" style="font-size: 12pt; width:100%;height:100%"  name="konu" value="'.$gelenKonu.'"><br>';
echo "<br>";
echo "<b>Talep Eden:</b><br>";
echo '<input type="text" '.textKilitliMi($gelenTamamlandi).'  id="talepEden" onclick="modalGoster(7);" readonly  style="font-size: 12pt; background-color:#fff333; height:100%"  name="talepEden" value="'.$gelenTalepEden.'"><br>';
echo "<br>";
echo "<b>Nakit Ödenen: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Cari Kod:</b><br>";
echo '<input type="number" style="text-align:right;" '.textKilitliMi($gelenTamamlandi).'  id="nakit" name="nakit" min="0" value="'.$gelenNakit.'" step="0.01">&nbsp; &nbsp; ';
echo '<input type="text" id="cariKod" onclick="modalGoster(0);" readonly style="height:100%;background-color : #fff333;" title="Nakit girişi varsa mutlaka cari seçilmelidir!"  name="cariKod" value="'.$gelenCariKod.'">&nbsp; <label id="cariAd">'.$gelenCariAd.'</label><br><br>';





echo "<b>Fatura Durumu: </b><br>";
echo '<select name="faturaDurumu" id="faturaDurumu">';
    if($gelenFaturaDurumu=="") echo '<option value="---" selected >---</option>';
    echo '<option value="Fiktif"' . selectedKoy($gelenFaturaDurumu, "Fiktif"). '>Fiktif</option>';
    echo '<option value="Reel"' . selectedKoy($gelenFaturaDurumu, "Reel"). '>Reel</option>';
echo "</select><br><br>";


echo "<b>Banka Ödenen:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Banka Kod: </b><br>";
echo '<input type="number" style="text-align:right;" '.textKilitliMi($gelenTamamlandi).'  id="banka" name="banka" min="0" value="'.$gelenBanka.'" step="0.01" oninput="bankaKodSifirla();">&nbsp; &nbsp;';
echo '<input type="text" id="bankaKod" onclick="modalGoster(5);" readonly style="height:100%;background-color : #fff333;" size="60" title="Banka ödemesi varsa mutlaka seçilmelidir!" name="bankaKod" value="'.$gelenBankaKod.'"><br><br>';

echo "<b>Evrakla Ödenen: </b><br>";
echo '<input type="number" style="text-align:right;" '.textKilitliMi($gelenTamamlandi).'  id="evrak" name="evrak" min="0" value="'.$gelenEvrak.'" step="0.01"><br><br><br>';

echo "<b>K.K. ile Ödenen:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Kredi Kartı: </b><br>";
echo '<input type="number" style="text-align:right;" '.textKilitliMi($gelenTamamlandi).'  id="kk" oninput="kkKontrol();" name="kk" min="0" value="'.$gelenKk.'" step="0.01" oninput="krediKartSifirla();">&nbsp; &nbsp;';
echo '<input type="text" id="kkKod" onclick="modalGoster(2);" readonly style="height:100%;background-color : #fff333;" title="Kredi kartı girişi varsa mutlaka KK seçilmelidir!"  name="kkKod" value="'.$gelenKkKodd.'">&nbsp;<br><br><br>'; // <label id="cariAd">'.$gelenCariAd.'</label>

echo "<b>Açık Hesap Ödenen: </b><br>";
echo '<input type="number" style="text-align:right;" '.textKilitliMi($gelenTamamlandi).'  id="acik" name="acik" min="0" step="0.01" value="'.$gelenAcik.'"><br>';




//----------------------


echo "<br><br>";
echo "<b>Borçlandırılacak Cari: </b> ";
echo '<input type="text" id="borclanacakCariKod" onclick="modalGoster(9);" readonly style="height:100%;background-color : #fff333;" title="Varsa Borçlandırılacak Cari seçilmelidir!"  name="borclanacakCariKod" value="'.$gelenBorclanacakCariKod.'">&nbsp; <label id="borclanacakCariAd">'.$gelenBorclanacakCariAd.'</label>';

echo '<button type="button" onclick="temizle(this);"><img src="Delete_Icon.png" height="15" width="15" title="Temizle" valign="middle"/></button><br><br>';


//----------------




echo "<br>";
echo "<b>Fatura No:<br> (Fatura olmadığı durumlarda -1 giriniz.)</b><br>";
echo '<input type="text" id="faturaNo" style="height:100%;" title="Fatura No girilmelidir!"  name="faturaNo" value="'.$gelenFaturaNo.'">';
echo '&nbsp;<button type="button" onclick="eFaturaListele(this);"><img src="change.png" height="15" width="15" title="E-Fatura Al" valign="middle"/></button>';

echo "<br><br>";
echo "<b>Fatura/Fiş Tarihi: </b><br>";
echo '<input type="date" id="faturaTarihi" required  name="faturaTarihi" value="'.$gelenFaturaTarihi.'" title="Fatura Tarihi">';
echo "<br>";






//----------------

echo "<br><br><hr>";

echo "<font style='color:red;'>";
echo "<b>Aynı anda Sadece bir seçenek girilebilir! Stok kodu veya Hizmet kodu <br><br></b>";
echo "</font>";

echo "<b>Stok Kod: </b><br>";
echo '<input type="text" id="stokKod" style="background-color : #fff333;" onclick="modalGoster(6);" readonly size="20" name="stokKod" value="'.$gelenStokKod.'">'.bosluk . bosluk.'<label id="stokAd">&nbsp;'.$gelenStokAd.'</label>';

echo bosluk . bosluk.'<button type="button" onclick="yeniStokEkle(this);"><img src="add-icon.png" height="20" width="20" title="Kayıt Ekle" valign="middle"/></button> <br><br>';


echo "<b>Hizmet Kodu:</b><br>";
echo '<input type="text" id="hizmetKod" style="background-color : #fff333;" onclick="modalGoster(4);" readonly size="4" name="hizmetKod" value="'.$gelenHizmetKod.'">'.bosluk . bosluk.'<label id="hizmetAd">&nbsp;'.$gelenHizmetAd.'</label><br>';




echo "<br><b>Miktar: </b><br>";
echo '<input type="number" id="miktar" style="text-align:right;" '.textKilitliMi($gelenKayitMod).'  name="miktar" maxlength="5" size="5" min="0" value="'.$gelenMiktar.'" title="Talep Miktarı girilebilir. :)" step="0.1">';


echo "<br><br><b>Birim: </b><br>";
echo '<select name="birim" id="birim">';

if($gelenBirim=="") echo '<option value="---" selected >---</option>';


    foreach (birimListesiVer() as $key => $birim) {
        echo '<option value="' . $birim . '"' . selectedKoy($gelenBirim, $birim). '>'.$birim.'</option>';	           
    }

echo "</select>";
echo "<br><br>";




echo "<br><b>Ne için Alındı (Parametrik):</b><br>";

echo '<input type="text" id="ilgiliAlim" onclick="modalGoster(8);" readonly size="4"  name="ilgiliAlim" value="'.$gelenIlgiliAlim.'" style="background-color : #fff333;height:100%"/>'.bosluk . bosluk.'<label id="ilgiliAlimAd">'.$gelenIlgiliAlimAd.'</label><br>';
	




echo "<br><b>Araç Kilometresi: </b><br>";
echo '<input type="number" id="kilometre" style="text-align:right;" '.textKilitliMi($gelenKayitMod).'  name="kilometre" maxlength="5" size="15" min="0" value="'.$gelenKilometre.'" title="Yakıt alınmış ise Araç Kilometresi girilmelidir!" step="1"><br>';






echo "<br><hr>";
//----------------




echo "<b>İsteyen Şube: </b><br>";
echo '<select name="sube" id="sube">';

if($gelenSube=="") echo '<option value="---" selected >---</option>';

    foreach (subeListesiVer() as $key => $sube) {
        echo '<option value="' . $sube . '"' . selectedKoy($gelenSube, $sube). '>'.$sube.'</option>';	           
    }
echo "<br>";
echo "</select>";

echo "<br><br><br>";
echo "<b>Dosya Ekle: </b><br>";
echo '<input type="file" name="fileToUpload[]" id="fileToUpload" multiple>';

$gormeYetkililer = ['milbuga', 'eguler', 'fcan', 'ssever', 'muhasebe', 'bseyrek', 'tsecilgin'];

if(in_array($kullanici, $gormeYetkililer)){
    echo "<br><br><br>";
    echo '<input type="checkbox" id="gizliTalep" name="gizliTalep" '.isChecked($gelenGizli).' title="Talep GİZLİ ?">Talep GİZLİ ?</input><br>';


}

echo "<br><br><br>";
echo '<input type="checkbox" id="acilTalep" name="acilTalep" '.isChecked($gelenAcil).' title="Talep ACİL ?"><font style="color:red;font-weight: bold;" >Talep ACİL ?</font></input><br>';





echo "<br><br><br>";    
echo '<input type="hidden" name="talepSn" value="'.$gelenTalepSn.'"/>';
echo '<input type="hidden" name="islemTip" value="'.$gelenIslemTip.'"/>';     
echo '<input type="hidden" name="islemTip" value="'.$gelenIslemTip.'"/>';     
echo '<input type="hidden" id="hizmetAd2" name="hizmetAd" value="'.$gelenHizmetAd.'"/>';
echo '<input type="hidden" id="ilgiliAlimAd2" name="ilgiliAlimAd" value="'.$gelenIlgiliAlimAd.'"/>';
echo '<input type="hidden" id="stokAd2" name="hizmetAd" value="'.$gelenStokAd.'"/>';

if($gelenIslemTip=='Görüntüleme'){
    echo '<button type="button" onclick="self.close()" style="width:200px;">Kapat</button> ';    

}else {
	require('reloadKontrol1.php');
    echo '<button type="submit" id="gonderTus" style="width:200px;" onclick="return veriKontrol();">Kaydet</button>';    
}         
echo '</form>';

echo "<br><br>";


if($gelenIslemTip=='Görüntüleme'){
    echo "<br>";
    //Edip beyin isteği üzerine belge gösterme kısmı eklendi.
    if(belgesiVarMi("talepBelgeler", $gelenTalepSn, 0)==true){
        
        //Belgeleri Göster
        echo '<form action="TalepBelgeleri.php" target="_blank"  method="post"  class="inline">'; 
        echo '<input type="hidden" name="talepSn" value="'.$gelenTalepSn.'">';    
        echo '<input type="hidden" name="talepDetaySn" value="">';
        echo '<input type="hidden" name="kriter" value="'.$gelenKriter.'">';      
        echo '<input type="hidden" name="kaynak" value="DogrudanHarcamaListesi.php">';  
        echo '<input type="hidden" name="tamamlandiMi" value="'.$row[3].'">';    
        echo '<button type="submit"><img src="documents.svg" height="20" width="20" title="Belgelere Git==>'.$gelenTalepSn.'"/></button>';
        //echo '<button type="submit">Detay</button>';
        echo '</form>';
    }

}



echo '<form action="AkinsoftStokOlustur.php" method="post" target="_blank" id="stokEklemeForm">';
echo '<input type="hidden" name="geriDon" value="1">';  
echo "</form>";





//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';




cikis:

pg_close($db);


?>

</body>
</html>
