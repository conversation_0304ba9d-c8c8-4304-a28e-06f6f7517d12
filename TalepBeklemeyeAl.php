<?php
//Toplu onayda talep detayı beklemeye alır. 

require('yetki.php');

$kullanici = $_SESSION["user"];
//require_once('ekranYetkileri.php');
require("PGConnect.php");
require("logYaz.php");

//-----------------------------
/*
require("mesajlasma.php");
require("KullanicidanBilgiVer.php");
require("TalepDetayYoksaTalepKapat.php");

*/
//************************************ */
//************************************ */
//************************************ */
//************************************ */
//************************************ */


$gelenTalepDetaySn = $_POST['talepDetaySn'];


if($gelenTalepDetaySn==""){
    echo "Hatalı veri!";
    exit();
}



$sorguCumle ="UPDATE talep_detay SET beklemede=TRUE WHERE sn = $gelenTalepDetaySn;";

$ret = pg_query($db, $sorguCumle);


if(!$ret){
    echo "Hata 122: " .  pg_last_error($db);
	pg_close($db);
    exit();    
}



if(pg_affected_rows($ret)>0){
	
	
    logYaz("$gelenTalepDetaySn numaralı talep detayı beklemeye alındı.");	
	
	ob_clean();
    echo "Okkk"; 
}else{
	ob_clean();
    echo "Hata!"; 
}


pg_close($db);

?>