// Talep Onay Kartı Ana <PERSON>ü - MySQL Version
// MEG Talep Tracking System - Approval Card System

const mysql = require('mysql2/promise');
const talepOnayDbConfig = require('./talepOnayDbConfig');

class TalepOnayKarti {
    constructor() {
        this.pool = null;
        this.init();
    }

    async init() {
        try {
            this.pool = mysql.createPool(talepOnayDbConfig);
            // Bağlantıyı test et
            const connection = await this.pool.getConnection();
            console.log('🔗 Talep Onay Kartı - MySQL bağlantısı başarılı');
            connection.release();
        } catch (error) {
            console.error('❌ Veritabanı bağlantı hatası:', error);
            throw error;
        }
    }

    /**
     * Onay bekleyen talepleri getirir
     * @param {number} limit - Getirilecek talep sayısı (varsayılan: 20)
     * @returns {Array} Onay bekleyen talep listesi
     */
    async getOnayBekleyenTalepler(limit = 20) {
        try {
            const query = `
                SELECT 
                    sn,
                    konu,
                    detay,
                    talep_eden,
                    kull<PERSON><PERSON>,
                    sube,
                    yaratma_zaman,
                    nakit_odenen,
                    bankadan_odenen,
                    evrakla_odenen,
                    kk_odenen,
                    acik_odenen,
                    (nakit_odenen + bankadan_odenen + evrakla_odenen + kk_odenen + acik_odenen) as toplam_tutar,
                    dogrudan,
                    tamamlandi
                FROM talep 
                WHERE tamamlandi = 0 
                AND dogrudan = 0
                ORDER BY yaratma_zaman DESC 
                LIMIT ?
            `;

            const [rows] = await this.pool.query(query, [limit]);
            
            // Türkçe tarih formatına çevir
            const formattedResults = rows.map(row => ({
                ...row,
                yaratma_zaman_formatted: this.turkishDateFormat(row.yaratma_zaman),
                toplam_tutar_formatted: this.formatCurrency(row.toplam_tutar)
            }));

            console.log(`📋 ${formattedResults.length} onay bekleyen talep getirildi`);
            return formattedResults;

        } catch (error) {
            console.error('❌ Onay bekleyen talepler getirme hatası:', error);
            throw error;
        }
    }

    /**
     * Belirli bir talebin detayını getirir
     * @param {number} talepSn - Talep seri numarası
     * @returns {Object} Talep detay bilgileri
     */
    async getTalepDetay(talepSn) {
        try {
            const query = `
                SELECT 
                    t.sn,
                    t.konu,
                    t.detay,
                    t.talep_eden,
                    t.kullanici,
                    t.sube,
                    t.yaratma_zaman,
                    t.nakit_odenen,
                    t.bankadan_odenen,
                    t.evrakla_odenen,
                    t.kk_odenen,
                    t.acik_odenen,
                    (t.nakit_odenen + t.bankadan_odenen + t.evrakla_odenen + t.kk_odenen + t.acik_odenen) as toplam_tutar,
                    t.dogrudan,
                    t.tamamlandi,
                    td.sn as detay_sn,
                    td.aciklama,
                    td.miktar,
                    td.birim,
                    td.termin_zaman,
                    td.onay,
                    td.onay_zaman
                FROM talep t
                LEFT JOIN talep_detay td ON t.sn = td.talep_sn
                WHERE t.sn = ? AND (td.iptal = 0 OR td.iptal IS NULL)
                ORDER BY td.sn
            `;

            const [rows] = await this.pool.query(query, [talepSn]);
            
            if (rows.length === 0) {
                throw new Error(`Talep SN: ${talepSn} bulunamadı`);
            }

            // İlk satırdan ana talep bilgilerini al
            const talepBilgi = rows[0];
            
            // Detayları filtrele (sadece detay_sn'si olan kayıtlar)
            const detaylar = rows
                .filter(row => row.detay_sn)
                .map(row => ({
                    detay_sn: row.detay_sn,
                    aciklama: row.aciklama || '-',
                    miktar: row.miktar || 0,
                    birim: row.birim || '-',
                    termin_zaman: row.termin_zaman,
                    termin_zaman_formatted: this.turkishDateFormat(row.termin_zaman),
                    onay: row.onay || 0,
                    onay_zaman: row.onay_zaman,
                    onay_zaman_formatted: this.turkishDateFormat(row.onay_zaman)
                }));

            return {
                sn: talepBilgi.sn,
                konu: talepBilgi.konu || '-',
                detay: talepBilgi.detay || '-',
                talep_eden: talepBilgi.talep_eden || '-',
                kullanici: talepBilgi.kullanici || '-',
                sube: talepBilgi.sube || '-',
                yaratma_zaman: talepBilgi.yaratma_zaman,
                yaratma_zaman_formatted: this.turkishDateFormat(talepBilgi.yaratma_zaman),
                toplam_tutar: talepBilgi.toplam_tutar || 0,
                toplam_tutar_formatted: this.formatCurrency(talepBilgi.toplam_tutar),
                dogrudan: talepBilgi.dogrudan || 0,
                tamamlandi: talepBilgi.tamamlandi || 0,
                detaylar: detaylar
            };

        } catch (error) {
            console.error('❌ Talep detay getirme hatası:', error);
            throw error;
        }
    }

    /**
     * Talebi onaylar
     * @param {number} talepSn - Talep seri numarası
     * @param {string} onayKullanici - Onaylayan kullanıcı
     * @param {Array} selectedDetails - Onaylanacak detay numaraları (opsiyonel)
     * @returns {boolean} İşlem başarısı
     */
    async talepOnayla(talepSn, onayKullanici = 'sistem', selectedDetails = null) {
        const connection = await this.pool.getConnection();
        
        try {
            await connection.beginTransaction();

            let detayResult;
            if (selectedDetails && Array.isArray(selectedDetails) && selectedDetails.length > 0) {
                // Sadece seçilen detayları onayla
                const placeholders = selectedDetails.map(() => '?').join(',');
                const detayOnayQuery = `
                    UPDATE talep_detay 
                    SET onay = 1, 
                        onay_zaman = NOW(), 
                        onay_kullanici = ?
                    WHERE talep_sn = ? AND onay = 0 AND sn IN (${placeholders})
                `;
                
                const queryParams = [onayKullanici, talepSn, ...selectedDetails];
                [detayResult] = await connection.query(detayOnayQuery, queryParams);
            } else {
                // Tüm detayları onayla (eski davranış)
                const detayOnayQuery = `
                    UPDATE talep_detay 
                    SET onay = 1, 
                        onay_zaman = NOW(), 
                        onay_kullanici = ?
                    WHERE talep_sn = ? AND onay = 0
                `;
                
                [detayResult] = await connection.query(detayOnayQuery, [onayKullanici, talepSn]);
            }
            
            // Talep ana kaydını güncelle
            const talepUpdateQuery = `
                UPDATE talep 
                SET duzeltme_zaman = NOW()
                WHERE sn = ?
            `;
            
            await connection.query(talepUpdateQuery, [talepSn]);
            
            await connection.commit();
            
            console.log(`✅ Talep SN: ${talepSn} başarıyla onaylandı. ${detayResult.affectedRows} detay onaylandı.`);
            return true;

        } catch (error) {
            await connection.rollback();
            console.error('❌ Talep onaylama hatası:', error);
            throw error;
        } finally {
            connection.release();
        }
    }

    /**
     * Talebi siler (iptal eder)
     * @param {number} talepSn - Talep seri numarası
     * @param {string} silmeKullanici - Silen kullanıcı
     * @returns {boolean} İşlem başarısı
     */
    async talepSil(talepSn, silmeKullanici = 'sistem') {
        const connection = await this.pool.getConnection();
        
        try {
            await connection.beginTransaction();

            // Önce talep detaylarını iptal et
            const detayIptalQuery = `
                UPDATE talep_detay 
                SET iptal = 1, 
                    iptal_kullanici = ?, 
                    duzeltme_zaman = NOW()
                WHERE talep_sn = ? AND iptal = 0
            `;
            
            const [detayResult] = await connection.query(detayIptalQuery, [silmeKullanici, talepSn]);
            
            // Ana talebi tamamlandı olarak işaretle (silmek yerine)
            const talepUpdateQuery = `
                UPDATE talep 
                SET tamamlandi = 1, 
                    tamamlanma_zaman = NOW(),
                    duzeltme_zaman = NOW()
                WHERE sn = ?
            `;
            
            await connection.query(talepUpdateQuery, [talepSn]);
            
            await connection.commit();
            
            console.log(`🗑️ Talep SN: ${talepSn} başarıyla silindi (iptal edildi). ${detayResult.affectedRows} detay iptal edildi.`);
            return true;

        } catch (error) {
            await connection.rollback();
            console.error('❌ Talep silme hatası:', error);
            throw error;
        } finally {
            connection.release();
        }
    }

    /**
     * Tarih formatını Türkçe'ye çevirir
     * @param {Date|string} tarih - Formatlanacak tarih
     * @returns {string} Türkçe formatlanmış tarih
     */
    turkishDateFormat(tarih) {
        if (!tarih) return '-';
        
        const date = new Date(tarih);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        
        return `${day}-${month}-${year} ${hours}:${minutes}`;
    }

    /**
     * Para formatını düzenler
     * @param {number} tutar - Formatlanacak tutar
     * @returns {string} Formatlanmış tutar
     */
    formatCurrency(tutar) {
        if (!tutar) return '0,00 ₺';
        
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(tutar);
    }

    /**
     * Bağlantıyı kapatır
     */
    async close() {
        if (this.pool) {
            await this.pool.end();
            console.log('🔌 Veritabanı bağlantısı kapatıldı');
        }
    }
}

module.exports = TalepOnayKarti;