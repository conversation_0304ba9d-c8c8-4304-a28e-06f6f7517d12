<?php
/*
Muhasebe ekranından doğrudan harcamalarda sıkıntı olduğunda bu rutin ile talep detay onayı iptal edilir ve varsa talep hareketi de silini!
Ancak ilgili faturası olmamalıdır!

*/
require('yetki.php');
$ekranKod = 65;
require('ekranYetkileri.php');
require("AkinsoftIslemleri.php");

require("logYaz.php");


//*********************************************
//*********************************************
//*********************************************
//*********************************************
//*********************************************
//*********************************************




$gelenTalepSn = $_POST['talepSn'];
$gelenTalepDetaySn = $_POST['talepDetaySn'];


if($gelenTalepSn==""){
	echo "Veri Hatası!";
	pg_close($db);
	exit();	
}


if($gelenTalepDetaySn==""){
	$dogrudanTalep = true;
	$aranacakTalepVeDetayNo =  $gelenTalepSn;
}else{
	$dogrudanTalep = false;
	$aranacakTalepVeDetayNo =  "$gelenTalepSn-$gelenTalepDetaySn";
}



//Burada talep ve varsa detay numarası ile ilişkili bir fatura var mı diye bakılacak. Faturası varsa bu şlkem geri alınamaz.
//2023 10 06


$akinsoftdaFaturasiVar = false;
$gelenFaturaNo = "";


$sorguCumle = "SELECT DISTINCT(FATURA_NO)
                        FROM FATURAHR FH JOIN FATURA F ON (FH.BLFTKODU = F.BLKODU) 
                        WHERE FH.BLKODU>0                                                
                        AND FH.EKBILGI_3='$aranacakTalepVeDetayNo';";

try {
	$dbh = new PDO($aktifDB , 'sysdba', 'masterkey');
	$ret = $dbh->query($sorguCumle);
	
		foreach($ret as $row) {
			$akinsoftdaFaturasiVar = true;
			$gelenFaturaNo = $gelenFaturaNo . $row[0] . ", ";
		} 				
		
		//VT sıfırlanıyor...
		$dbh = null;
		$ret = null;
		
	}
	catch (PDOException $e) {
		print "Hata!: " . $e->getMessage() . "<br/>";
		//VT sıfırlanıyor...
		$dbh = null;
		$ret = null;
		die();
	}	
	
	
if($akinsoftdaFaturasiVar){
	ob_clean();
	echo "$gelenFaturaNo numaralı faturalarda, içerisinde $aranacakTalepVeDetayNo nolu talep bilgisi bulunuyor.\nÖncelikle Akınsoftdan bu faturaların silinmesi gerekmektedir!";
	pg_close($db);
	exit();
}	





//echo "$gelenTalepSn - $gelenTalepDetaySn - $dogrudanTalep";


//exit();


if($dogrudanTalep){ //Sadece talep tablosundaki tamamlandı kısmını false yapacak. 


	$sorguCumle = "UPDATE talep SET tamamlandi = FALSE
                                    WHERE sn=$gelenTalepSn;";
									
	//Sorgu çalıştır.
	$ret = pg_query($db, $sorguCumle);

	if(!$ret){
		echo "Hata 168 " . pg_last_error($db);
		pg_close($db);
		exit();		
	} 	
	
	if(pg_affected_rows($ret)<1){		
		echo "$gelenTalepSn numaralı talep kapanması geri alınamadı!";
		pg_close($db);
		exit()	;	
	}else{
		logYaz($gelenTalepSn . ' Numaralı Talep tamamlandı konumunda çıkartıldı!');
		ob_clean();
		echo "Okkk";
		pg_close($db);
		exit()	;		
	}								
									
									
									
}else{ //Detayın tamamlandı tiki kaldırılacak peşi sıra da talep kapandısı kaldırılacak. 

	// Önce Talep açılacak.
	
	$sorguCumle = "UPDATE talep SET tamamlandi = FALSE
                                    WHERE sn=$gelenTalepSn;";
									
	//Sorgu çalıştır.
	$ret = pg_query($db, $sorguCumle);

	if(!$ret){
		echo "Hata 165 " . pg_last_error($db);
		pg_close($db);
		exit();		
	} 	
	
	if(pg_affected_rows($ret)<1){		
		echo "$gelenTalepSn numaralı talep kapanması geri alınamadı222!";
		pg_close($db);
		exit()	;	
	}
	
	//Şimdi detaydaki TAMAMLANDI kaldırılıyor. 
	//BU KISMA GEREK YOKMUŞ! İPTAL EDİLDİ!
	//Valla gereği varmış. Tekrar açıldı. :) 2024 01 20
	
	
	$sorguCumle = "UPDATE talep_detay SET tamamlandi = FALSE
                                    WHERE sn=$gelenTalepDetaySn;";
									
	//Sorgu çalıştır.
	$ret = pg_query($db, $sorguCumle);

	if(!$ret){
		echo "Hata 169 " . pg_last_error($db);
		pg_close($db);
		exit();		
	} 	
	
	if(pg_affected_rows($ret)<1){		
		echo "$gelenTalepDetaySn numaralı talep detay kapanması geri alınamadı!";
		pg_close($db);
		exit();	
	}
	
	
	//Şimdi de ilgili talep hareket kayıtları Kapalı mı tiki FALSE yapılacak. 
	
	$sorguCumle = "UPDATE talep_hareket SET kapali_mi = FALSE
                                    WHERE talep_sn = $gelenTalepSn
									AND talep_detay_sn = $gelenTalepDetaySn;";
									
	//echo "$sorguCumle<br>";
	//exit();
									
	//Sorgu çalıştır.
	$ret = pg_query($db, $sorguCumle);

	if(!$ret){
		echo "Hata 160 " . pg_last_error($db);
		pg_close($db);
		exit();		
	}
	
	/*
 	
	
	if(pg_affected_rows($ret)<1){		
		echo "Talep no: $gelenTalepSn\nDetay No: $gelenTalepDetaySn numaralı talep detay hareketi kapanması geri alınamadı!";
		pg_close($db);
		exit();	
	}
	*/
	
	
	logYaz($gelenTalepSn . ' Numaralı Talep ve hareketleri tamamlandı konumunda çıkartıldı!');
	ob_clean();
	echo "Okkk";							
									
									
									
									
}

pg_close($db);
?>