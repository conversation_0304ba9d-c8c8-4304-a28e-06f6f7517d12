<?php
require('yetki.php');
//$ekranKod = 84;
$kullanici = $_SESSION["user"];
//require('ekranYetkileri.php');

?>
<style>   
  table#tableMain2 thead { display:block; }
  table#tableMain2 tbody { height:300px; overflow-y:scroll; display:block; }
</style>
</style>

<?php



function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}




function talepDetaydaOnayliVarMi($gelenTalepNo){
	
	global $db;
	
	$sorguCumle = "SELECT COUNT(sn) 
					FROM talep_detay
					WHERE talep_sn = $gelenTalepNo
					AND onay= TRUE;";
	
	$ret = pg_query($db, $sorguCumle);
	
	
	while($row = pg_fetch_row($ret)){
		
		if($row[0]==0){
			$donenDeger = false;
		}else{
			$donenDeger = true;
		
		}
		
	}
	
	return $donenDeger;
	
}

//******************************************** */
//******************************************** */
//******************************************** */
//******************************************** */
//******************************************** */
//******************************************** */
//******************************************** */

require("PGConnect.php");




//$gelenSadeceAcik = $_POST['sadeceAcik'];



$gelenCariKod = $_POST['cariKod'];
$gelenHedefTransfer = $_POST['hedefTransfer'];
$gelenSadeceOnaylilarGorunsun = $_POST['sadeceOnaylilarGorunsun'];

if($gelenCariKod==""){
	echo "Veri Hatası!";
	pg_close($db);
	exit();
	
}





	
if($gelenSadeceOnaylilarGorunsun=="1"){
	
	$sorguCumle = "SELECT sn, konu, talep_eden, kullanici
					FROM talep 
					WHERE tamamlandi = TRUE 
					AND dogrudan=TRUE 
					AND dogrudan_odenen_cari_kod= '$gelenCariKod'                
					ORDER BY sn DESC;";
	
}else{
	$sorguCumle = "SELECT sn, konu, talep_eden, kullanici
					FROM talep 
					WHERE tamamlandi = FALSE 
					AND dogrudan=FALSE 
					AND dogrudan_odenen_cari_kod= '$gelenCariKod'                
					ORDER BY sn DESC;";
	
}
	
	
	


//echo $sorguCumle;


if($gelenHedefTransfer==""){
	$gelenHedefTransfer = "veriTransfer";	
}




$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
}

$count = pg_num_rows($ret); //Sorgu sonucunun satır sayısını veriyor.

if($count<1){
    echo '<p style="color:red">Detay kaydı yok!</p>';
    pg_close($db);
    exit();
}


echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi2" onkeyup="searchTableColumns(\'tableMain2\',\'myInputHepsi2\',\'bulunanKayitAdet2\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">&nbsp;&nbsp;<label id="bulunanKayitAdet2"></label>';
echo "<br><br>";

echo '<table class= "sortable" valign="middle" id="tableMain2" >';
echo "<tr>";
echo '<th style="text-align:center;cursor:pointer;">S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Konu</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Eden</th>';
echo '<th style="text-align:center;cursor:pointer;">Kullanıcı</th>';
echo "</tr>";

while($row = pg_fetch_row($ret)){    

    $sayac+=1;
    echo "<tr class='item' onclick='$gelenHedefTransfer(\"$row[0]\", \"$row[1]\");'>";    
    echo "<td style='text-align:center'>". $row[0] . "</td>";   //Sn
    echo "<td style='text-align:left'>". $row[1]. "</td>";   //Konu
    
    echo "<td style='text-align:left'>". $row[2]. "</td>";   //Talep Eden
    echo "<td style='text-align:left'>". $row[3]. "</td>";   //Kullanıcı
    echo "</tr>";
    
}


echo "</table>";

pg_close($db);


?>