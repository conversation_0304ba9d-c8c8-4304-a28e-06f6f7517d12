<?php
require('yetki.php');
$ekranKod = 76;
require('ekranYetkileri.php');

//require_once("PGConnect.php");
require("logYaz.php");
require("AkinsoftIslemleri.php");
require("mesajlasma.php");
require("KullanicidanBilgiVer.php");


$gelenTalepSn = $_POST['talepSn'];
$gelenTalepDetaySn = $_POST['talepDetaySn'];
$gelenSilmeTalepSn = $_POST['silmeTalepSn'];
$gelenIsteyenKullanici = $_POST['isteyenKullanici'];





if($gelenTalepSn=="" || $gelenTalepDetaySn=="" || $gelenSilmeTalepSn=="" || $gelenIsteyenKullanici==""){
    echo "Veri hatası!";
	pg_close($db);
    exit();
}


//Öncelikle akınsoft içinde kaydı var mı diye bakılacak. Talep ve detay bilgisi ile bakılacak.


if($gelenTalepDetaySn=="0"){
	
	$sorguCumle = "SELECT FIRST 1 DISTINCT(FATURA.FATURA_NO), 
						  FATURA.CARIKODU,
						  CARI.TICARI_UNVANI					  					  
					FROM FATURA 
					LEFT JOIN FATURAHR ON FATURA.BLKODU = FATURAHR.BLFTKODU  
					LEFT JOIN CARI ON CARI.CARIKODU = FATURA.CARIKODU  
					WHERE FATURAHR.EKBILGI_3 LIKE '$gelenTalepSn%';";
}else{
	
		$sorguCumle = "SELECT FIRST 1 DISTINCT(FATURA.FATURA_NO), 
						  FATURA.CARIKODU,
						  CARI.TICARI_UNVANI					  					  
					FROM FATURA 
					LEFT JOIN FATURAHR ON FATURA.BLKODU = FATURAHR.BLFTKODU  
					LEFT JOIN CARI ON CARI.CARIKODU = FATURA.CARIKODU  
					WHERE FATURAHR.EKBILGI_3 = '$gelenTalepSn-$gelenTalepDetaySn';";
	
}

//echo $sorguCumle;
//exit();


				
$sorguCumle = isoMetinYap($sorguCumle);	

$akinsoftdaKayitVar = false;

try {
    
    $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
    
    $ret = $dbh->query($sorguCumle);

		foreach($ret as $row){	
			$akinsoftdaKayitVar = true;
		
			$faturaNo = utf8MetinYap($row[0]);			
			$faturaCariKod = utf8MetinYap($row[1]);			
			$faturaCariAd = utf8MetinYap($row[2]);

		}
	
    $dbh = null;
    $ret = null;
    
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;
        $ret = null;
        die();
    }

if($akinsoftdaKayitVar==true){
	echo "$gelenTalepSn / $gelenTalepDetaySn numaralı Talep/Detay ile ilgili halen Akınsoft içinde kayıt görünmektedir. SİLİNEMEZ!\n"
		."Fatura No: $faturaNo\nCari Kod: $faturaCariKod\nCari Ad: $faturaCariAd\n"; //$sorguCumle
	pg_close($db);
    exit();	
}





//Akınsoft kaydı olmadığı için silme işlemi yapılacak.
if($gelenTalepDetaySn=="0"){
	
	$sorguCumle = "DELETE FROM talep WHERE sn=$gelenTalepSn;";
	
}else{
	
	$sorguCumle = "DELETE FROM talep_detay WHERE sn=$gelenTalepDetaySn;";
	
}





$ret = pg_query($db, $sorguCumle);

if(pg_affected_rows($ret)<1){
	echo "$gelenTalepSn / $gelenTalepDetaySn numaralı Talep/Detay silinemedi!";
	pg_close($db);
    exit();		
}



//echo "************* <br> $sorguCumle ";
//exit();



//Silindi demek ki silme talebine de onay yazılacak

$sorguCumle = "UPDATE talep_silme_istegi 
				SET onay=TRUE 
				WHERE sn=$gelenSilmeTalepSn;";

$ret = pg_query($db, $sorguCumle);

if(pg_affected_rows($ret)<1){
	echo "$gelenTalepSn / $gelenTalepDetaySn numaralı Talep/Detay silindi ancak $gelenSilmeTalepSn numaralı silme talebi onaylanamadı!";
	pg_close($db);
    exit();		
}

//Silme isteyen kişiye eposta gidecek.
$gidenKimden="<EMAIL>";
$gidenAdresler = [];    
$gidenBilgi="Talep silme işleğiniz onaylandı...";


$mesaj = "$gelenTalepSn numaralı talep isteğiniz onaylandı ve silindi.";
array_push($gidenAdresler, kullanicidanBilgiVer('eposta', $gelenIsteyenKullanici));

epostaGonder($gidenKimden, $gidenAdresler, $gidenBilgi, $mesaj, 0);

logYaz("$gelenTalepSn / $gelenTalepDetaySn Numaralı talep başarı ile silindi.");


ob_clean();
echo "Okkk"; //$gelenTalepSn;

pg_close($db);
?>