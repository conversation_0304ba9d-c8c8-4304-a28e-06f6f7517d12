// Talep Onay Kartı Test Dosyası
// Sistemin doğru çalışıp çalışmadığını test eder

const TalepOnayKarti = require('./talepOnayKarti');

/**
 * Test fonksiyonları
 */
class TalepOnayTest {
    constructor() {
        this.onayKarti = null;
        this.testSonuclari = [];
    }

    /**
     * Testi başlat
     */
    async testBaslat() {
        console.log('🧪 MEG Talep Onay Kartı - Test Başlatılıyor...\n');
        
        try {
            // Database bağlantısını test et
            await this.databaseBaglantiTest();
            
            // Talep getirme testini yap
            await this.talepGetirmeTest();
            
            // Talep detay testini yap
            await this.talepDetayTest();
            
            // Onay ve silme testlerini yap (gerçek veritabanında test edilmemeli)
            await this.onayVeSilmeTest();
            
            // Test sonuçlarını göster
            this.testSonuclariniGoster();
            
        } catch (error) {
            console.error('❌ Test hatası:', error);
        } finally {
            if (this.onayKarti) {
                await this.onayKarti.close();
            }
        }
    }

    /**
     * Database bağlantı testi
     */
    async databaseBaglantiTest() {
        console.log('🔗 Database Bağlantı Testi...');
        
        try {
            this.onayKarti = new TalepOnayKarti();
            await new Promise(resolve => setTimeout(resolve, 2000)); // Bağlantı için bekle
            
            this.testSonucEkle('Database Bağlantısı', true, 'Başarıyla bağlandı');
            console.log('✅ Database bağlantısı başarılı\n');
            
        } catch (error) {
            this.testSonucEkle('Database Bağlantısı', false, error.message);
            console.log('❌ Database bağlantısı başarısız:', error.message, '\n');
            throw error;
        }
    }

    /**
     * Talep getirme testi
     */
    async talepGetirmeTest() {
        console.log('📋 Talep Getirme Testi...');
        
        try {
            const talepler = await this.onayKarti.getOnayBekleyenTalepler(5);
            
            if (Array.isArray(talepler)) {
                this.testSonucEkle('Talep Getirme', true, `${talepler.length} talep getirildi`);
                console.log(`✅ ${talepler.length} onay bekleyen talep getirildi`);
                
                // İlk talep varsa özelliklerini kontrol et
                if (talepler.length > 0) {
                    const ilkTalep = talepler[0];
                    const gerekliOzellikler = ['sn', 'konu', 'talep_eden', 'sube', 'yaratma_zaman_formatted'];
                    const eksikOzellikler = gerekliOzellikler.filter(ozellik => !ilkTalep.hasOwnProperty(ozellik));
                    
                    if (eksikOzellikler.length === 0) {
                        console.log('✅ Talep özellikleri tam');
                        this.testSonucEkle('Talep Özellikleri', true, 'Tüm gerekli özellikler mevcut');
                    } else {
                        console.log('⚠️ Eksik özellikler:', eksikOzellikler);
                        this.testSonucEkle('Talep Özellikleri', false, `Eksik özellikler: ${eksikOzellikler.join(', ')}`);
                    }
                }
                
            } else {
                this.testSonucEkle('Talep Getirme', false, 'Dönen veri array değil');
                console.log('❌ Dönen veri array formatında değil');
            }
            
            console.log('');
            
        } catch (error) {
            this.testSonucEkle('Talep Getirme', false, error.message);
            console.log('❌ Talep getirme hatası:', error.message, '\n');
        }
    }

    /**
     * Talep detay testi
     */
    async talepDetayTest() {
        console.log('📝 Talep Detay Testi...');
        
        try {
            // Önce mevcut talepleri al
            const talepler = await this.onayKarti.getOnayBekleyenTalepler(1);
            
            if (talepler.length > 0) {
                const testTalepSn = talepler[0].sn;
                const detay = await this.onayKarti.getTalepDetay(testTalepSn);
                
                if (detay && detay.sn === testTalepSn) {
                    this.testSonucEkle('Talep Detay', true, `Talep ${testTalepSn} detayı getirildi`);
                    console.log(`✅ Talep ${testTalepSn} detayı başarıyla getirildi`);
                    
                    // Detay özelliklerini kontrol et
                    const gerekliDetayOzellikleri = ['sn', 'konu', 'detay', 'talep_eden', 'detaylar'];
                    const eksikDetayOzellikleri = gerekliDetayOzellikleri.filter(ozellik => !detay.hasOwnProperty(ozellik));
                    
                    if (eksikDetayOzellikleri.length === 0) {
                        console.log('✅ Detay özellikleri tam');
                        this.testSonucEkle('Detay Özellikleri', true, 'Tüm detay özellikleri mevcut');
                    } else {
                        console.log('⚠️ Eksik detay özellikleri:', eksikDetayOzellikleri);
                        this.testSonucEkle('Detay Özellikleri', false, `Eksik: ${eksikDetayOzellikleri.join(', ')}`);
                    }
                    
                } else {
                    this.testSonucEkle('Talep Detay', false, 'Detay getirilmedi veya yanlış talep');
                    console.log('❌ Talep detayı getirilemedi');
                }
                
            } else {
                this.testSonucEkle('Talep Detay', false, 'Test için talep bulunamadı');
                console.log('⚠️ Detay testi için talep bulunamadı');
            }
            
            console.log('');
            
        } catch (error) {
            this.testSonucEkle('Talep Detay', false, error.message);
            console.log('❌ Talep detay hatası:', error.message, '\n');
        }
    }

    /**
     * Onay ve silme testi (simülasyon)
     */
    async onayVeSilmeTest() {
        console.log('⚠️ Onay ve Silme Testi (Simülasyon)...');
        
        try {
            // Bu testler gerçek veritabanında çalıştırılmamalı
            // Sadece fonksiyonların varlığını kontrol edelim
            
            const onayFonksiyonuVar = typeof this.onayKarti.talepOnayla === 'function';
            const silmeFonksiyonuVar = typeof this.onayKarti.talepSil === 'function';
            
            if (onayFonksiyonuVar) {
                this.testSonucEkle('Onay Fonksiyonu', true, 'talepOnayla fonksiyonu mevcut');
                console.log('✅ talepOnayla fonksiyonu mevcut');
            } else {
                this.testSonucEkle('Onay Fonksiyonu', false, 'talepOnayla fonksiyonu eksik');
                console.log('❌ talepOnayla fonksiyonu eksik');
            }
            
            if (silmeFonksiyonuVar) {
                this.testSonucEkle('Silme Fonksiyonu', true, 'talepSil fonksiyonu mevcut');
                console.log('✅ talepSil fonksiyonu mevcut');
            } else {
                this.testSonucEkle('Silme Fonksiyonu', false, 'talepSil fonksiyonu eksik');
                console.log('❌ talepSil fonksiyonu eksik');
            }
            
            // Tarih ve para formatı testleri
            const turkishDateTest = this.onayKarti.turkishDateFormat(new Date());
            const currencyTest = this.onayKarti.formatCurrency(1234.56);
            
            if (turkishDateTest && turkishDateTest.includes('-')) {
                this.testSonucEkle('Tarih Formatı', true, 'Türkçe tarih formatı çalışıyor');
                console.log('✅ Türkçe tarih formatı çalışıyor');
            } else {
                this.testSonucEkle('Tarih Formatı', false, 'Tarih formatı hatalı');
                console.log('❌ Tarih formatı hatalı');
            }
            
            if (currencyTest && currencyTest.includes('₺')) {
                this.testSonucEkle('Para Formatı', true, 'Para formatı çalışıyor');
                console.log('✅ Para formatı çalışıyor');
            } else {
                this.testSonucEkle('Para Formatı', false, 'Para formatı hatalı');
                console.log('❌ Para formatı hatalı');
            }
            
            console.log('');
            
        } catch (error) {
            this.testSonucEkle('Fonksiyon Testleri', false, error.message);
            console.log('❌ Fonksiyon testi hatası:', error.message, '\n');
        }
    }

    /**
     * Test sonucu ekle
     */
    testSonucEkle(testAdi, basarili, mesaj) {
        this.testSonuclari.push({
            test: testAdi,
            basarili: basarili,
            mesaj: mesaj,
            zaman: new Date().toISOString()
        });
    }

    /**
     * Test sonuçlarını göster
     */
    testSonuclariniGoster() {
        console.log('📊 TEST SONUÇLARI');
        console.log('='.repeat(50));
        
        const basariliTestler = this.testSonuclari.filter(t => t.basarili).length;
        const toplamTest = this.testSonuclari.length;
        
        this.testSonuclari.forEach(sonuc => {
            const durum = sonuc.basarili ? '✅' : '❌';
            console.log(`${durum} ${sonuc.test}: ${sonuc.mesaj}`);
        });
        
        console.log('='.repeat(50));
        console.log(`📈 Başarı Oranı: ${basariliTestler}/${toplamTest} (%${Math.round((basariliTestler/toplamTest)*100)})`);
        
        if (basariliTestler === toplamTest) {
            console.log('🎉 Tüm testler başarıyla geçti!');
        } else {
            console.log('⚠️ Bazı testler başarısız oldu. Lütfen kontrol edin.');
        }
    }
}

// Test otomatik olarak çalıştır
if (require.main === module) {
    const test = new TalepOnayTest();
    test.testBaslat().catch(console.error);
}

module.exports = TalepOnayTest;