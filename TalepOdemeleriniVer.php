<?php
require('yetki.php');
$ekranKod = 76;
require('ekranYetkileri.php');

?>
<style style="text/css">
    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }
</style>

<?php


function talepDetayKapaliMi($gelenTalepDetaySn){

    global $db;    
   

	$sorgu="SELECT tamamlandi FROM talep_detay WHERE sn=$gelenTalepDetaySn;";	

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   

   $donenDeger=0;   

   while($row = pg_fetch_row($ret))
   {
       $donenDeger = $row[0];	   
   }  

   return $donenDeger;
}


//*****************************************************************
//*****************************************************************
//*****************************************************************
//*****************************************************************
//*****************************************************************
//*****************************************************************
//*****************************************************************

require("AkinsoftCariAdVer.php");
require("AkinsoftIslemleri.php");



$gelenTalepSn = $_POST['talepSn'];
//$gelenKriter = $_POST['kriter'];

//echo $gelenBilgi;
//echo $gelenKriter;


//echo $gelenTalepSn . "-". $gelenTalepDetaySn;


$sorguCumle = "SELECT nakit_odenen, 
					  bankadan_odenen, 
					  evrakla_odenen, 
					  kk_odenen, 
					  acik_odenen, 
					  talep_fatura_bilgisi, 
					  sn, 
					  fatura_no,
					  nakit_odenen_cari_kod					  
					  FROM talep_hareket 
					  WHERE talep_sn = $gelenTalepSn;";

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
}

$count = pg_num_rows($ret); //Sorgu sonucunun satır sayısını veriyor.

if($count<1){
    echo '<p style="color:red">Hareket kaydı yok!</p>';
    pg_close($db);
    exit;
}

echo '<table class= "sortable" valign="middle" id="tableMain">';
//echo "<table >";
echo "<tr>";
echo '<th style="text-align:center;cursor:pointer;">S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Nakit Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Bankadan Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Evrakla Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">K.K. Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Açık Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Fatura No</th>';
echo '<th style="text-align:center;cursor:pointer;">Cari Ad</th>';
echo '<th style="text-align:center;cursor:pointer;">Satır Bilgisi</th>';
echo '<th style="text-align:center;cursor:pointer;">İşlem</th>';
echo "</tr>";

while($row = pg_fetch_row($ret)){

    $sayac+=1;

    echo "<tr>";
    echo "<td style='text-align:center'>". $sayac . "</td>";   //S/N
    echo "<td style='text-align:right'>". number_format($row[0], 2, ',', '.') . "</td>";   //Nakit ödenen
    echo "<td style='text-align:right'>". number_format($row[1], 2, ',', '.') . "</td>";   //Bankadan ödenen
    echo "<td style='text-align:right'>". number_format($row[2], 2, ',', '.') . "</td>";   //Evrakla ödenen
    echo "<td style='text-align:right'>". number_format($row[3], 2, ',', '.') . "</td>";   //K.K.  ödenen
    echo "<td style='text-align:right'>". number_format($row[4], 2, ',', '.') . "</td>";   //Açık  ödenen
	echo "<td style='text-align:left'>". $row[7] . "</td>";   //Fatura Detay Bilgisi
    echo "<td style='text-align:left'>". akinsoftCariAdVer($row[8], 0) . "</td>";   //Cari Ad
    echo "<td style='text-align:left'>". $row[5] . "</td>";   //Fatura No

    //İşlem kolonu

    echo "<td style='vertical-align: middle; text-align:center'>";
    echo "<div style='margin: 0 auto; width: 100px'>";
    


    

    

    echo "</div>";
    echo "</td>";














    echo "</tr>";

    
}
echo "</table>";

pg_close($db);


?>