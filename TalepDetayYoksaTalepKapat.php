<?php

//Talep detaydaki tüm satırlar iptalse ana kaydı kapatır. 

function talepDetayYoksaTalepKapat($gelenTalepSn){

    global $db;
	
	//Detay adet alınıyor.

    $sorgu="SELECT COUNT(sn) FROM talep_detay WHERE talep_sn=$gelenTalepSn;";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Talep Detay Dosyası bulunamadı";
      exit;
    }  

    $kayitAdet=-1;

    while($row = pg_fetch_row($ret)){
        $kayitAdet = $row[0];	   
    }
	
	
	//İPTAL edilmiş satırlar alınıyor. 
	
	$sorgu="SELECT COUNT(sn) FROM talep_detay WHERE talep_sn=$gelenTalepSn AND iptal=TRUE;";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Talep Detay Dosyası bulunamadı";
      exit;
    }  

    $iptalAdet=-1;

    while($row = pg_fetch_row($ret)){
        $iptalAdet = $row[0];	   
    }
	
	
	if(($kayitAdet==$iptalAdet) && $kayitAdet>0){
		
		$sorgu="UPDATE talep SET tamamlandi=TRUE WHERE sn=$gelenTalepSn;";
		$ret = pg_query($db, $sorgu);	
		
	}
	
	
	return true;
	

   
}

?>