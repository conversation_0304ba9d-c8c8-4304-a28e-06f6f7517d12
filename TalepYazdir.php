<?php
require('yetki.php');
$ekranKod = 60;
require('ekranYetkileri.php');

//require('PGConnect.php');
require("AkinsoftIslemleri.php");



//include('blink.html');


require_once('mc_table.php');
//require_once('wordwrap.php');
//require_once('fpdf.php');




function turkce($k){
    return iconv('utf-8','iso-8859-9',$k);
}

function cariAdVer($gelenCariKod){

    global $aktifDB;

    //$gelenSubeAd = isoMetinYap($gelenSubeAd);

    $gelenSubeKod = isoMetinYap($gelenCariKod);

    $sorguCumle = "SELECT TICARI_UNVANI FROM CARI WHERE CARIKODU = '$gelenSubeKod'";

    $donenDeger = $sorguCumle;

    try {
        $dbh = new PDO($aktifDB , 'sysdba', 'masterkey');		        
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            $donenDeger = $row[0];            
        } 


        //VT sıfırlanıyor...
        $dbh = null;
        $ret = null;	

    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;			
        die();
    }

    return substr(utf8MetinYap($donenDeger), 0, 40);

}

function tarihYaz($gelenTarih){

    if($gelenTarih=="") return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));


}

// Sakın hiçbir yere echo koyma!
// Hata veriyor!

//******************************************************** */
//******************************************************** */
//******************************************************** */
//******************************************************** */


class PDF_MC_Table_2 extends PDF_MC_Table
{

    // Page footer
    function Footer(){
        // Position at 1.5 cm from bottom
        $this->SetY(-15);
        // Arial italic 8
        $this->SetFont('Arial','I',8);
        // Page number
        $this->Cell(0,10,'Sayfa '.$this->PageNo().'/{nb}',0,0,'C');
    }


}




$gelenTalepSn = $_POST["talepSn"];


//Deneme için kullanıldı.
//$gelenTalepSn = 2741;



if($gelenTalepSn==""){
	echo "Talep kodu boş geçilemez! <br><br>";
    //Kapat butonu
    echo '<button type="button" onclick="self.close();" style="width:200px;" >Kapat</button> ';
	exit;
}


//goto atla;

//echo $gelenSiparisSn;



//Sorgu başlıyor.
$sorguCumle = "SELECT konu, detay, talep_eden, kullanici, sube, dogrudan, yaratma_zaman
                            FROM talep
                            WHERE sn = $gelenTalepSn;";    
    

//echo $sorguCumle;

$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
} 

$sayac = 0;

while($row = pg_fetch_row($ret)){
    
    $gelenKonu =	$row[0];
    $gelenDetay =	$row[1];
    $gelenTalepEden =	$row[2];
    $gelenKullanici = $row[3];
    $gelenSube =	$row[4];
    $gelenDogrudan =	$row[5];
    $gelenTarih =	tarihYaz($row[6]);    
}
   



//Sayfa hazırlanıyor!
$pdf=new PDF_MC_Table_2();
$pdf->AliasNbPages();
$pdf->AddPage();
$pdf->AddFont('arial_tr', '', 'arial.php'); 
$pdf->SetFont('arial_tr', '', 10);

//$pdf->Text(95, 15,iconv("utf-8", "iso-8859-9","Sepet İçeriği"));


$title = "Talep No: $gelenSiparisSn";
$pdf->SetTitle(turkce($title));
$pdf->SetFont('arial_tr','U', 10);


$pdf->SetY($pdf->GetY()-5);
$pdf->Cell(70);
$pdf->SetFont('arial_tr','U', 10);
$pdf->Cell(0,5,turkce('TEKLİF FORMU'),0,1);

$pdf->SetFont('arial_tr','', 10);

// Birinci Sütun

$pdf->Ln(5);

$pdf->Cell(0,5,turkce('Talep No'), 0, 1);
$pdf->Cell(0,5,turkce('Konu'), 0, 1);
$pdf->Cell(0,5,turkce('Detay'), 0, 1);

//İkinci Sütun

$pdf->SetY($pdf->GetY()-15);
$pdf->Cell(20);
$pdf->Cell(0,5,": ". turkce($gelenTalepSn),0,1);
$pdf->Cell(20);
$pdf->Cell(0,5,": ". turkce($gelenKonu),0,1);



//Üçüncü Sütun

$pdf->SetY($pdf->GetY()-10);
$pdf->Cell(130);
$pdf->Cell(7,5, turkce("Talep Eden"),0,1);
$pdf->Cell(130);
$pdf->Cell(0,5, turkce("Kullanıcı"),0,1);
$pdf->Cell(130);
$pdf->Cell(0,5, turkce("Tarih"),0,1);
$pdf->Cell(130);
$pdf->Cell(0,5, turkce("Şube"),0,1);

//Dördüncü Sütun

$pdf->SetY($pdf->GetY()-20);
$pdf->Cell(150);
$pdf->Cell(7,5,": ". turkce("Bormeg Boru"),0,1);
$pdf->Cell(150);
$pdf->Cell(0,5,": ". turkce($gelenKullanici),0,1);
$pdf->Cell(150);
$pdf->Cell(0,5,": ". turkce($gelenTarih),0,1);
$pdf->Cell(150);
$pdf->Cell(0,5,": ". turkce($gelenSube),0,1);


$pdf->SetY($pdf->GetY()-10);
$pdf->Cell(20);
$pdf->MultiCell(100,5,": ".turkce($gelenDetay), 0, 'L', 0);




// Line break
$pdf->Ln(8);


//echo '<td style="text-align:right">'. date('d-m-Y', strtotime(($row[2]))) . '</td>';
//$aciklama = $gelenAciklama;




//Tablo için yer beildiriliyor.
//$pdf->SetXY(10, 55);


//---------------------------
//Detay Liste
//---------------------------


//Table with 20 rows and 4 columns
$kolonGenislikleri = array(8,110,12,17,25,25);
$pdf->SetWidths($kolonGenislikleri);

$kolonYaslama = array("C","C","C","C","C","C");

$pdf->SetFont('arial_tr','', 10);

$pdf->SetAligns($kolonYaslama);

$pdf->Row(array(turkce("S/N"),
                turkce("Açıklama"),                
                turkce("Miktar"),
                turkce("Birim"),
                turkce("Birim Fiyat"),
                turkce("Toplam Fiyat")
                )
		);

//Buradan Sonra tablo oluşuyor

$kolonYaslama = array("C","L","C","C","C");



$pdf->SetAligns($kolonYaslama);



$sorguCumle = "SELECT aciklama, miktar, birim, termin_zaman 
                        FROM talep_detay                          
                        WHERE talep_sn = $gelenTalepSn AND iptal = false
                        ORDER BY sn;"; 

        //echo $sorguCumle;

		$ret = pg_query($db, $sorguCumle);

	   if(!$ret){
		  echo pg_last_error($db);
		  exit;
	   } 

       //echo "<br><br><br>XXXXX";


$sayac = 0;

		while($row = pg_fetch_row($ret)){			
            $sayac+=1;
			
			$pdf->Row(array($sayac,
						turkce($row[0]), 
						$row[1],
						turkce($row[2]),
						"",
                        "",                        
						));
						
			//$toplam+=$row[8];
			//if($row[7] =='t') $kdv+= ($row[6] * ($kdvOrani/100));
			
		}

$pdf->Ln(4);
$pdf->Cell(130);
$pdf->Cell(7,5, turkce("Toplam:"),0,1);




$pdf->Output();

//En son yer...
pg_close($db); 


?>