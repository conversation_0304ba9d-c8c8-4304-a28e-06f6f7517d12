<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEG Talep <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 10px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 6px;
            font-weight: 600;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .stats-bar {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 13px;
            color: #666;
            margin-top: 5px;
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 8px;
        }

        .talep-card {
            background: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .talep-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 6px;
        }

        .talep-sn {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .talep-tarih {
            font-size: 12px;
            color: #666;
        }

        .talep-konu {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.4;
        }

        .talep-detay {
            font-size: 13px;
            color: #666;
            margin-bottom: 6px;
            line-height: 1.5;
            max-height: 60px;
            overflow: hidden;
            position: relative;
        }

        .talep-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 12px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 3px;
        }

        .info-value {
            font-size: 13px;
            font-weight: 500;
            color: #333;
        }

        .talep-tutar {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 12px;
        }

        .tutar-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .tutar-value {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            display: inline-block;
        }

        .btn-approve {
            background: #28a745;
            color: white;
        }

        .btn-approve:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .btn-details {
            background: #17a2b8;
            color: white;
            margin-bottom: 6px;
        }

        .btn-details:hover {
            background: #138496;
        }

        /* Modal Stilleri */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px;
            border-radius: 8px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            border: none;
            background: none;
            padding: 0;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.8;
        }

        .modal-body {
            padding: 10px;
        }

        .detay-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .detay-table th,
        .detay-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .detay-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state i {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 12px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            .cards-container {
                grid-template-columns: 1fr;
            }
            
            .stats-bar {
                flex-direction: column;
                gap: 8px;
            }
            
            .talep-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 MEG Talep Onay Kartları</h1>
            <p>Onay bekleyen talepleri yönetin ve sürecin akışını kontrol edin</p>
        </div>

        <div class="stats-bar">
            <div class="stat-item">
                <span class="stat-number" id="toplam-talep">-</span>
                <span class="stat-label">Toplam Talep</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="toplam-tutar">-</span>
                <span class="stat-label">Toplam Tutar</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="bugunku-talep">-</span>
                <span class="stat-label">Bugünkü Talep</span>
            </div>
        </div>

        <div id="loading" class="loading">
            <h3>🔄 Talepler yükleniyor...</h3>
        </div>

        <div id="empty-state" class="empty-state" style="display: none;">
            <i>📭</i>
            <h3>Onay bekleyen talep bulunmuyor</h3>
            <p>Tüm talepler işlenmiş durumda.</p>
        </div>

        <div id="cards-container" class="cards-container">
            <!-- Talep kartları buraya dinamik olarak eklenecek -->
        </div>
    </div>

    <!-- Detay Modal -->
    <div id="detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-title">📋 Talep Detayları</span>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Detay içeriği buraya yüklenecek -->
            </div>
        </div>
    </div>

    <script>
        // Global değişkenler
        let talepListesi = [];
        let secilenTalep = null;

        // Sayfa yüklendiğinde talepleri getir
        document.addEventListener('DOMContentLoaded', function() {
            talepleriYukle();
        });

        // Talepleri yükle (Gerçek API)
        async function talepleriYukle() {
            try {
                // Gerçek API çağrısı
                const response = await fetch('/api/onay-bekleyen-talepler');
                const data = await response.json();
                
                if (data.success) {
                    talepListesi = data.data.talepler;
                    // İstatistikleri de güncelle
                    document.getElementById('toplam-talep').textContent = data.data.istatistikler.toplamTalep;
                    document.getElementById('toplam-tutar').textContent = new Intl.NumberFormat('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                    }).format(data.data.istatistikler.toplamTutar);
                    document.getElementById('bugunku-talep').textContent = data.data.istatistikler.bugunkuTalep;
                } else {
                    console.error('API Hatası:', data.message);
                    talepListesi = [];
                }
                
                // Fallback simülasyon verisi (API çalışmazsa)
                if (talepListesi.length === 0) {
                    talepListesi = [
                    {
                        sn: 1001,
                        konu: "Ofis Malzemeleri Tedariki",
                        detay: "Yazıcı kartuşu, A4 kağıt ve kırtasiye malzemeleri satın alınması gerekiyor.",
                        talep_eden: "Ahmet Yılmaz",
                        kullanici: "mehmet",
                        sube: "İdari İşler",
                        yaratma_zaman_formatted: "26-08-2025 14:30",
                        toplam_tutar: 2500.00,
                        toplam_tutar_formatted: "2.500,00 ₺",
                        detaylar: [
                            { aciklama: "HP LaserJet Kartuş", miktar: 3, birim: "Adet" },
                            { aciklama: "A4 Fotokopi Kağıdı", miktar: 10, birim: "Paket" }
                        ]
                    },
                    {
                        sn: 1002,
                        konu: "Araç Bakım ve Onarım",
                        detay: "Şirket aracının periyodik bakımı ve lastik değişimi yapılacak.",
                        talep_eden: "Fatma Demir",
                        kullanici: "mehmet",
                        sube: "Lojistik",
                        yaratma_zaman_formatted: "26-08-2025 10:15",
                        toplam_tutar: 4200.00,
                        toplam_tutar_formatted: "4.200,00 ₺",
                        detaylar: [
                            { aciklama: "Motor Yağı Değişimi", miktar: 1, birim: "Set" },
                            { aciklama: "Lastik Takımı", miktar: 4, birim: "Adet" }
                        ]
                    },
                    {
                        sn: 1003,
                        konu: "Bilgisayar Donanım Yükseltme",
                        detay: "Muhasebe departmanı bilgisayarlarının RAM ve SSD yükseltmesi",
                        talep_eden: "Mehmet Kaya",
                        kullanici: "mehmet", 
                        sube: "Muhasebe",
                        yaratma_zaman_formatted: "25-08-2025 16:45",
                        toplam_tutar: 8750.00,
                        toplam_tutar_formatted: "8.750,00 ₺",
                        detaylar: [
                            { aciklama: "DDR4 16GB RAM", miktar: 6, birim: "Adet" },
                            { aciklama: "1TB SSD", miktar: 3, birim: "Adet" }
                        ]
                    }
                    ];
                }

                document.getElementById('loading').style.display = 'none';
                
                if (talepListesi.length === 0) {
                    document.getElementById('empty-state').style.display = 'block';
                } else {
                    talepleriRender();
                    istatistikleriGuncelle();
                }

            } catch (error) {
                console.error('Talepler yüklenirken hata:', error);
                document.getElementById('loading').innerHTML = '<h3>❌ Talepler yüklenirken hata oluştu</h3>';
                
                // Hata durumunda simülasyon verilerini kullan
                talepListesi = [
                    {
                        sn: 1001,
                        konu: "Ofis Malzemeleri Tedariki",
                        detay: "Yazıcı kartuşu, A4 kağıt ve kırtasiye malzemeleri satın alınması gerekiyor.",
                        talep_eden: "Ahmet Yılmaz",
                        sube: "İdari İşler",
                        yaratma_zaman_formatted: "26-08-2025 14:30",
                        toplam_tutar_formatted: "2.500,00 ₺",
                        detaylar: [
                            { aciklama: "HP LaserJet Kartuş", miktar: 3, birim: "Adet" }
                        ]
                    }
                ];
                talepleriRender();
                istatistikleriGuncelle();
            }
        }

        // Talepleri ekranda göster
        function talepleriRender() {
            const container = document.getElementById('cards-container');
            container.innerHTML = '';

            talepListesi.forEach(talep => {
                const card = talepKartiOlustur(talep);
                container.appendChild(card);
            });
        }

        // Talep kartı oluştur
        function talepKartiOlustur(talep) {
            const card = document.createElement('div');
            card.className = 'talep-card';
            card.innerHTML = `
                <div class="card-header">
                    <span class="talep-sn">T-${talep.sn}</span>
                    <span class="talep-tarih">${talep.yaratma_zaman_formatted}</span>
                </div>
                
                <div class="talep-konu">${talep.konu}</div>
                
                <div class="talep-detay">${talep.detay}</div>
                
                <div class="talep-info">
                    <div class="info-item">
                        <span class="info-label">Talep Eden</span>
                        <span class="info-value">${talep.talep_eden}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Şube</span>
                        <span class="info-value">${talep.sube}</span>
                    </div>
                </div>
                
                <div class="talep-tutar">
                    <div class="tutar-label">Toplam Tutar</div>
                    <div class="tutar-value">${talep.toplam_tutar_formatted}</div>
                </div>
                
                <button class="btn btn-details" onclick="detayGoster(${talep.sn})">
                    📋 Detayları Görüntüle
                </button>
                
                <div class="card-actions">
                    <button class="btn btn-approve" onclick="talepOnayla(${talep.sn})">
                        ✅ Onayla
                    </button>
                    <button class="btn btn-delete" onclick="talepSil(${talep.sn})">
                        🗑️ Sil
                    </button>
                </div>
            `;
            
            return card;
        }

        // Detay göster
        async function detayGoster(talepSn) {
            try {
                // API'den tam talep detayını çek
                const response = await fetch(`/api/talep-detay/${talepSn}`);
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message || 'Talep detayı alınamadı');
                }
                
                const talep = data.data;
                secilenTalep = talep;
                
                const modalBody = document.getElementById('modal-body');
                modalBody.innerHTML = `
                    <div class="talep-info" style="margin-bottom: 12px;">
                        <div class="info-item">
                            <span class="info-label">Talep No</span>
                            <span class="info-value">T-${talep.sn}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Tarih</span>
                            <span class="info-value">${talep.yaratma_zaman_formatted}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Talep Eden</span>
                            <span class="info-value">${talep.talep_eden}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Şube</span>
                            <span class="info-value">${talep.sube}</span>
                        </div>
                    </div>
                    
                    <div class="info-item" style="margin-bottom: 12px;">
                        <span class="info-label">Konu</span>
                        <span class="info-value">${talep.konu}</span>
                    </div>
                    
                    <div class="info-item" style="margin-bottom: 12px;">
                        <span class="info-label">Detay</span>
                        <span class="info-value">${talep.detay}</span>
                    </div>
                    
                    <h4 style="margin-bottom: 6px;">📝 Talep Detayları</h4>
                    ${talep.detaylar && Array.isArray(talep.detaylar) && talep.detaylar.length > 0 ? `
                        <table class="detay-table">
                            <thead>
                                <tr>
                                    <th style="width: 40px;"><input type="checkbox" id="select-all-${talep.sn}" onclick="toggleAllDetails(${talep.sn})" checked> Tümü</th>
                                    <th>Açıklama</th>
                                    <th>Miktar</th>
                                    <th>Birim</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${talep.detaylar.map((detay, index) => `
                                    <tr>
                                        <td style="text-align: center;">
                                            <input type="checkbox" class="detail-checkbox" data-talep-sn="${talep.sn}" data-detay-sn="${detay.detay_sn}" id="detail-${talep.sn}-${detay.detay_sn}" checked onchange="updateSelectAllState(${talep.sn})">
                                        </td>
                                        <td>${detay.aciklama || '-'}</td>
                                        <td>${detay.miktar || '0'}</td>
                                        <td>${detay.birim || '-'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                        <div style="margin-top: 15px; text-align: right;">
                            <small>ℹ️ Onaylamak istediğiniz detayların kutucuklarını işaretleyin</small>
                        </div>
                    ` : '<p style="color: #666; text-align: center; padding: 10px;">Bu talep için detay kaydı bulunmuyor.</p>'}
                    
                    <div class="talep-tutar" style="margin-top: 20px;">
                        <div class="tutar-label">Toplam Tutar</div>
                        <div class="tutar-value">${talep.toplam_tutar_formatted}</div>
                    </div>
                    
                    <div class="card-actions" style="margin-top: 20px;">
                        <button class="btn btn-approve" onclick="talepOnayla(${talep.sn}); closeModal();">
                            ✅ Onayla
                        </button>
                        <button class="btn btn-delete" onclick="talepSil(${talep.sn}); closeModal();">
                            🗑️ Sil
                        </button>
                    </div>
                `;
                
                document.getElementById('detail-modal').style.display = 'block';
                
            } catch (error) {
                console.error('Detay getirme hatası:', error);
                alert('❌ Talep detayları yüklenirken hata oluştu: ' + error.message);
                
                // Fallback: Listeden bulunan talebi kullan
                const talep = talepListesi.find(t => t.sn === talepSn);
                if (talep) {
                    secilenTalep = talep;
                    
                    const modalBody = document.getElementById('modal-body');
                    modalBody.innerHTML = `
                        <div class="talep-info" style="margin-bottom: 12px;">
                            <div class="info-item">
                                <span class="info-label">Talep No</span>
                                <span class="info-value">T-${talep.sn}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Tarih</span>
                                <span class="info-value">${talep.yaratma_zaman_formatted}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Talep Eden</span>
                                <span class="info-value">${talep.talep_eden}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Şube</span>
                                <span class="info-value">${talep.sube}</span>
                            </div>
                        </div>
                        
                        <div class="info-item" style="margin-bottom: 12px;">
                            <span class="info-label">Konu</span>
                            <span class="info-value">${talep.konu}</span>
                        </div>
                        
                        <div class="info-item" style="margin-bottom: 12px;">
                            <span class="info-label">Detay</span>
                            <span class="info-value">${talep.detay}</span>
                        </div>
                        
                        <div class="info-item" style="margin-bottom: 12px;">
                            <span class="info-label">Toplam Tutar</span>
                            <span class="info-value">${talep.toplam_tutar_formatted}</span>
                        </div>
                        
                        <p style="color: #666; text-align: center; padding: 10px;">⚠️ Detaylı bilgiler API'den alınamadı. Temel bilgiler gösteriliyor.</p>
                        
                        <div class="card-actions" style="margin-top: 20px;">
                            <button class="btn btn-approve" onclick="talepOnayla(${talep.sn}); closeModal();">
                                ✅ Onayla
                            </button>
                            <button class="btn btn-delete" onclick="talepSil(${talep.sn}); closeModal();">
                                🗑️ Sil
                            </button>
                        </div>
                    `;
                    
                    document.getElementById('detail-modal').style.display = 'block';
                }
            }
        }

        // Modal kapat
        function closeModal() {
            document.getElementById('detail-modal').style.display = 'none';
            secilenTalep = null;
        }

        // Function to toggle all detail checkboxes
        function toggleAllDetails(talepSn) {
            const selectAllCheckbox = document.getElementById(`select-all-${talepSn}`);
            const detailCheckboxes = document.querySelectorAll(`.detail-checkbox[data-talep-sn="${talepSn}"]`);
            
            detailCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        }

        // Function to update select all checkbox state based on individual checkboxes
        function updateSelectAllState(talepSn) {
            const selectAllCheckbox = document.getElementById(`select-all-${talepSn}`);
            const detailCheckboxes = document.querySelectorAll(`.detail-checkbox[data-talep-sn="${talepSn}"]`);
            const checkedCheckboxes = document.querySelectorAll(`.detail-checkbox[data-talep-sn="${talepSn}"]:checked`);
            
            selectAllCheckbox.checked = detailCheckboxes.length === checkedCheckboxes.length && detailCheckboxes.length > 0;
            selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < detailCheckboxes.length;
        }

        // Update the talepOnayla function to handle selected details only
        async function talepOnayla(talepSn) {
            // Get selected detail checkboxes
            const selectedDetails = [];
            const detailCheckboxes = document.querySelectorAll(`.detail-checkbox[data-talep-sn="${talepSn}"]:checked`);
            
            detailCheckboxes.forEach(checkbox => {
                selectedDetails.push(checkbox.dataset.detaySn);
            });
            
            // If no details are selected, ask for confirmation
            if (selectedDetails.length === 0) {
                if (!confirm(`Hiçbir detay seçilmedi. Talebin tamamını onaylamak istediğinizden emin misiniz?`)) {
                    return;
                }
            } else {
                if (!confirm(`Seçilen ${selectedDetails.length} detayı onaylamak istediğinizden emin misiniz?`)) {
                    return;
                }
            }

            try {
                // Send selected details to the API
                const response = await fetch(`/api/talep-onayla/${talepSn}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        onayKullanici: 'html_kullanici',
                        selectedDetails: selectedDetails
                    })
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message);
                }
                
                console.log(`Talep ${talepSn} onaylandı:`, data.message);
                
                // Listedeki talebi kaldır
                talepListesi = talepListesi.filter(t => t.sn !== talepSn);
                
                // Ekranı güncelle
                talepleriRender();
                istatistikleriGuncelle();
                
                alert(`✅ T-${talepSn} numaralı talep başarıyla onaylandı!`);
                closeModal();
                
            } catch (error) {
                console.error('Onaylama hatası:', error);
                alert('❌ Talep onaylanırken hata oluştu!');
            }
        }

        // Talep sil
        async function talepSil(talepSn) {
            if (!confirm(`T-${talepSn} numaralı talebi silmek istediğinizden emin misiniz?`)) {
                return;
            }

            try {
                // Gerçek API çağrısı
                const response = await fetch(`/api/talep-sil/${talepSn}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ silmeKullanici: 'html_kullanici' })
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message);
                }
                
                console.log(`Talep ${talepSn} silindi:`, data.message);
                
                // Listedeki talebi kaldır
                talepListesi = talepListesi.filter(t => t.sn !== talepSn);
                
                // Ekranı güncelle
                talepleriRender();
                istatistikleriGuncelle();
                
                alert(`🗑️ T-${talepSn} numaralı talep başarıyla silindi!`);
                
            } catch (error) {
                console.error('Silme hatası:', error);
                alert('❌ Talep silinirken hata oluştu!');
            }
        }

        // İstatistikleri güncelle
        function istatistikleriGuncelle() {
            const toplamTalep = talepListesi.length;
            const toplamTutar = talepListesi.reduce((total, talep) => total + talep.toplam_tutar, 0);
            const bugun = new Date().toDateString();
            const bugunkuTalep = talepListesi.filter(talep => 
                new Date(talep.yaratma_zaman_formatted).toDateString() === bugun
            ).length;

            document.getElementById('toplam-talep').textContent = toplamTalep;
            document.getElementById('toplam-tutar').textContent = new Intl.NumberFormat('tr-TR', {
                style: 'currency',
                currency: 'TRY'
            }).format(toplamTutar);
            document.getElementById('bugunku-talep').textContent = bugunkuTalep;

            if (toplamTalep === 0) {
                document.getElementById('empty-state').style.display = 'block';
                document.getElementById('cards-container').style.display = 'none';
            } else {
                document.getElementById('empty-state').style.display = 'none';
                document.getElementById('cards-container').style.display = 'grid';
            }
        }

        // Modal dışına tıklayınca kapat
        window.onclick = function(event) {
            const modal = document.getElementById('detail-modal');
            if (event.target === modal) {
                closeModal();
            }
        }

    </script>
</body>
</html>
