// Database Configuration for MEG Talep Tracking System
// PostgreSQL connection settings (extracted from PGConnect.php)

const dbConfig = {
    host: '127.0.0.1',              // host=127.0.0.1
    port: 5432,                     // port=5432
    database: 'megdb',              // dbname=megdb
    user: 'postgres',               // user=postgres
    password: 'Ata11TURK_1',        // password=Ata11TURK_1
    max: 20,                        // <PERSON><PERSON><PERSON>um bağlantı sayısı
    idleTimeoutMillis: 30000,       // Boşta kalma süresi
    connectionTimeoutMillis: 5000,   // connect_timeout=5 (5 saniye)
    timezone: 'Europe/Istanbul',     // Timezone ayarı
};

module.exports = dbConfig;