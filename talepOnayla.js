#!/usr/bin/env node

/**
 * MEG Talep Onaylama Servisi - MySQL Server (*************)
 * MySQL'deki talep tablosunda islemYapildi = 1 olduğunda trigger ile çağr<PERSON>lan servis
 * Hem MySQL'de hem de PostgreSQL'de (VPN server) aynı onay işlemlerini yapar
 */

const mysql = require('mysql2/promise');
const https = require('https');
const fs = require('fs');
const path = require('path');

// MySQL bağlantı konfigürasyonu (local)
const mysqlConfig = {
    host: 'localhost',
    user: 'mehmet',
    password: 'Mb_07112024',
    database: 'bormeg_msg',
    charset: 'utf8mb4',
    timezone: '+03:00'
};

// PostgreSQL server endpoint (VPN server)
const POSTGRES_SERVER = {
    host: 'YOUR_VPN_SERVER_IP', // Bu IP adresini gerçek VPN server IP'si ile değiştirin
    port: 3080,
    endpoint: '/talep/onay'
};

// Log dosyası
const LOG_FILE = path.join(__dirname, 'talep-onay.log');

/**
 * Log mesajı yaz
 */
function writeLog(message, level = 'INFO') {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    const logMessage = `[${timestamp}] [${level}] ${message}\n`;
    
    console.log(logMessage.trim());
    
    try {
        fs.appendFileSync(LOG_FILE, logMessage);
    } catch (error) {
        console.error('Log yazma hatası:', error);
    }
}

/**
 * PostgreSQL server'a onay isteği gönder
 */
function sendToPostgreSQL(talep_detay_sn) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            talep_detay_sn: talep_detay_sn
        });

        const options = {
            hostname: POSTGRES_SERVER.host,
            port: POSTGRES_SERVER.port,
            path: POSTGRES_SERVER.endpoint,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            },
            timeout: 10000 // 10 saniye timeout
        };

        const req = https.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(responseData);
                    if (res.statusCode === 200 && response.success) {
                        resolve(response);
                    } else {
                        reject(new Error(`PostgreSQL server hatası: ${response.error || 'Bilinmeyen hata'}`));
                    }
                } catch (error) {
                    reject(new Error(`Response parse hatası: ${error.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error(`PostgreSQL bağlantı hatası: ${error.message}`));
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('PostgreSQL isteği zaman aşımına uğradı'));
        });

        req.write(postData);
        req.end();
    });
}

/**
 * MySQL'de onay işlemini yap
 */
async function processApprovalInMySQL(talepSn) {
    let connection;
    
    try {
        writeLog(`MySQL onay işlemi başlıyor: Talep SN=${talepSn}`);
        
        connection = await mysql.createConnection(mysqlConfig);
        
        await connection.beginTransaction();
        
        // 1. İlk olarak talep_detay tablosunda bu talebe ait kayıtları bul
        const detayQuery = `
            SELECT sn as talep_detay_sn 
            FROM talep_detay 
            WHERE talep_sn = ? AND onay != 1
        `;
        
        const [detayRows] = await connection.execute(detayQuery, [talepSn]);
        
        if (detayRows.length === 0) {
            writeLog(`Talep SN=${talepSn} için onaylanacak detay bulunamadı`, 'WARN');
            await connection.rollback();
            return;
        }
        
        writeLog(`Talep SN=${talepSn} için ${detayRows.length} detay kaydı bulundu`);
        
        // 2. Tüm detay kayıtlarını onaylanmış yap
        const updateDetayQuery = `
            UPDATE talep_detay 
            SET onay = 1, 
                onay_zaman = NOW(), 
                onay_kullanici = 'eguler',
                duzeltme_zaman = NOW()
            WHERE talep_sn = ? AND onay != 1
        `;
        
        await connection.execute(updateDetayQuery, [talepSn]);
        
        // 3. Talep tablosunda dogrudan_onaylayan güncelle
        const updateTalepQuery = `
            UPDATE talep 
            SET dogrudan_onaylayan = 'eguler',
                duzeltme_zaman = NOW()
            WHERE sn = ?
        `;
        
        await connection.execute(updateTalepQuery, [talepSn]);
        
        await connection.commit();
        
        writeLog(`MySQL'de onay işlemi tamamlandı: Talep SN=${talepSn}, ${detayRows.length} detay onaylandı`);
        
        // 4. Her detay için PostgreSQL'e istek gönder
        for (const detay of detayRows) {
            try {
                writeLog(`PostgreSQL'e onay isteği gönderiliyor: Detay SN=${detay.talep_detay_sn}`);
                
                const postgresResponse = await sendToPostgreSQL(detay.talep_detay_sn);
                
                writeLog(`PostgreSQL onayı başarılı: Detay SN=${detay.talep_detay_sn}, Response=${JSON.stringify(postgresResponse.data)}`);
                
            } catch (error) {
                writeLog(`PostgreSQL onay hatası: Detay SN=${detay.talep_detay_sn}, Error=${error.message}`, 'ERROR');
                // PostgreSQL hatası MySQL işlemini geri almaz, sadece log'a yazar
            }
        }
        
        writeLog(`Talep onaylama işlemi tamamlandı: Talep SN=${talepSn}`, 'SUCCESS');
        
    } catch (error) {
        writeLog(`MySQL onay hatası: Talep SN=${talepSn}, Error=${error.message}`, 'ERROR');
        
        if (connection) {
            try {
                await connection.rollback();
            } catch (rollbackError) {
                writeLog(`Rollback hatası: ${rollbackError.message}`, 'ERROR');
            }
        }
        
        throw error;
        
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

/**
 * Ana fonksiyon - Trigger tarafından çağrılır
 */
async function onaylamalariIsle(talepSn) {
    try {
        writeLog(`=== TALEP ONAY İŞLEMİ BAŞLADI ===`);
        writeLog(`Parametre - Talep SN: ${talepSn}`);
        
        if (!talepSn || isNaN(talepSn)) {
            throw new Error('Geçersiz talep SN parametresi');
        }
        
        await processApprovalInMySQL(parseInt(talepSn));
        
        writeLog(`=== TALEP ONAY İŞLEMİ TAMAMLANDI ===`);
        
    } catch (error) {
        writeLog(`=== TALEP ONAY İŞLEMİ HATASI ===`, 'ERROR');
        writeLog(`Hata: ${error.message}`, 'ERROR');
        writeLog(`Stack: ${error.stack}`, 'ERROR');
        
        // Hata durumunda bile process'i sonlandırmaz, sadece log'lar
        return false;
    }
    
    return true;
}

/**
 * Command line'dan çağrıldığında
 */
if (require.main === module) {
    const talepSn = process.argv[2];
    
    if (!talepSn) {
        console.error('Kullanım: node talepOnayla.js <talep_sn>');
        console.error('Örnek: node talepOnayla.js 12345');
        process.exit(1);
    }
    
    onaylamalariIsle(talepSn)
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch((error) => {
            console.error('Beklenmeyen hata:', error);
            process.exit(1);
        });
}

module.exports = {
    onaylamalariIsle,
    processApprovalInMySQL,
    sendToPostgreSQL
};