<?php
require('yetki.php');
$ekranKod = 56;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">





<script src="sorttable.js"></script>



<style style="text/css">




/** Modal pencere için */ 
    /* The Modal (background) */
    .modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}




/* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }


.sortable th{ 
        position:sticky;
        /*position: -webkit-sticky;*/
        top: 0;
        background: green;/*#727272;*/
        color: white; /*#c5c5c5;*/
        z-index: 0;

		/*padding:7px; border: #4e95f4 1px solid;*/
		/*#4e95f4 orjinal hali*/ 
}
















    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }
</style>

<?php
require('siteBaslik.html');
require('menuKaynak.php');
require('blink.html');
?>


<script type="text/javascript">




function temizle(sender){
	
	sender.style.display = "none";
	
	document.getElementById("borclanacakCariKod").value="";
	document.getElementById("borclanacakCariAd").innerHTML = "";	
	
	sender.style.display = "";	
	
}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(satirAdet){	

	var artisDeger;
	var deger;
	var formX;
	var miktarX;	
	
	
	if(!ConfirmIslem("Fiyat Listesi Kaydedilecektir!")) return false;	
	
	//console.log("Tabloyu Oku:" + satirAdet);
	
	//satirAdet -=1;
	
	
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];	
	var satirlar = "";	
	var tumTablo = "";
	var sayac = 1;
	var hucre = "";
	var rakam = "";
	var atlamaYap;
	var eskiRakam;
	
	for (var i = 0, row; row = table.rows[i]; i++) {			
	   
	   satirlar = "";
	   sayac = i+1;  	  
		
		
		hucre = "fiyat" + sayac;
		
		rakam = roundNumber(document.getElementById(hucre).value, 2).toString();
		
		//console.log(rakam);
		
		eskiRakam = row.cells[4].innerText;
		
		if(rakam!=eskiRakam)
		{
			satirlar = row.cells[0].innerText + ";"+ rakam;
			tumTablo = tumTablo + satirlar + "|";		
			
		} 

		
	   
	   
	   
	   //alert(hucre);
	   
	   //console.log("Debug Objects: " + hucre);
	   
	   //rakam = document.getElementById(hucre).value.toString();
	   
	   //console.log("Debug Objects: " + rakam);
	   
	   //satirlar = satirlar.slice(0, -1);
	   
	   //satirlar += rakam;
	   
	   
	   	   
	   
	   //if(sayac<satirAdet)sayac +=1;
	}
	
	if(tumTablo!="")
	{
		document.getElementById('tabloBilgileri').value = tumTablo.slice(0, -1);
		return true;	
		
	}else
	{
		alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");
		return false;
	}		
	
	
}

function veriKontrol(sender){

    
    if(document.getElementById("isteyenSube").value=="---"){
        alert("Şube girilmelidir!\nİşlem iptal edilecek.");
        return false;
    }   

    
    if(document.getElementById("konu").value==""){
        alert("Konu mutlaka girilmelidir!\nİşlem iptal edilecek.");
		return false;
    }  

    if(document.getElementById("talepEden").value==""){
        alert("Talep Eden mutlaka girilmelidir!\nİşlem iptal edilecek.");
		return false;
    }  
	

    if(!ConfirmIslem("YENİ KAYIT YAPIYORSANIZ MUTLAKA İHTİYAÇ DUYULAN, SATIN ALINACAK\nPARÇALARIN ADI, ADET VE MİKTARINI BİR SONRAKİ EKRANDAN DA GİRMELİSİNİZ!")) return false;



    //document. getElementById("gonderTus").style.display = "none";
    sender.style.display = "none";
    return true;

    

    


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}


function modalGoster() {  
    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();
	
    xhttp.onreadystatechange = function() {
		if (this.readyState == 4 && this.status == 200) {
		  document.getElementById("icerik").innerHTML = this.responseText;
		  document.body.style.cursor  = 'default';
		  try{
		  document.getElementById("myInputHepsi2").focus();
		}catch{
		  //
		}
		}
    };

    
	document.getElementById('baslik').innerHTML ="Kullanıcılar";
	xhttp.open("POST", "KullaniciVer.php", true);
	xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xhttp.send("veriTransferAd=veriTransferCari&sadeceBormeg=1");
    

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}



function veriTransfer(gelenCariKod, gelenCariAd, gelenBlKodu, gelenAdres){ //Cari liste seçili ise bu rutin çalışacak
    document.getElementById("cariKod").value = gelenCariKod;
    document.getElementById("cariAd").innerHTML = gelenCariAd ;
    //document.getElementById("firma").value = gelenCariAd;
    //document.getElementById("adres").value = gelenAdres;
    //document.getElementById("cariBakiye").value = roundNumber(gelenBakiye,2);
    //document.getElementById("tutar").value = roundNumber(gelenBakiye,2);

    
    modalKapat();     
}

function veriTransfer2(gelenCariKod, gelenCariAd, gelenBlKodu, gelenAdres){ //Cari liste seçili ise bu rutin çalışacak
    document.getElementById("borclanacakCariKod").value = gelenCariKod;
    document.getElementById("borclanacakCariAd").innerHTML = gelenCariAd ;
    //document.getElementById("firma").value = gelenCariAd;
    //document.getElementById("adres").value = gelenAdres;
    //document.getElementById("cariBakiye").value = roundNumber(gelenBakiye,2);
    //document.getElementById("tutar").value = roundNumber(gelenBakiye,2);

    
    modalKapat();     
}


function modalGoster1(gelenKod) {  
    document.body.style.cursor  = 'wait';  
    var xhttp = new XMLHttpRequest();
	
    xhttp.onreadystatechange = function() {
		if (this.readyState == 4 && this.status == 200) {
		  document.getElementById("icerik").innerHTML = this.responseText;
		  document.body.style.cursor  = 'default';
		  try{
		  document.getElementById("myInputHepsi2").focus();
		}catch{
		  //
		}
		}
    };

    
	document.getElementById('baslik').innerHTML ="Cari Listesi";
	xhttp.open("POST", "CariVerAkinsoftdan.php", true);
	xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	
	if(gelenKod==1){
		xhttp.send("hedefTransfer=veriTransfer");
		
	}else if(gelenKod==2){
		xhttp.send("hedefTransfer=veriTransfer2");
		
	}
	
    

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    //console.log(gelenKod);
    //document.body.style.cursor  = 'default';
}


function veriTransferCari(gelenKullanici, gelenCariKod, gelenCariAd){	
	
    document.getElementById("talepEden").value = gelenKullanici;
	
	//document.getElementById("cariKod").value = gelenCariKod;
    //document.getElementById("kullaniciAd").innerHTML = gelenCariAd;
    //document.getElementById("cariAd2").value = gelenCariAd;    
    
    modalKapat();     
}


function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
    document.body.style.cursor  = 'default';    
}



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
    document.body.style.cursor  = 'default';
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}



//Tüm kolonlarda arama

function searchTableColumns(gelenTablo, gelenAlan,gelenHedefAlan) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById(gelenAlan);
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      var bulunanKayitAdet = 0; //Kayıt sayısı için
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
            bulunanKayitAdet++;
        } else {
            tr[i].style.display = "none";
        }
      }   

      //Kayıt adedi yazılıyor.

      if(bulunanKayitAdet > 0){
          document.getElementById(gelenHedefAlan).innerHTML = bulunanKayitAdet + " Adet kayıt listelendi"; 
      }else{
          document.getElementById(gelenHedefAlan).innerHTML = ""; 
      }





}
























function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body>





<?php

function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) return 'selected';
    
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
      
   while($row = pg_fetch_row($ret))
   {
       array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function isChecked($gorevSn, $gelenKisi){

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);

    $donenDeger = "";

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $gelenKisi) $donenDeger = "checked";        
    }

    return $donenDeger;
    
}

function kilitliMi($gelenMod){
    if($gelenMod == "Görüntüleme") return ' onclick="return false;" onkeydown="return false;" '; else return '';
}

function textKilitliMi($gelenMod){
    if($gelenMod == "Görüntüleme" ) return ' readonly '; else return '';
}

function subeListesiVer(){
    
    global $aktifSirketBLKodu;
    global $aktifSirketDB;

    $donenListe=[];

    $sorguCumle = "SELECT SUBE_ADI FROM SUBE WHERE BLSRKODU=$aktifSirketBLKodu AND AKTIF = 1 ORDER BY SUBE_ADI;";

    try {	
        $dbh = new \PDO($aktifSirketDB , 'sysdba', 'masterkey');
        
        $ret = $dbh->query($sorguCumle);

        foreach($ret as $row) {
            array_push($donenListe, utf8MetinYap($row[0]));
        }
        $dbh = null;
        $ret = null;
        
        }
        catch (PDOException $e) {
            print "Hata!: " . $e->getMessage() . "<br/>";
            $dbh = null;
            $ret = null;
            die();
        }

    return $donenListe;
}


//************************************************************************************************ */
//************************************************************************************************ */
//************************************************************************************************ */
//************************************************************************************************ */
//************************************************************************************************ */
//************************************************************************************************ */


//Bağlantı Yapılıyor... 
//require_once("PGConnect.php");
require("AkinsoftIslemleri.php");

//Gelen Değerler alınıyor...

$gelenSn = $_POST['talepSn'];
$gelenMod = $_POST['kayitMod'];
$gelenTalepEden = $_POST['talepEden'];
$gelenSube = $_POST['isteyenSube'];
$gelenKonu = $_POST['konu'];
$gelenYaz = $_POST['yaz'];
$gelenCariKod = $_POST['cariKod'];
$gelenBorclanacakCariKod = $_POST['borclanacakCariKod'];

$gelenTalepOlusturan = $_POST['talepOlusturan'];

if($gelenSn=="") $gelenSn = 0;
if($gelenMod=="") $gelenMod = "Yeni Kayıt";

if($gelenTalepOlusturan=="") $gelenTalepOlusturan = $kullanici;




//-------------------------------
if(isset($_POST['acilTalep'])) { // checkbox seçilmişse "on" değeri gönderiliyor
    //echo 'Onayladınız!';
    $gelenAcil = 'TRUE';
} else { // seçilmemişse bu değer sayfaya hiç gönderilmiyor
    //echo 'Onaylamadınız.';
    $gelenAcil = 'FALSE';
}


//echo "**" . $gelenTalepOlusturan;




//if($gelenTalepEden=="") $gelenTalepEden = $kullanici;







echo '<h1 style="text-align:center;">Satınalma Talep ('.$gelenMod.')</h1>';


//Burada kayıt işlemi yapılıyor.
if($gelenMod=="Düzenleme" && $gelenSn <> 0 && $gelenYaz==""){

    $sorguCumle = "UPDATE talep SET sube = '$gelenSube', 
									konu ='$gelenKonu',
									cari_kod = '$gelenCariKod',
									borclanacak_cari_kod = '$gelenBorclanacakCariKod',
									talep_eden = '$gelenTalepEden'
									
                    WHERE sn = $gelenSn;";

    $ret = pg_query($db, $sorguCumle);

    if($ret){
        echo '<script type="text/javascript"> alert("Kayıt işlemi Tamamlandı.");window.close(); </script>';
    }else {
        echo "Hata 123: " .  pg_last_error($db);
        exit;        
    }


}



/*


$gelenTip = $_POST['tip'];
$gelenKriterTum = $_POST['kriterTum'];

$takas = explode(" - ", $gelenKullanici);

$gelenKullaniciKod = trim($takas[1]);

*/


//Burada varolan kayıt alınıyor.

if($gelenMod=="Düzenleme" || $gelenMod == "Görüntüleme"){

    $sorguCumle = "SELECT konu, detay, sube, talep_eden, cari_kod
                   FROM talep WHERE sn = $gelenSn;";

    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    while($row = pg_fetch_row($ret)){

        //if($gelenTarih2>$gelenTarih1)$oncekiTarih = date('Y-m-d', strtotime('-1 day', strtotime($gelenTarih2)));

        $gelenKonu =  $row[0];
        $gelenDetay = $row[1];  
        $gelenSube =  $row[2];  
        $gelenTalepEden = $row[3];
		$gelenCariKod  = $row[4];		
        
    }


    //Kullanıcıları bulacak.


}


//if($gelenGorevTarih=='') $gelenGorevTarih = date("Y-m-d"); //"2021-12-31";

//$sayac = 1;
//print_r(subeListesiVer());
if($gelenMod=="Yeni Kayıt"){
    echo '<form action="talepDetayGiris.php" method="post">';    
}else{
    echo '<form action="#" method="post">';
}

echo "<b>Talebi Oluşturan: </b>";
echo '<input type="text" id="goreviVeren" name="goreviVeren" readonly value="'.$gelenTalepOlusturan.'"><br><br>';
echo "<br>";
echo "<b>Konu: </b><br>";
echo '<input type="text" id="konu" '.textKilitliMi($gelenMod).' style="font-family: Arial;font-size: 12pt; width:100%;height:100%"  name="konu" value="'.$gelenKonu.'"><br><br><br>';

/*
echo "<b>Detay: </b>";
echo '<textarea type="text" size="150" '.textKilitliMi($gelenMod).'  name="detay" maxlength="1000000" style="font-family: Arial;font-size: 12pt; width:100%;height:100%">';
echo $gelenDetay.'</textarea>';
echo "<br>";
echo "<br>";
*/

echo "<b>Talep Eden: </b>";
echo '<input type="text" style="background-color : #fff333;" onclick="modalGoster();" readonly '.textKilitliMi($gelenMod).' id="talepEden" name="talepEden" value="'.$gelenTalepEden.'"><br><br><br>';

echo "<font style='color:red;'>*** Ön Ödeme yapılacaksa Cari Kodu seçilmelidir!</font><br><br>";
echo "<b>Cari Kod: </b><br>";
//echo '<input type="text" style="background-color : #fff333;" onclick="modalGoster();" readonly '.textKilitliMi($gelenMod).' id="cariKod" name="cariKod" value="'.$gelenCariKod.'"><br><br><br>';
echo '<input type="text" style="background-color : #fff333;" id="cariKod" onclick="modalGoster1(1);" readonly name="cariKod" value="'.$gelenCariKod.'"><label id="cariAd">&nbsp;'.$gelenCariAd.'</label><br><br>';



echo "<font style='color:red;'>*** Talep tutarı bir cariye borç yazılacaksa Cari Kodu seçilmelidir!</font><br><br>";
echo "<b>Borçlanacak Cari Kod: </b><br>";
//echo '<input type="text" style="background-color : #fff333;" onclick="modalGoster();" readonly '.textKilitliMi($gelenMod).' id="cariKod" name="cariKod" value="'.$gelenCariKod.'"><br><br><br>';
echo '<input type="text" style="background-color : #fff333;" id="borclanacakCariKod" onclick="modalGoster1(2);" readonly name="borclanacakCariKod" value="'.$gelenBorclanacakCariKod.'"><label id="borclanacakCariAd">&nbsp;'.$gelenBorclanacakCariAd.'</label>';

echo '<button type="button" onclick="temizle(this);"><img src="Delete_Icon.png" height="15" width="15" title="Temizle" valign="middle"/></button><br><br>';


echo "<b>İsteyen Şube: </b>";
echo '<select name="isteyenSube" id="isteyenSube">';

$sirketGormeYetkili = ["fcan", "ssever", "milbuga"];  

if($gelenSube=="") echo '<option value="---" selected >---</option>';


    foreach (subeListesiVer() as $key => $sube) {        
        echo '<option value="' . $sube . '"' . selectedKoy($gelenSube, $sube); 

        //Sevgili Fahriya hanımın isteği üzerine bu eklentiyi yaptık. 2022 06 14 (Edip GÜLER'i de koymayın dedi!)

        if(!in_array($kullanici, $sirketGormeYetkili) && $sube =="BORMEG BORU MAKİNA"){

            echo " disabled ";

        }
        
        echo  '>'.$sube.'</option>';	           
    }

echo "</select>";


echo "<br><br><br>";
echo '<input type="checkbox" id="acilTalep" name="acilTalep" title="Talep ACİL ?"><font style="color:red;font-weight: bold;" >Talep ACİL ?</font></input><br>';




echo "<br><br>";
//echo '<input type="text" id="kullanici" name="kullanici" readonly value="'.$kullanici.'"><br><br><br>';
echo '<input type="hidden" id="kayitMod" name="kayitMod" value="'.$gelenMod.'">';
echo '<input type="hidden" id="talepSn" name="talepSn" value="'.$gelenSn.'">';


echo "<br>";
//echo "<center>";

if($gelenMod != "Görüntüleme"){
	require('reloadKontrol1.php');
    echo '<input type="submit" id="gonderTus" style="width:200px;" value="Kaydet" onclick="return veriKontrol(this);">';
}

//echo "</center>";
/*
if($yeniYetki == 't'){
    //echo '<input type="hidden" id="islemMod" name="islemMod" value="Kaydet">';
	//echo '<input type="submit" class= "tusBosluk" value="Kaydet" formaction="isTanimDuzenle.php">';	
}
*/
echo '</form>';
//echo "<br>";




/*
if($gelenMod == "Görüntüleme"){
    echo "<center>";
    //echo '<br><br><br>';
    echo '<form action="GorevListesi.php" method="post">';
    //echo '<input type="hidden" id="tarih1" name="tarih1" value="'.$gelenTarih1.'">';
    //echo '<input type="hidden" id="tarih2" name="tarih2" value="'.$gelenTarih2.'">';
    echo '<input type="submit" value="Görev Listesine Dön" style="width:200px;">';
    echo '</form>';
    echo "</center>";
    
}

*/




//echo '<input type="button" value="Click Me" style="float: right;">';

/*
if($gelenKullaniciKod=="")
{
	pg_close($db);
	exit;
}

*/





//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';




pg_close($db);

//echo "<hr><br><br><b>Dip Toplam :$toplam TL </b><hr>";   

//echo "<br>";
//echo "***Tüm geçmiş siparişleriniz listelenmiştir.";

?>

</body>
</html>
