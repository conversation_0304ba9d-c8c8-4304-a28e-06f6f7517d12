#!/bin/bash

# MEG VPN Kurulum Script'i
# ************* sunucusunda çalıştırın

echo "=== MEG VPN KURULUM BAŞLIYOR ==="

# OpenVPN kurulumu
echo "OpenVPN kuruluyor..."
apt update
apt install -y openvpn

# VPN config klasörü oluştur
mkdir -p /etc/openvpn/client
cd /etc/openvpn/client

# VPN config dosyasını oluştur
cat > megtar-vpn.conf << 'EOF'
dev tun
persist-tun
persist-key
data-ciphers AES-256-GCM:AES-128-GCM:CHACHA20-POLY1305:AES-256-CBC
data-ciphers-fallback AES-256-CBC
auth SHA256
tls-client
client
resolv-retry infinite
remote *********** 1196 udp4
nobind
verify-x509-name "EvOfisTLSCA-Cert" name
auth-user-pass /etc/openvpn/client/auth.txt
pkcs12 /etc/openvpn/client/Megtar-pfSense-UDP4-1196-masik.p12
tls-auth /etc/openvpn/client/Megtar-pfSense-UDP4-1196-masik-tls.key 1
remote-cert-tls server
explicit-exit-notify
log /var/log/openvpn-client.log
daemon
EOF

# Kullanıcı bilgilerini kaydet
cat > auth.txt << 'EOF'
masik
G=3r89RZj2_
EOF

chmod 600 auth.txt

echo "=== VPN CONFIG DOSYASI OLUŞTURULDU ==="
echo "Şimdi sertifika dosyalarını kopyalamanız gerekiyor:"
echo "1. Megtar-pfSense-UDP4-1196-masik.p12"
echo "2. Megtar-pfSense-UDP4-1196-masik-tls.key"
echo ""
echo "Bu dosyaları /etc/openvpn/client/ klasörüne kopyalayın"
echo ""
echo "Sonra VPN'i başlatmak için:"
echo "systemctl start openvpn-client@megtar-vpn"
echo "systemctl enable openvpn-client@megtar-vpn"