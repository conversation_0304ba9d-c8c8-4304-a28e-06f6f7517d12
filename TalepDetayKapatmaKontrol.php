<?php
require('yetki.php');
$ekranKod = 137;
require('ekranYetkileri.php');
?>

<!DOCTYPE html>
<html lang="tr">
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> 
<link rel="icon">
<link rel="icon" href="MegtarLogo.png">
<link rel="stylesheet" href="menuStyle.css" type="text/css">


<script src="sorttable.js"></script>
<script src="jquery-3.6.0.js"></script>



<style style="text/css">
    /* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.sortable{
		width:100%; 
		border-collapse:collapse; 
	}

	.sortable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.sortable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .sortable tr:hover {
          background-color: #ffff99;  
    }





    .inline {
        display: inline;
        margin-left :10px 
    }
    #form2 {        
        position: relative;
        bottom: 29px;
        left: 25px;
    }
    #form3 {        
        position: relative;
        bottom: 58px;
        left: 70px;
        
    }
    .tusBosluk {
		margin-left :30px 
		}
  	.hoverTable{
		width:100%; 
		border-collapse:collapse; 
	}
	.hoverTable td{ 
		padding:7px; border: #4e95f4 1px solid;
		/*#4e95f4 orjinal hali*/ 
	}
	/* Define the default color for all the table rows */
	.hoverTable tr{
		background: white;
		/*   #b8d1f3*/
		
	}
	/* Define the hover highlight color for the table row */
    .hoverTable tr:hover {
          background-color: #ffff99;  
    }


/** Modal pencere için */ 
    /* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 200px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-header {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #5cb85c;
  color: white;
}



</style>

<?php
include('siteBaslik.html');
include('menuKaynak.php');
include('blink.html');
?>


<script type="text/javascript">

function proceed (gelenSayfa) {
    var form = document.createElement('form');
    form.setAttribute('method', 'post');    
    form.setAttribute('action', gelenSayfa);
    form.style.display = 'hidden';
    document.body.appendChild(form)
    form.submit();
}

function silmeOnay(){
    if (confirm("Kayıt ve ilişkili tüm yüklenmiş belgeler Silinecektir!\nİşleme devam edilsin mi?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }

}




function roundNumber(num, scale) {
  if(!("" + num).includes("e")) {
    return +(Math.round(num + "e+" + scale)  + "e-" + scale);
  } else {
    var arr = ("" + num).split("e");
    var sig = ""
    if(+arr[1] + scale > 0) {
      sig = "+";
    }
    return +(Math.round(+arr[0] + "e" + sig + (+arr[1] + scale)) + "e-" + scale);
  }
}


function tabloyuOku(gelenMod){ //1: Önceki durum 2: sonraki durum	

var artisDeger;
var deger;
var formX;
var miktarX;	

var table = document.getElementById("tableMain2");

//console.log(gelenMod);
//console.log("ilkOkuma");


if(gelenMod==2){
    if(table.rows.length==1){
        alert ("Listelenen kayıt bulunamadı!");
        return false;	
    } 

    if(!ConfirmIslem("Değişiklikler kaydedilecektir!")) return false;	
}



var satirlar = "";	
var tumTablo = "";

var hucre = "";

var detayNo = "";
//var onay = "";

var nakit = ""; 
var bankadan = "";
var evrak = "";
var kredi = "";
var acik = "";

var oncekiTablo = document.getElementById('tabloBilgileriIlk').value;

for (var i = 1, row; row = table.rows[i]; i++) {			

    satirlar = "";

    detayNo = row.cells[0].innerText;
    
    hucre = "nakit" + i;        
    nakit = roundNumber(document.getElementById(hucre).value, 2).toString();    

    hucre = "bankadan" + i;        
    bankadan = roundNumber(document.getElementById(hucre).value, 2).toString();

    hucre = "evrak" + i;        
    evrak = roundNumber(document.getElementById(hucre).value, 2).toString(); 

    hucre = "kredi" + i;        
    kredi = roundNumber(document.getElementById(hucre).value, 2).toString();   
    
    hucre = "acik" + i;        
    acik = roundNumber(document.getElementById(hucre).value, 2).toString();   
    



/*
    if(rakam == '0' && onay == 1 && gelenMod==2 ){
        alert("SIFIR miktar ile ONAY verilemez!\nİşlem iptal edildi.");        
        return false;
    }
    */

    satirlar = detayNo + ";" + nakit + ";" + bankadan + ";" + evrak + ";" + kredi + ";" + acik;

    tumTablo = tumTablo + satirlar + "|";
    
}

tumTablo = tumTablo.slice(0, -1);


//console.log(tumTablo);

if(gelenMod==1){
    document.getElementById('tabloBilgileriIlk').value = tumTablo;
    //console.log("İlk Hali: " + tumTablo);
    return true;
} 
    

if(tumTablo!="" && tumTablo!=oncekiTablo) 
{
    //Burada kıyaslama yaparak gönderilecek
    var degisenTablo = "";
    var ilk = oncekiTablo.split("|"); 
    var sonraki = tumTablo.split("|");

    for (var i = 0; i < ilk.length; i++) {
        if(ilk[i]!=sonraki[i]) degisenTablo = degisenTablo + sonraki[i] + "|";             
    }

    degisenTablo = degisenTablo.slice(0, -1);



    document.getElementById('tabloBilgileri').value = degisenTablo; 
    //console.log("İlk Hali: " + oncekiTablo);
    //console.log("Son Hali: " + degisenTablo);
    
    return true;	
    
}else
{
    alert("Listede bir değişiklik yapılmamış!\nİşlem iptal edildi.");        
    return false;
}		


}


function searchTableColumns(gelenTablo) {
      // Declare variables 
      var input, filter, table, tr, i, j, column_length, count_td;
      column_length = document.getElementById(gelenTablo).rows[0].cells.length;
      input = document.getElementById("myInputHepsi");
      filter = input.value.toLocaleUpperCase("tr");
      table = document.getElementById(gelenTablo);
      tr = table.getElementsByTagName("tr");
      for (i = 1; i < tr.length; i++) { // except first(heading) row
        count_td = 0;
        for(j = 0; j < column_length-1; j++){ // except first column İptal ettim
            td = tr[i].getElementsByTagName("td")[j];
            /* ADD columns here that you want you to filter to be used on */
            if (td) {
              if ( td.innerHTML.toLocaleUpperCase("tr").indexOf(filter) > -1)  {            
                count_td++;
              }
            }
        }
        if(count_td > 0){
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
      }      
}


function kolonArama(gelenKolon, kolonNo) {
  var input, filter, table, tr, td, i, txtValue;
  input = document.getElementById(gelenKolon);//"myInput"
  filter = input.value.toLocaleUpperCase("tr");
  table = document.getElementById("tableMain");
  tr = table.getElementsByTagName("tr");
  for (i = 0; i < tr.length; i++) {
    td = tr[i].getElementsByTagName("td")[kolonNo];
    if (td) {
      txtValue = td.textContent || td.innerText;
      if (txtValue.toLocaleUpperCase("tr").indexOf(filter) > -1) {
        tr[i].style.display = "";
      } else {
        tr[i].style.display = "none";
      }
    }       
  }
}



function veriKontrolTalep(){ //Doğrudan olmayan Talep ödemelerinin giriş kontrolü için
    
    //İlk okuma yapılıyor.
    if(tabloyuOku(2)){

        //console.log("İlk Hali: " + document.getElementById('tabloBilgileriIlk').value);
        //console.log("Son Hali: " + document.getElementById('tabloBilgileri').value);

        var tabloBilgileriX = document.getElementById('tabloBilgileri').value;
        var tabloBilgileriY = document.getElementById('tabloBilgileriIlk').value;

        //Değerleri veritabanına yaz.

        $.ajax({
                method: "POST",
                url: "TalepOdemeDegisikligiKaydet.php",
                async: false,
                data: { tabloBilgileri: tabloBilgileriX,
                        tabloBilgileriIlk: tabloBilgileriY
                    }        
                })
                .done(function( response ) { 

                    //console.log("Dönen değer: ");
                    //console.log("---");
                    //console.log(response);
                    response = response.trim();
                    //console.log(response);

                    

                    if(response.indexOf("Ok") !== -1){    
                        //console.log("OK---"); 
                        location.reload(); 
                        donenDeger = true;          

                        //return true;
                                    
                    } else{
                        alert(response);
                        //console.log("Hata");           
                        donenDeger = false;
                        //return false;                
                    }             
                    
                });


                if(donenDeger == true){
                    modalKapat();
                }








    }else{
        //alert ("Değerler Okunamadı");
        return false;
    }



    


}



function veriKontrol(){

    var hataVar = false;

    var nakit = 0;
    var banka = 0;
    var evrak = 0;
    var kK = 0;
    var acik = 0;

    nakit = document.getElementById("nakit").value; 
    banka = document.getElementById("banka").value; 
    evrak = document.getElementById("evrak").value; 
    kK = document.getElementById("kk").value; 
    acik = document.getElementById("acik").value; 

    if((nakit+banka+evrak+kK+acik)==0){
        alert("En az bir harcama bilgisi girilmelidir!\nİşlem iptal edilecek.");
        return false;
    }
    	


}

function pasifYap2() {
	pasifYap("zamYap");
	pasifYap("kayitTusu"); 
	
	//Girişler de pasif oluyor.
	var table = document.getElementById("tableMain").getElementsByTagName('tbody')[1];
	
	for (var i = 0, row; row = table.rows[i]; i++) 
	{
		sayac = i+1;
		hucre = "fiyat" + sayac;
		pasifYap(hucre);		
	}


	
}


function pasifYap(gelenID) {
  document.getElementById(gelenID).disabled = true;
}



function ConfirmIslem(gelenMesaj){
	
	console.log(gelenMesaj);

    if (confirm(gelenMesaj + "\nOnaylıyor musunuz?")){
          return true;
    }
    else {
       //alert('İşlem iptal edildi.');
       return false;
    }
}




function modalGoster(gelenKod) {
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
      document.getElementById("icerik").innerHTML = this.responseText;
    }
    };
    xhttp.open("POST", "talepDetayVer.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("talepSn="+gelenKod);

    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "block";
    console.log(gelenKod);
}

function modalKapat() {
    // Get the modal
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}



// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    var modal = document.getElementById("myModal");
  if (event.target == modal) {
    modal.style.display = "none";
  }
}

// Get the <span> element that closes the modal
//var span = document.getElementsByClassName("close")[0];


// When the user clicks on <span> (x), close the modal
function spanKapat() {
    //console.log("gelenMesaj");
    var modal = document.getElementById("myModal");
    modal.style.display = "none";
}




function modalGosterTalep(gelenKod) {

    console.log(gelenKod);

    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200) {
    document.getElementById("icerik").innerHTML = this.responseText;

    //İlk okuma yapılıyor.
    tabloyuOku(1);
    }
    };
    xhttp.open("POST", "TalepOdemeleriListesiVer.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("talepSn="+gelenKod);

    // Get the modal

    //Başlık yazar
    document.getElementById('baslik').innerHTML ="Talep Harcama Bilgileri";

    var modal = document.getElementById("myModal");
    modal.style.display = "block";




    //console.log(gelenKod);
}



function secilmisSatirlarTutari(){

    var table = document.getElementById("tableMain");
    var toplam = 0.0;
    var satir = "";

    for (var i = 1, row; row = table.rows[i]; i++){

        satir = row.cells[0].innerText.concat(row.cells[1].innerText);
        nesne = "secim".concat(satir);

        if(document.getElementById(nesne).checked){

            var nakitOdenen = parseFloat(row.cells[8].innerText.replace(".", "").replace(",","."));
            var bankadanOdenen = parseFloat(row.cells[9].innerText.replace(".", "").replace(",","."));
            var evraklaOdenen = parseFloat(row.cells[10].innerText.replace(".", "").replace(",","."));
            var kkOdenen = parseFloat(row.cells[11].innerText.replace(".", "").replace(",","."));
            var acikOdenen = parseFloat(row.cells[12].innerText.replace(".", "").replace(",","."));
    
            toplam+= nakitOdenen + bankadanOdenen + evraklaOdenen + kkOdenen + acikOdenen;
        }
    }

    return toplam;
}







function akinsoftaTopluYaz(sender){

    var toplamTutar =  secilmisSatirlarTutari();
    
    if(toplamTutar == 0){
        alert("Hiçbir seçim yapılmamış!\nİşlem iptal ediliyor!");
        return false;
    }


    if(!ConfirmIslem("Tüm listelenenler tek bir fatura içerisinde kapatılacaktır!\nToplam Tutar: " + toplamTutar + " TL\nEmin misiniz?")) return false;	


    sender.style.display = "none";

    //Kum saati
    document.body.style.cursor  = 'wait';


    var table = document.getElementById("tableMain");
    var gidenDeger = "";
    var satir="";
    var faturaNo = "";
    var faturaOncekiNo = "";
    var islemedeHataVar = false;
	var subeler = [];


    for (var i = 1, row; row = table.rows[i]; i++){

        satir = row.cells[0].innerText.concat(row.cells[1].innerText);
        var nesne = "secim".concat(satir);
		
		
		
        //console.log(nesne);

        if(document.getElementById(nesne).checked){
			
			subeler.push(row.cells[7].innerText);

            //Satiri gizler.
            row.style.display = "none";

            faturaNo = row.cells[15].innerText;

            islemedeHataVar = false;

            if(faturaOncekiNo!=""){ //Demek ki bir sonrakine geçmiş!

                if(faturaNo != faturaOncekiNo){

                    islemedeHataVar = true;
                    alert("Farklı fatura numaraları bulunuyor!\nKontrol edin!");
                    document.body.style.cursor  = 'default';
                    return false;

                }

            }else{
                faturaOncekiNo = faturaNo;              

            }


            if(!islemedeHataVar){

                gidenDeger = gidenDeger + row.cells[0].innerText + ";" + row.cells[1].innerText + ";" + row.cells[2].innerText + "#"; 
            }



        }    //İşaretlenmiş olanlar...        
        
    }

    //alert (gidenDeger);
    //return false;


    //İkinci kontrol döngüsü. Seçilmiş olanlardan başka işaretlenmemiş olan var mı kontrolü?
    //Aslında gerek yok bence buna. Artık neden yapmışsak?
    //İptal ediyorum!
    /*


    var secilmisler=[];
    var secilmemisler=[];

    for (var i = 1, row; row = table.rows[i]; i++){

        var satir = row.cells[0].innerText;
        var nesne = "secim".concat(satir);

        faturaNo = row.cells[15].innerText;

        if(document.getElementById(nesne).checked){

            secilmisler.push(faturaNo); 
            console.log("Seçilmiş: " + faturaNo );

        }else{

            secilmemisler.push(faturaNo);
            console.log("Seçilmemiş: " + faturaNo );
        }
    }

    


    for (var i = 0; i < secilmemisler.length; i++) {

        console.log(secilmemisler[i]);
        
        if(secilmisler.includes(secilmemisler[i])){
            alert("Eksik işaretlenmiş numaralar var!");
            document.body.style.cursor  = 'default';
            sender.style.display = "block";
            return false;
        }
    
    }



    alert("KOntrolü geçti!");
    document.body.style.cursor  = 'default';

    return false;
    */


	if(allAreEqual(subeler)!=true){
		alert("Toplu aktarımda tüm şubaler aynı olmalıdır!");
		return false;
	}



    if(gidenDeger==""){

        alert("Mutlaka seçim yapılmış olmalıdır!");
        return false;        

    }else{
        gidenDeger = gidenDeger.slice(0, -1);
    }


    //Deneme için.
    //console.log(gidenDeger);
    //return false;


    $.ajax({
        method: "POST",
        url: "AkinsoftaTalepDetaydanTopluCariHareketYaz.php",
        async: false,
        data: { gelenDeger: gidenDeger
            }        
    })
        .done(function( response ) { 
            
            response = response.trim();        

            

            if(response == "Okkk"){    
                //console.log("OK---"); 
                donenDeger = true; 
                //Normal Saat
                document.body.style.cursor  = 'default';  
                alert("Akınsoft kayıtları başarı ile oluşturuldu.");
                
                //Ekran tazeleme
                window.location.reload();       

                //return true;
                            
            } else{
                //Normal Saat
                document.body.style.cursor  = 'default';
                alert(response);
                console.log(response);
                //console.log("Hata");           
                donenDeger = false;
                //return false;                
            }   
                    
            
        });


    //return donenDeger;
    return false;

}



function akinsoftaYaz(sender, gelenTalepNo, gelenTalepDetayNo, gelenOdemelerToplami, gelenFaturaNo){

    if(!ConfirmIslem(gelenFaturaNo + ' Fatura numaralı tüm harcamalar toplamı: '+gelenOdemelerToplami+' TL dir. Akınsoft\'a FATURA ve CARİ HAREKET olarak aktarılacaktır?\nTUTARI MUTLAKA KONTROL EDİN!!')) return false;

    

    if(gelenFaturaNo==""){
        alert("Fatura No mutlaka girilmelidir!\nİşleme devam edilemez!");
        return false;
    }

    sender.style.display = "none";

    //console.log(gelenSifre);

    //Yazma işlemine başlıyor.
    //donenDeger = false;

    //Kum saati
    document.body.style.cursor  = 'wait';

    $.ajax({
        method: "POST",
        url: "AkinsoftaTalepDetaydanCariHareketYaz.php",
        async: false,
        data: { talepNo: gelenTalepNo,
                talepDetayNo: gelenTalepDetayNo,
                faturaNo: gelenFaturaNo,
                odemelerToplami: gelenOdemelerToplami
            }        
    })
        .done(function( response ) { 

            //console.log("Dönen değer: ");
            //console.log("---");
            //console.log(response);
            response = response.trim();
            //console.log(response);

            

            if(response == "Okkk"){    
                //console.log("OK---"); 
                donenDeger = true; 
                //Normal Saat
                document.body.style.cursor  = 'default';  
                alert("Akınsoft kayıtları başarı ile oluşturuldu.");
                //Ekran tazeleme
                //document.getElementById('EkranTazeleme20210914').submit();

                //window.location.reload();       

                //return true;
                            
            } else{
                //Normal Saat
                document.body.style.cursor  = 'default';
                alert(response);
                console.log(response);
                //console.log("Hata");           
                donenDeger = false;
                //return false;                
            }   
                    
            
        });


    return donenDeger;
    //return false;

}

function sayfaYuklendi(){

    //return false;
    //alert("Sayfa yüklendi!");

    const degerler = [];
    var sayac = 0;

    var table = document.getElementById("tableMain");

    for (var i = 1, row; row = table.rows[i]; i++){

        degerler.push(row.cells[15].innerText);
        sayac++;
        
    }

    //console.log(degerler);
    //console.log(allAreEqual(degerler));

    var satir="";
    var nesne = "";

    if(allAreEqual(degerler)==true || sayac<2){
        //console.log(sayac);
        //console.log(allAreEqual(degerler));

        //document.getElementById("topluAktarTus").style.display = "none";

        //Seçim kolonu içindekiler gizlenecek. Zira ihtiyaç yok!

        for (var i = 1, row; row = table.rows[i]; i++){

            //row.cells[14].firstChild.style.display = "none";

        }

        return;
    }else if(allAreEqual(degerler)==false && sayac==2){

        document.getElementById("topluAktarTus").style.display = "none";
        return;

    }

    //Gizleme yapılıyor.

    satir="";
    nesne = "";

    


    for (var i = 1, row; row = table.rows[i]; i++){

        satir = row.cells[2].innerText;
        nesne = "akinsoftGonderTus".concat(satir);
        document.getElementById(nesne).style.display = "none";

    }




}


function allAreEqual(array) {
  const result = array.every(element => {
    if (element === array[0]) {
      return true;
    }
  });

  return result;
}












function stopRKey(evt) {
  var evt = (evt) ? evt : ((event) ? event : null);
  var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
  if (((evt.keyCode == 13) && (node.type=="text"))
	|| ((evt.keyCode == 13) && (node.type=="date"))
	|| ((evt.keyCode == 13) && (node.type=="time"))
	|| ((evt.keyCode == 13) && (node.type=="number"))
	


    )  {return false;} else return true;
}

document.onkeypress = stopRKey;

</script>

</head>

<body onload="sayfaYuklendi();">



<?php

echo '<br><h1 style="text-align:center;">Satınalma Detay Kapatma</h1>';


function selectedKoy($gelen, $kriter){
    if ($gelen == $kriter) {return 'selected';}
    
}


function isDate($value){
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function ayarlardanCek($gelenParametre){	
	global $db;	
	$sorgu="SELECT deger FROM ayarlar WHERE parametre='$gelenParametre';";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   
   return $donenDeger;  
	
}

function tumCarilerMi($gelenKriter, $cari){
	// AND sepet.cari_kod ='" . $gelenCariKod
	if($gelenKriter !=1) return 'AND sepet.cari_kod =\''.$cari.'\''; else return "";	
	
}

function tarihSaatYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y H:i:s", strtotime(($gelenTarih)));


}
function tarihYaz($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function tarihFormatla($gelenTarih){

    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("YmdHis", strtotime(($gelenTarih)));
}

function gizliMi($gelengizli){
    if($gelengizli=='t') return "Gizli";else return "Normal"; 
}


function gorevlendirilenleriVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$liste . $row[0].', ';	   
   }  

   return substr($liste, 0, -2);
}


function gorevlendirilenleriArrayVer($gorevSn){

    global $db;
    
    $liste = [];

	$sorgu="SELECT kullanici_kod FROM gorev_alan WHERE gorev_sn=$gorevSn;";	
	$ret = pg_query($db, $sorgu);
   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   $donenDeger=0;   
   while($row = pg_fetch_row($ret))
   {
	   array_push($liste, $row[0]);	   
   }  

   return $liste;
}

function goreviGorebilir($gorevSn, $kullanici, $gorevVeren){

    //Görevi veren dahil ediliyor.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function sadeceGorevli($gorevSn, $kullanici){

    //Sadece görevi alanlar.

    $gorevliler = gorevlendirilenleriArrayVer($gorevSn);
    //array_push($gorevliler, $gorevVeren);

    $donenDeger = false;

    foreach ($gorevliler as $gorevAlan) {
        if($gorevAlan == $kullanici) $donenDeger = true;        
    }

    return $donenDeger;

}

function kullanicidanBilgiVer($bilgiTur, $gelenKullaniciKod){

    global $db;

    $sorgu="SELECT $bilgiTur FROM kullanici WHERE kullanici_kod = '$gelenKullaniciKod';";	

	$ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Kullanıcı bulunamadı";
      exit;
   }  

   $donenDeger="Hata";

   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }   

   return $donenDeger;  
}



function acikTalepDetayVarMi($talepSn){

    global $db;

    $sorgu="SELECT COUNT(*) FROM talep_detay WHERE talep_sn = $talepSn
                                                    AND iptal=false
                                                    AND tamamlandi=false;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Talep detay bulunamadı";
      exit;
   }  
   
   $donenDeger=true;
   
   while($row = pg_fetch_row($ret))
   {
	  if($row[0]>0) $donenDeger=true; else $donenDeger=false;	   
   }  

   return $donenDeger;
}

function iptalOlmayantalepDetayVarMi($talepSn){

    global $db;

    $sorgu="SELECT COUNT(*) FROM talep_detay WHERE talep_sn = $talepSn
                                               AND iptal=false;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Talep detay bulunamadı";
      exit;
   }  
   
   $donenDeger=true;
   
   while($row = pg_fetch_row($ret))
   {
	  if($row[0]>0) $donenDeger=true; else $donenDeger=false;	   
   }  

   return $donenDeger;
}




function gorevVereniVer($gorevSn){

    global $db;
    
    $liste = '';

	$sorgu="SELECT gorev_veren FROM gorevlendirme WHERE sn=$gorevSn;";

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Ayar parametresi bulunamadı";
      exit;
   }   
   
   while($row = pg_fetch_row($ret))
   {
	   $liste =$row[0];	   
   }  

   return $liste;
}

function paslamaKoduVer($kullaniciKod, $hedefSayfa, $postAlan, $postVeri){

    global $db;
 
    $token = bin2hex(random_bytes(64));
 
    $sorgu = "INSERT INTO pasla(pas_kod, kullanici_kod, paslama_adres, post_alan, post_veri) 
              VALUES('$token', '$kullaniciKod', '$hedefSayfa', '$postAlan', '$postVeri');";
 
    $ret = pg_query($db, $sorgu);
 
    if(!$ret){
       echo pg_last_error($db);
    echo "Ayar parametresi bulunamadı";
    exit;
    } 
    
 
   return "https://portal.bormegplastik.com/meg/pasla.php?kod=" . $token;
 }



function trueFalse($gelen, $mesaj){
    if($gelen=='t') return $mesaj;else return "-";
}


function talepdenGorevSnVer($gelenTalepSn){

    global $db;  

    $sorgu="SELECT sn FROM gorevlendirme WHERE talep_sn=$gelenTalepSn;";

    $donenDeger=0;

    $ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Görev bulunamadı!";
      exit;
   }   
    
   while($row = pg_fetch_row($ret))
   {
	   $donenDeger = $row[0];	   
   }  

   return $donenDeger;

}

function hikayesiVarMi($gelenGorevSn){

    global $db;   
    

	$sorgu="SELECT COUNT(*) FROM gorevlendirme_hikaye WHERE gorev_sn=$gelenGorevSn;";	

	$ret = pg_query($db, $sorgu);

   if(!$ret){
      echo pg_last_error($db);
	  echo "Görev hikayeleri sayılamadı!";
      exit;
   }   
    
   while($row = pg_fetch_row($ret))
   {
	   if($row[0]>0) $donenDeger=true;else $donenDeger=false;	   
   }  

   return $donenDeger;
}


function talepIleIlgililerListesi($talepSn){

    global $db; 

    $liste=[];

    $sorgu="SELECT DISTINCT(islem_kullanici) FROM talep_detay WHERE talep_sn=$talepSn
                                                                AND islem_kullanici<>'';";

    $ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Detaya ulaşılamadı!";
        exit;
    }   
    
   while($row = pg_fetch_row($ret)){
	   array_push($liste,$row[0]);
   }   
   
   return $liste;
}


function akinsoftBaglantiKod(){
    
	$anaSorgu = "http://192.168.0.29:3056/getdata.html?";

	$baglan ="command=wlogin&username=sysdba&password=abe6db4c9f5484fae8d79f2e868a673c&devCode=*********&devPass=53C61&timeOut=60";

	$sifreliBaglan = base64_encode($baglan);

	$AkinsoftCevap = trim(file_get_contents($anaSorgu . $sifreliBaglan));

	$baglantiKod = substr(base64_decode($AkinsoftCevap), 2);
	
	return $baglantiKod;	
}




function faturaNoyaGoreToplamTutar($gelenFaturaNo, $gelenTalepSn ){

    global $db; 

    $donenDeger = 0;

    $sorgu="SELECT SUM(nakit_odenen+bankadan_odenen+evrakla_odenen+kk_odenen+acik_odenen) 
                            FROM talep_hareket 
                            WHERE fatura_no = '$gelenFaturaNo'
                            AND talep_sn = $gelenTalepSn;";

    $ret = pg_query($db, $sorgu);

    if(!$ret){
        echo pg_last_error($db);
        echo "Detaya ulaşılamadı!";
        exit;
    }   
    
   while($row = pg_fetch_row($ret)){
    $donenDeger = $row[0];
   }   
   
   return $donenDeger;

}









//*************************************************************** */
//*************************************************************** */
//*************************************************************** */
//*************************************************************** */
//*************************************************************** */
//*************************************************************** */





//Bağlantı Yapılıyor... 
//require_once("PGConnect.php");
require("mesajlasma.php");
require("logYaz.php");
include('blink.html');




$gelenTalepSn = $_POST['talepSn'];
$gelenIslemTip = $_POST['islemTip'];
$gelenKriter = $_POST['kriter'];

$gelenTalepDetaySn = $_POST['talepDetaySn'];
$gelenFaturaNo = $_POST['faturaNo'];

require("saat1.inc");


/*
$gelenNakit = $_POST['nakit'];
$gelenBanka = $_POST['banka'];
$gelenEvrak = $_POST['evrak'];
$gelenKk = $_POST['kk'];
$gelenAcik = $_POST['acik'];
$gelenTamamla= $_POST['tamamla'];


if($gelenNakit=="") $gelenNakit=0;
if($gelenBanka=="") $gelenBanka=0;
if($gelenEvrak=="") $gelenEvrak=0;
if($gelenKk=="") $gelenKk=0;
if($gelenAcik=="") $gelenAcik=0;
*/
//echo $gelenIslemTip;
//echo $gelenKriter;




if($gelenKriter=="")$gelenKriter = "1";


//----------------------------------------------------------------------
//Burada işlem başlıyor.
//Onaylama işlemi.

if($gelenIslemTip=='DetayHareketKapat'){

    //echo "<br><br><br><br>xxx";

    $sorguCumle = "UPDATE talep_hareket SET kapali_mi = TRUE
                           WHERE fatura_no = '$gelenFaturaNo'
                             AND talep_sn = $gelenTalepSn;";

    //Sorgu çalıştır.
    $ret = pg_query($db, $sorguCumle);

    if(!$ret){
        echo pg_last_error($db);
        exit;
    } 

    if(pg_affected_rows($ret)>0){
        
        $mesaj = $kullanici . " Kullanıcısı, ".$gelenTalepSn." numaralı talep ile ilgili $gelenFaturaNo faturalı alışları kapattı.";
    
        logYaz($mesaj);
    
        epostaGonder("<EMAIL>", ['<EMAIL>', '<EMAIL>'], "Talep Faturası kapatıldı!", $mesaj, 1);
    }




}



echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi" onkeyup="searchTableColumns(\'tableMain\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">';

echo "<br><br>";




$sorguCumle = "SELECT  talep_hareket.sn,                                    
                    talep_hareket.nakit_odenen + talep_hareket.bankadan_odenen + talep_hareket.evrakla_odenen + talep_hareket.kk_odenen + talep_hareket.acik_odenen,
                    talep_hareket.fatura_no,
					yaratma_zaman
					
					FROM talep_hareket                             
					ORDER BY talep_hareket;";



$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
} 


//echo "<br><br>";    


echo '<table class= "sortable" valign="middle" id="tableMain">';
echo "<tr title ='Kolon başlıkları ile sıralama yapılabilir.'>";
echo '<th style="text-align:center;cursor:pointer;">Sayaç</th>';
echo '<th style="text-align:center;cursor:pointer;">Kaynak Hareket S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Kaynak Hareket Toplam</th>';
echo '<th style="text-align:center;cursor:pointer;">Hedef S/N</th>';
echo '<th style="text-align:center;cursor:pointer;">Hedef Toplam</th>';
echo '<th style="text-align:center;cursor:pointer;">Hedef Fatura No</th>';
echo '<th style="text-align:center;cursor:pointer;">Kaynak Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">Hedef Zaman</th>';
echo '<th style="text-align:center;cursor:pointer;">Gün Fark</th>';
echo "</tr>";


//$akinsoftBaglantiSifresi = "-";

$sayac = 0;

while($row = pg_fetch_row($ret)){
	
	
	
	$sorguCumle2 = "SELECT sn, 
						   talep_hareket.nakit_odenen + talep_hareket.bankadan_odenen + talep_hareket.evrakla_odenen + talep_hareket.kk_odenen + talep_hareket.acik_odenen,
						   fatura_no,
						   yaratma_zaman						   
						FROM talep_hareket
						WHERE ABS ( $row[1] - (talep_hareket.nakit_odenen + talep_hareket.bankadan_odenen + talep_hareket.evrakla_odenen + talep_hareket.kk_odenen + talep_hareket.acik_odenen))<1
						AND sn<>$row[0] 
						AND fatura_no='$row[2]';";
	
	$ret2 = pg_query($db, $sorguCumle2);
	
	while($row2 = pg_fetch_row($ret2)){
		$sayac +=1;
		
		echo "<tr>";
		echo "<td style='text-align:center'>". $sayac . "</td>"; //Kıyaslanan       
		echo "<td style='text-align:center'>". $row[0] . "</td>"; //Kıyaslanan       
		echo "<td style='text-align:center'>". number_format($row[1], 2, ',', '.')  . "</td>"; //Kıyaslanan Tutar
		echo "<td style='text-align:center'>". $row2[0] . "</td>"; //Hedef Sn   
		echo "<td style='text-align:center'>". number_format($row2[1], 2, ',', '.')  . "</td>"; //Hedef toplamlar		
		
		echo "<td style='text-align:center'>$row2[2]</td>"; //Hedef Fatura No   
		echo "<td style='text-align:center'>$row[3]</td>"; //Kaynak Zaman   
		echo "<td style='text-align:center'>$row2[3]</td>"; //Hedef Zaman
		
		
		$first_date = strtotime($row[3]);
		$second_date = strtotime($row2[3]);
		
		$datediff = $first_date - $second_date;
		
		



		
		echo "<td style='text-align:center'>".round($datediff / (60 * 60 * 24))."</td>"; //Gün Farkı   
		
		echo "</tr>";
	
		
	}
	
	
	
	
}

echo "</table>";






require("saat2.inc");


atla: //Bu kısım atlama için

//En alta eklenmesi gereken kod

//Modal
echo '<div id="myModal" class="modal">

<!-- Modal content -->
<div class="modal-content">
  <div class="modal-header" >
    <span class="close" onclick="spanKapat()">&times;</span>
    <h2 id="baslik"></h2>
  </div>
  <div class="modal-body" id="icerik">    

  </div>
  <div class="modal-footer">
    <h3></h3>
  </div>
</div>

</div>';



echo '<input type="hidden" id="tabloBilgileri" name="tabloBilgileri" value="">'; 
echo '<input type="hidden" id="tabloBilgileriIlk" name="tabloBilgileriIlk" value="">'; 




pg_close($db);


?>

</body>
</html>
