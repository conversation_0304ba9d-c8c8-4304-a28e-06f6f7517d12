<?php
require('yetki.php');
$ekranKod = 66;
require('ekranYetkileri.php');

?>
<style style="text/css">
    table#tableMain2 thead { display:block; }
    table#tableMain2 tbody { height:200px; overflow-y:scroll; display:block; }

    table#tableMain3 thead { display:block; }
    table#tableMain3 tbody { height:200px; overflow-y:scroll; display:block; }
</style>

<?php

function islemTurVer($islemTuru){

    $donenDeger = "-";

    switch ($islemTuru){
        case "1":
            $donenDeger = "Devir";
            break;
        case "2":
            $donenDeger = "Evrak";
            break;
        case "3":
            $donenDeger = "Nakit";
            break;
        case "4":
            $donenDeger = "Dekont";
            break;
        case "5":
            $donenDeger = "Kredi Kartı";
            break;
        case "6":
            $donenDeger = "Pos";
            break;
        case "7":
            $donenDeger = "Çek";
            break;
        case "8":
            $donenDeger = "Senet";
            break;
        default:
            $donenDeger = "Belirsiz";
    }

    return $donenDeger;

}

function tarihYaz($gelenTarih){
    if(date("Y", strtotime(($gelenTarih)))<1990) return "-";
        else return date("d-m-Y", strtotime(($gelenTarih)));
}

function oncekiGirilmisOdemeEmirleriToplami($gelenTalepNo){
    
    global $db;

    $sorgu = "SELECT SUM(yeni_tutar) 
					FROM odeme_emri 
					WHERE ilgili_talep_kod = $gelenTalepNo
					AND onay = TRUE;";

    $ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Tabloya ulaşılamadı!";
      exit();
   }

   $donenDeger = -1;

   while($row = pg_fetch_row($ret)){
    $donenDeger = $row[0];
   }
   
   
   //İade edilmiş tutarın d buraya eklenmesi gerekir.
   //Sevgili Fahriye hanımın derin ve emir telakki ettiğimiz isteğiyle ekleme yapıldı. 2024 01 05
   
   $sorgu = "SELECT SUM(iade_miktar * birim_fiyat * (1 + (kdv_oran/100))     ) 
					FROM alis_iade_detay 
					WHERE talep_bilgisi LIKE '$gelenTalepNo%';";

    $ret = pg_query($db, $sorgu);

    if(!$ret){
      echo pg_last_error($db);
	  echo "Tabloya ulaşılamadı!";
      exit();
   }

   

   while($row = pg_fetch_row($ret)){
    $donenDeger += $row[0];
   }
   
   
   
   
   
   
   
   
   
   
   
   
   


   return $donenDeger;

}







//-----------------------------------------------------
//-----------------------------------------------------
//-----------------------------------------------------
//-----------------------------------------------------

//Bağlantı Yapılıyor... 
//require("PGConnect.php");
require("AkinsoftIslemleri.php");



$gelenCariKod = $_POST['cariKod'];

//Öncelikle Akınsofta tablolarından detayında talep no olan faturaların detaydaki talep numaraları 
//alınacak

$sorguCumle = "SELECT FATURAHR.EKBILGI_3 
                FROM FATURA 
                JOIN FATURAHR ON FATURA.BLKODU = FATURAHR.BLFTKODU  
                WHERE FATURA.CARIKODU='$gelenCariKod'
                AND CAST(CAST(TARIHI AS DATE) AS TIMESTAMP)>='29.03.2021'
				AND FATURA.FATURA_DURUMU IN (5) /*Yurtiçi alış*/
				AND FATURAHR.EKBILGI_3 NOT LIKE '%MAAS%'
                AND FATURAHR.EKBILGI_3<>''
                AND FATURA.SILINDI = 0;";




$inDegerleri = "";

try {
    
    $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
    
    $ret = $dbh->query($sorguCumle);

    foreach($ret as $row) {

        if($row[0]=="") continue;

        $dilimler = explode("-", $row[0]);

        if(substr($row[0], 0, 3)=="HZM"){ //demek ki Hizmet. Pas geçiliyor.
            $inDegerleri =  $inDegerleri . "0,";

        }else{

            //if(strpos($row[0], "-") !== false){
                $inDegerleri = $inDegerleri . $dilimler[0] . ",";
            //} else{
            //    $inDegerleri = $inDegerleri . $row[0] . ",";
            //}
            
        }



    }
    $dbh = null;
    $ret = null;
    
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;
        $ret = null;
        die();
    }

$inDegerleri = substr($inDegerleri, 0, -1);

if($inDegerleri == "") $inDegerleri = "-1";

//echo "***$inDegerleri";
//exit;




$sorguCumle ="SELECT sn, konu, talep_eden, acik_odenen  
                FROM talep 
                WHERE sn IN($inDegerleri) ORDER BY sn DESC;";


$ret = pg_query($db, $sorguCumle);

if(!$ret){
    echo pg_last_error($db);
    exit;
}

$count = pg_num_rows($ret); //Sorgu sonucunun satır sayısını veriyor.

if($count<1){
    echo '<p style="color:red">Hareket kaydı yok!</p>';
    pg_close($db);
    exit;
}
echo "<br>";
//echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi2" autofocus onkeyup="searchTableColumns(\'tableMain2\', \'myInputHepsi2\' )" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">';
echo 'Tüm Kolonlarda Ara: <input type="text" id="myInputHepsi2" onkeyup="searchTableColumns(\'tableMain2\',\'myInputHepsi2\',\'bulunanKayitAdet\')" placeholder="Satıra göre ara.." title="Kelimeyi yaz.">&nbsp;&nbsp;<label id="bulunanKayitAdet"></label>';
echo "<br><br>";

//echo "xxxxxxx";

echo '<table class= "sortable" valign="middle" id="tableMain2">';
//echo "<table >";
echo "<tr>";
echo '<th style="text-align:center;cursor:pointer;">Talep Kod</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Konu</th>';
echo '<th style="text-align:center;cursor:pointer;">Talep Eden</th>';
echo '<th style="text-align:center;cursor:pointer;">Açık<br>Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Ödenmiş<br>Tutar</th>';
echo '<th style="text-align:center;cursor:pointer;">Bakiye</th>';
echo "</tr>";

$sumAcik = 0;
$sumOdenmis = 0;
$sumBakiye = 0;
$sayac = 0;


    
    while($row = pg_fetch_row($ret)){

        $sallamaDegisken = oncekiGirilmisOdemeEmirleriToplami($row[0]) ;
        $sallamaBakiye =  $row[3]  - $sallamaDegisken;
		$sallamaAd = str_replace('"', '\"', $row[1]);

        if($sallamaBakiye<=0) continue;

        echo "<tr class='item' onclick='veriTransfer2(\"$row[0]\", \"$sallamaAd\", \"$sallamaBakiye\" );'>";
        echo "<td style='text-align:center'>". $row[0] . "</td>";   //Cari Kod
        echo "<td style='text-align:left'>". $row[1] . "</td>";   //Cari Ad
        echo "<td style='text-align:left'>". $row[2] . "</td>";   //CAri Bakiye
        echo "<td style='text-align:right'>". number_format($row[3] , 2, ',', '.') . "</td>";   //Cari Bakiye
        echo "<td style='text-align:right'>". number_format($sallamaDegisken, 2, ',', '.') . "</td>";   //Toplam
        echo "<td style='text-align:right'>". number_format($sallamaBakiye, 2, ',', '.') . "</td>";   //Bakiye
        echo "</tr>";   
        
        $sumAcik+=$row[3];
        $sumOdenmis+=$sallamaDegisken;
        $sumBakiye+=$sallamaBakiye;

        $sayac+=1;
            
    }

    if($sayac>0){
        echo '<b>';
        echo "<tr>";
        echo "<td COLSPAN='3' style='text-align:center'> TOPLAMLAR</td>";
        echo "<td style='text-align:right'>". number_format($sumAcik , 2, ',', '.') . "</td>";   //Cari Bakiye
        echo "<td style='text-align:right'>". number_format($sumOdenmis, 2, ',', '.') . "</td>";   //Toplam
        echo "<td style='text-align:right'>". number_format($sumBakiye, 2, ',', '.') . "</td>";   //Bakiye            
        echo "</tr>";
        echo '</b>';
    }



        
        
echo "</table>";   

//****************************************************** */
//---------- İkinci tablo
//****************************************************** */
//****************************************************** */
//****************************************************** */


echo "<br><hr><br>";

echo '<table class= "sortable" valign="middle" id="tableMain3">';
//echo "<table >";
echo "<tr>";
echo '<th style="text-align:center;cursor:pointer;">İşlem<br>Tarihi</th>';
echo '<th style="text-align:center;cursor:pointer;">İşlem<br>Türü</th>';
echo '<th style="text-align:center;cursor:pointer;">Ödenen</th>';
echo '<th style="text-align:center;cursor:pointer;">Ödenen<br>(OE)</th>';
echo '<th style="text-align:center;cursor:pointer;">Evrak<br>No</th>';
echo "</tr>";



$sorguCumle = "SELECT TARIHI, ISLEM_TURU, KPB_BTUT, EVRAK_NO FROM CARIHR JOIN CARI ON CARI.BLKODU = CARIHR.BLCRKODU WHERE CARI.CARIKODU = '$gelenCariKod';";

try {	
    $dbh = new \PDO($aktifDB , 'sysdba', 'masterkey');
    
    $ret = $dbh->query($sorguCumle);

    $oeToplam = 0;


    foreach($ret as $row) {
        if($row[2]==0) continue;

        echo "<tr class='item'>";
        echo "<td style='text-align:center'>". tarihYaz($row[0]) . "</td>";   //Cari Kod
        echo "<td style='text-align:left'>". islemTurVer($row[1]) . "</td>";   //İşlem türü

        if(substr($row[3], 0, 2)=="OE"){
            echo "<td></td>";
            echo "<td style='text-align:right'>". number_format($row[2], 2, ',', '.') . "</td>";   //CAri Bakiye
            $oeToplam +=$row[2];
        }else{            
            echo "<td style='text-align:right'>". number_format($row[2], 2, ',', '.') . "</td>";   //CAri Bakiye
            echo "<td></td>";
        }        
        
        echo "<td style='text-align:left'>". $row[3] . "</td>";   //CAri Bakiye
        echo "</tr>";
        
    }
    $dbh = null;
    $ret = null;
    
    }
    catch (PDOException $e) {
        print "Hata!: " . $e->getMessage() . "<br/>";
        $dbh = null;
        $ret = null;
        die();
    }


    echo '<b>';
    echo "<tr>";
    echo "<td COLSPAN='3' style='text-align:center'>TOPLAMLAR</td>";
    echo "<td style='text-align:right'>". number_format($oeToplam , 2, ',', '.') . "</td>";   //Cari Bakiye
    echo "<td></td>";
    echo "</tr>";
    echo '</b>';

    echo "<tr>";
    echo "<td COLSPAN='3' style='text-align:center'>BORÇ BAKİYE</td>";
    echo "<td style='text-align:right'>". number_format($sumBakiye - $oeToplam , 2, ',', '.') . "</td>";   //Cari Bakiye
    echo "<td></td>";
    echo "</tr>";


//number_format($sumBakiye, 2, ',', '.')

echo "</table>";






//echo "<script type='text/javascript'> document.body.style.cursor = 'default';</script>";
echo '<script type="text/javascript">
    document.body.style.cursor = "default";
    </script>';

    
pg_close($db);


?>